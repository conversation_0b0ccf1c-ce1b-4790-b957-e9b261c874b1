﻿// 现场交接管理类
class PMAssociateManager {
    constructor() {
        this.initializeEventListeners();
        this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.modal = null;
        this.associateData = [];
        this.curData=null;
        this.historyData=null;
        this.bShowHistory=false;    // 控制历史履历信息的显示与隐藏
        this.analysisData = [];     // 获取搜索的分析数据
        this.chartType = 'bar';     // 图表显示类型

        // 控制业务选项的显示
        // const projectContainer = document.getElementById('projectSelect');
        // if (projectContainer && this.userInfo?.level === 20) {
        //     projectContainer.style.display = 'inline-block';
        // }
    }
    
    // 初始化所有事件监听器
    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeTableRows();     // 初始化 交接登录 表格行
            this.loadSearchOptions();       // 加载搜索选项数据
            this.initModal();               // 初始化模态框
            //this.initECharts();             // 测试使用，初始化图表显示
            this.initializeShiftTableRows();     // 初始化 交接登录 表格行
        });

        // 右键事件监听 
        document.getElementById('assSearchTable').addEventListener('contextmenu', function(e) { 
            e.preventDefault(); // 阻止默认菜单 
            const menu = document.getElementById('contextMenu'); 
            menu.style.display = 'block'; 
            menu.style.left = e.pageX + 'px'; 
            menu.style.top = e.pageY + 'px'; 
        }); 
        // 点击其他区域隐藏菜单 
        document.addEventListener('click', () => { 
            document.getElementById('contextMenu').style.display = 'none'; 
        }); 
    }

    //#region 分界线：PM List登录
    // 登陆页面 - 初始化 交接登录 表格行
    initializeTableRows() {
        const tbody = document.getElementById('registerTableBody');
        if (!tbody) return;
        
        // 清空现有行
        tbody.innerHTML = '';
        
        // 添加10行空表格
        for (let i = 0; i < 10; i++) {
            this.addNewRow(tbody);
            
        }

        // 绑定添加更多行按钮事件
        const addRowsBtn = document.querySelector('.btn-add-rows');
        if (addRowsBtn) {
            addRowsBtn.addEventListener('click', () => {
                for (let i = 0; i < 5; i++) {
                    this.addNewRow(tbody);
                }
            });
        }

        // 绑定表单提交事件
        const form = document.querySelector('.assregister-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();//阻止连接默认的跳转行为
                this.submitForm(form);
            });
        }

        // 初始化单选框事件
        // this.initRadioButtonChangeEvent(tbody);
    }

    // 登陆页面 - 添加新行
    addNewRow(tbody) {
        const row = document.createElement('tr');
        const rowIndex = tbody.children.length;
        
        row.innerHTML = `
            <td>
                <select name="line[]" required>
                    <option value="">请选择</option>
                </select>
            </td>    
            <td>
                <select name="unit[]" required>
                    <option value="">请选择</option>
                </select>
            </td>
            
            <td>
                <select name="problemtype[]" required>
                    <option value="一般" selected>一般</option>
                    <option value="硬件干涉">硬件干涉</option>
                    <option value="程序bug">程序bug</option>
                    <option value="部件损坏">部件损坏</option>
                </select>
            </td>
            <td>
                <input type="text" name="problempart[]">
            </td>
            <td>
                <textarea name="phenomenon[]" rows="2" required></textarea>
            </td>
            <td>
                <textarea name="analysis[]" rows="2"></textarea>
            </td>
            <td>
                <textarea name="measure[]" rows="2"></textarea>
            </td>
            <td>
                <div class="radio-group">
                    <label>
                        <input type="radio" name="needfollow_${rowIndex}" value="是" id="needfollowyes_${rowIndex}" >
                        <span>是</span>
                    </label>
                    <label>
                        <input type="radio" name="needfollow_${rowIndex}" value="否" checked id="needfollowno_${rowIndex}" >
                        <span>否</span>
                    </label>
                </div>
            </td>
            
            <td>
                <input type="file" id="fileUpload" name="files[]" multiple 
                       accept="image/jpeg,image/png,image/gif" 
                       style="width: 100px;"
                       onchange="pmAssociateManager.handleFileSelect(event, ${rowIndex})">
                <div class="file-list" id="fileList-${rowIndex}"></div>
            </td>
            <td class="action-column">
                <button type="button" class="btn-delete-row" onclick="pmAssociateManager.deleteRow(this)">
                    删除
                </button>
            </td>
        `;

        tbody.appendChild(row);

        // 加载该行的选项数据
        this.loadRowOptions(row);

        // 初始化文件上传处理方法
        // this.initFileUpload();
    }
    
    // 初始化单选框事件
    initRadioButtonChangeEvent(tbody){
        const rowIndex = tbody.children.length;
        const rowCount = tbody.rows.length;

        for(var i=0; i<rowCount; i++){
            // 为needfollow单选框添加事件
            // 获取初始单选框元素 
            var yesRadio = document.getElementById(`needfollowyes_${i}`); 
            var noRadio = document.getElementById(`needfollowno_${i}`); 
            // console.log('yesRadio',yesRadio,i);
            // console.log('noRadio',noRadio,i);
        
            const toWho = document.getElementById(`toWho_${i}`); 

            // 监听初始单选框的 change 事件 
            yesRadio?.addEventListener('change', function () { 
                if (this.checked) { 
                    toWho.removeAttribute('disabled'); 
                    toWho.setAttribute('required',true);
                } 
            }); 
            noRadio?.addEventListener('change', function () { 
                if (this.checked) { 
                    toWho.removeAttribute('disabled'); 
                    toWho.setAttribute('disabled',true); 
                } 
            });
            
        }
    }

    // 初始化文件上传处理方法
    initFileUpload() {
        const fileUpload = document.getElementById('fileUpload');
        const fileList = document.querySelector('.file-list');
        
        if (!fileUpload || !fileList) return;

        // 存储已选择的文件
        let selectedFiles = new Map();

        fileUpload.addEventListener('change', (e) => {
            const newFiles = Array.from(e.target.files);
            
            // 添加新选择的文件到已有文件列表中
            newFiles.forEach(file => {
                const fileId = Date.now() + '-' + file.name;
                selectedFiles.set(fileId, file);
                addFileToList(fileId, file);
            });

            // 清空input，允许重复选择相同文件
            fileUpload.value = '';
        });

                // 添加文件到列表的函数
                const addFileToList = (fileId, file) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span class="file-name" data-file-id="${fileId}">${file.name}</span>
                        <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                        <button type="button" class="btn-delete-file" data-file-id="${fileId}">×</button>
                    `;
        
                    // 添加文件名点击事件（预览）
                    const fileName = fileItem.querySelector('.file-name');
                    fileName.addEventListener('click', () => {
                        previewFile(file);
                    });
        
                    // 添加删除按钮事件
                    const deleteBtn = fileItem.querySelector('.btn-delete-file');
                    deleteBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        selectedFiles.delete(fileId);
                        fileItem.remove();
                    });
        
                    fileList.appendChild(fileItem);
                };
        
                // 文件预览函数
                const previewFile = (file) => {
                    // 如果是图片，创建预览
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const previewWindow = window.open('', '_blank');
                            previewWindow.document.write(`
                                <img src="${e.target.result}" style="max-width: 100%; height: auto;">
                            `);
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // 对于其他类型的文件，尝试在新窗口中打开
                        const fileUrl = URL.createObjectURL(file);
                        window.open(fileUrl, '_blank');
                    }
                };

        // // 表单提交处理
        // const form = document.querySelector('.assregister-form');
        // if (form) {
        //     form.onsubmit = async (e) => {
        //         e.preventDefault();
                
        //         try {
        //             const formData = new FormData(form);
                    
        //             // 添加用户科室数据
        //             if (this.userInfo?.section) {
        //                 formData.set('section', this.userInfo.section);
        //             }
                    
        //             // 添加文件
        //             if (selectedFiles.size > 0) {
        //                 selectedFiles.forEach((file) => {
        //                     formData.append('files[]', file);
        //                 });
        //             }

        //             // 发送请求
        //             const response = await fetch('php/submit_fault.php', {
        //                 method: 'POST',
        //                 body: formData
        //             });

        //             const result = await response.json();
        //             if (result.success) {
        //                 alert('故障信息已成功添加');
        //                 form.reset();
        //                 fileList.innerHTML = '';
        //                 selectedFiles.clear();
        //                 this.loadFaultList();
                        
        //                 // 重新设置科室和记录人
        //                 this.initFaultRegisterForm();
        //             } else {
        //                 throw new Error(result.message);
        //             }
        //         } catch (error) {
        //             console.error('提交错误:', error);
        //             alert('提交出错：' + error.message);
        //         }
        //     };
        // }
    }

    // 加载行选项数据
    async loadRowOptions(row) {
        try {
            // 获取用户科室
            const section = this.userInfo?.section || '';
            
            // 根据用户权限设置 project
            const project = this.getProject(this.userInfo.level);
            // console.log('登录level:', this.userInfo.level);
            // console.log('登录project:', project);
            // console.log('登录project01:', project==='');

            // 加载LINE选项
            const lineSelect = row.querySelector('select[name="line[]"]');
            const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}&project=${project}`);
            const lineData = await lineResponse.json();
            if (lineData.success) {
                lineSelect.innerHTML = '<option value="">请选择</option>' + 
                    lineData.data.map(line => `<option value="${line}">${line}</option>`).join('');
            }

            // 加载UNIT选项
            const unitSelect = row.querySelector('select[name="unit[]"]');
            const unitResponse = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
            const unitData = await unitResponse.json();
            if (unitData.success) {
                unitSelect.innerHTML = '<option value="">请选择</option>' + 
                    unitData.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
            }

            // 添加line变化时更新unit的监听 Line暂时不用
            lineSelect?.addEventListener('change', async () => {
                const line = lineSelect.value;
                
                // 获取当前行及其后面的所有行
                const tbody = row.parentElement;
                const allRows = Array.from(tbody.children);
                const currentRowIndex = allRows.indexOf(row);
                const followingRows = allRows.slice(currentRowIndex + 1);
                
                // 如果选择了line，更新后续行的line选项
                if (line) {
                    followingRows.forEach(async followingRow => {
                        const followingLineSelect = followingRow.querySelector('select[name="line[]"]');
                        if (followingLineSelect) {
                            try {
                                // 重新获取所有line选项
                                const response = await fetch(`php/get_options.php?type=line&section=${section}`);
                                const data = await response.json();
                                if (data.success) {
                                    followingLineSelect.innerHTML = '<option value="">请选择</option>' + 
                                        data.data.map(l => `<option value="${l}" ${l === line ? 'selected' : ''}>${l}</option>`).join('');
                                }
                            } catch (error) {
                                console.error('加载line选项失败:', error);
                            }
                            // 如果之前选中的值与新的line不同，触发change事件以更新unit
                            // followingLineSelect.dispatchEvent(new Event('change'));
                        }
                    });
                }

                // 如果line未选择，加载该科室下所有unit
                const url = line ? 
                    `php/get_options.php?type=unit&section=${section}&line=${line}` :
                    `php/get_options.php?type=unit&section=${section}`;
                
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">请选择</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });
            // unitSelect?.addEventListener('change', async () => {
            //     const unit = unitSelect.value;
                
            //     // 获取当前行及其后面的所有行
            //     const tbody = row.parentElement;
            //     const allRows = Array.from(tbody.children);
            //     const currentRowIndex = allRows.indexOf(row);
            //     const followingRows = allRows.slice(currentRowIndex + 1);
                
            //     // 如果选择了unit，更新后续行的unit选项
            //     if (unit) {
            //         followingRows.forEach(async followingRow => {
            //             const followingUnitSelect = followingRow.querySelector('select[name="unit[]"]');
            //             if (followingUnitSelect) {
            //                 try {
            //                     // 重新获取所有unit选项
            //                     const response = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
            //                     const data = await response.json();
            //                     if (data.success) {
            //                         followingUnitSelect.innerHTML = '<option value="">请选择</option>' + 
            //                             data.data.map(U => `<option value="${U}" ${U === unit ? 'selected' : ''}>${U}</option>`).join('');
            //                     }
            //                 } catch (error) {
            //                     console.error('加载line选项失败:', error);
            //                 }
            //                 // 如果之前选中的值与新的line不同，触发change事件以更新unit
            //                 // followingLineSelect.dispatchEvent(new Event('change'));
            //             }
            //         });
            //     }

            //     // 如果unit未选择，加载该科室下所有line
            //     const url = unit ? 
            //         `php/get_options.php?type=line&section=${section}&unit=${unit}&project=${project}` :
            //         `php/get_options.php?type=line&section=${section}&project=${project}`;
                
            //     try {
            //         const response = await fetch(url);
            //         const data = await response.json();
            //         if (data.success) {
            //             lineSelect.innerHTML = '<option value="">请选择</option>' + 
            //                 data.data.map(line => `<option value="${line}">${line}</option>`).join('');
            //         }
            //     } catch (error) {
            //         console.error('加载line选项失败:', error);
            //     }
            // });

            // 添加班次选择
            // const classesSelect = row.querySelector('select[name="classes[]"]');
            // classesSelect?.addEventListener('change', async () => {
            //     const classesName = classesSelect.value;
                
            //     // 获取当前行及其后面的所有行
            //     const tbody = row.parentElement;
            //     const allRows = Array.from(tbody.children);
            //     const currentRowIndex = allRows.indexOf(row);
            //     const followingRows = allRows.slice(currentRowIndex + 1);
                
            //     // 如果选择了班次className，更新后续行的className选项
            //     if (classesName==='LD') {
            //         followingRows.forEach(async followingRow => {
            //             const followingClassesSelect = followingRow.querySelector('select[name="classes[]"]');
            //             if (followingClassesSelect) {
            //                 followingClassesSelect.innerHTML = '<option value="LD" selected>LD</option><option value="LG">LG</option>';
            //             }
            //         });
            //     }
            //     else if(classesName==='LG') {
            //         followingRows.forEach(async followingRow => {
            //             const followingClassesSelect = followingRow.querySelector('select[name="classes[]"]');
            //             if (followingClassesSelect) {
            //                 followingClassesSelect.innerHTML = '<option value="LD">LD</option><option value="LG" selected>LG</option>';
            //             }
            //         });
            //     }

            //     // // 如果unit未选择，加载该科室下所有line
            //     // const url = unit ? 
            //     //     `php/get_options.php?type=line&section=${section}&unit=${unit}&project=${project}` :
            //     //     `php/get_options.php?type=line&section=${section}&project=${project}`;
                
            //     // try {
            //     //     const response = await fetch(url);
            //     //     const data = await response.json();
            //     //     if (data.success) {
            //     //         lineSelect.innerHTML = '<option value="">请选择</option>' + 
            //     //             data.data.map(line => `<option value="${line}">${line}</option>`).join('');
            //     //     }
            //     // } catch (error) {
            //     //     console.error('加载line选项失败:', error);
            //     // }
            // });

        } catch (error) {
            console.error('加载选项失败:', error);
        }
    }

    // 处理文件选择
    handleFileSelect(event, rowIndex) {
        const files = Array.from(event.target.files);
        const fileListDiv = document.getElementById(`fileList-${rowIndex}`);
        const fileInput = event.target;
        
        if (!fileListDiv) return;

        // 验证文件类型
        const invalidFiles = files.filter(file => !file.type.match(/^image\/(jpeg|png|gif)$/));
        if (invalidFiles.length > 0) {
            alert('只能上传JPG、PNG或GIF格式的图片文件！');
            fileInput.value = ''; // 清空选择
            fileListDiv.innerHTML = '';
            return;
        }

        // 显示文件列表 —— 重复上传图片，只保留最近一次的图片
        fileListDiv.innerHTML = files.map(file => `
            <div class="file-item">
                <span class="file-name">${file.name}</span>
                <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                <button type="button" class="btn-delete-file" 
                        onclick="pmAssociateManager.removeFile(${rowIndex}, '${file.name}', this)">×</button>
            </div>
        `).join('');
    }
    
    // 移除文件
    removeFile(rowIndex, fileName, button) {
        const fileInput = document.querySelector(`input[name="files[]"][onchange*="${rowIndex}"]`);
        const fileListDiv = document.getElementById(`fileList-${rowIndex}`);
        
        button.closest('.file-item').remove();
        
        if (fileListDiv.children.length === 0) {
            fileInput.value = '';
        }
    }

    // 登录页面 - 提交表单
    async submitForm(form) {
        try {
            const formData = new FormData();
            let validRowCount = 0;  // 记录有效行数
            console.log('登录页面 - 提交表单 validRowCount:',validRowCount);

            // 获取用户科室
            const section = this.userInfo?.section || '';
            // 根据用户权限设置 project
            let project = this.getProject(this.userInfo.level);

            // 收集所有行的数据
            const tbody = document.getElementById('registerTableBody');
            const rows = tbody.children;
            for (let i = 0; i < rows.length; i++) {
                // 获取行中的所有输入值
                const classes = "O";
                const line = rows[i].querySelector('select[name="line[]"]').value;
                const unit = rows[i].querySelector('select[name="unit[]"]').value;
                const problemtype = rows[i].querySelector('select[name="problemtype[]"]').value;
                const phenomenon = rows[i].querySelector('textarea[name="phenomenon[]"]').value;
                const analysis = rows[i].querySelector('textarea[name="analysis[]"]').value;
                const measure = rows[i].querySelector('textarea[name="measure[]"]').value;
                const problempart = rows[i].querySelector('input[name="problempart[]"]').value;
                // const problemcode = rows[i].querySelector('input[name="problemcode[]"]').value;
                const problemcode="";
                // const towho = rows[i].querySelector('select[name="towho[]"]').value;
                //console.log('登录页面 - towho:',towho);
                const towho = "PM";
                console.log('登录页面 - towho:',towho);

                const url = `php/get_options.php?type=project&section=${section}&line=${line}&unit=${unit}`;
                // 如果project没有值，根据Line Unit，添加project信息
                //if(project ==='')
                //    url = `php/get_options.php?type=project&section=${section}&line=${line}&unit=${unit}`;
                console.log('登录页面 - url:',url);

                try {
                    // console.log('0');
                    const response = await fetch(url);
                    // console.log('11');
                    const data = await response.json();
                    // console.log('登录页面 - data:',data);
                    // console.log('登录页面 - data:',typeof data);
                    // console.log('登录页面 - data.data:',data.data);
                    // console.log('登录页面 - data:',typeof data.data);
                    // console.log('登录页面 - data.data[0]:',data.data[0]);
                    // console.log('登录页面 - data.data[0]:',typeof data.data[0]);
                    if (data.success) {
                        // console.log('222');
                        project = data.data[0];
                        // console.log('登录页面 - project:',project);
                    }
                } catch (error) {
                    console.error('登录获取project失败:', error);
                    alert('登录获取project失败:' + error.message);
                    exit;
                }

                // 只处理有必填字段的行
                if (line && unit && problemtype && phenomenon) {
                    // 添加行数据
                    formData.append('classes[]', classes);
                    formData.append('line[]', line);
                    formData.append('unit[]', unit);
                    formData.append('problemtype[]', problemtype);
                    formData.append('phenomenon[]', phenomenon);
                    formData.append('analysis[]', analysis);
                    formData.append('measure[]', measure);
                    formData.append('problempart[]', problempart);
                    formData.append('problemcode[]', problemcode);
                    formData.append('towho[]', towho);
                    
                    // 添加科室信息 + project信息
                    formData.append('section[]', this.userInfo?.section || '');
                    formData.append('project[]', project);

                    // 添加单选按钮值
                    const selectedRadio = rows[i].querySelector('input[type="radio"]:checked');
                    formData.append('needfollow[]', selectedRadio ? selectedRadio.value : '否');

                    // 根据是否需要跟进，给出status的值open、close
                    if(selectedRadio.value === '否'){
                        formData.append('status[]', 'close');
                    }else{
                        formData.append('status[]', 'open');
                    }

                    // 添加文件（如果有）
                    const fileInput = rows[i].querySelector('input[type="file"]');
                    if (fileInput && fileInput.files.length > 0) {
                        Array.from(fileInput.files).forEach(file => {
                            // 使用validRowCount而不是i作为索引
                            formData.append(`files[${validRowCount}][]`, file);
                        });
                    }
                    validRowCount++;  // 增加有效行计数
                }
            }

            // 添加当前用户作为recorder
            formData.append('recorder', this.userInfo?.name || '');

            // 读出 formData 中的 键值对
            for (let [key, value] of formData) { 
                console.log(`Key: ${key}, Value: ${value}`); 
            } 
            
            const response = await fetch('php/submit_associate.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                alert('提交成功');
                form.reset();
                this.initializeTableRows();
                window.location.href = 'pm.html';
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('提交失败:', error);
            alert('提交失败: ' + error.message);
        }
    }

    // 添加删除行的方法
    deleteRow(button) {
        const row = button.closest('tr');
        if (row) {
            // 获取tbody中的行数
            const tbody = row.parentElement;
            if (tbody.children.length > 1) {  // 确保至少保留一行
                row.remove();
                // 重新排序其他行的radio name属性
                this.reorderRows(tbody);
            } else {
                alert('至少需要保留一行');
            }
        }
    }

    // 重新排序行的方法
    reorderRows(tbody) {
        Array.from(tbody.children).forEach((row, index) => {
            // 更新radio按钮的name属性
            const radioInputs = row.querySelectorAll('input[type="radio"]');
            radioInputs.forEach(input => {
                input.name = `needfollow_${index}`;
            });
            
            // 更新文件上传相关的属性
            const fileInput = row.querySelector('input[type="file"]');
            if (fileInput) {
                fileInput.setAttribute('onchange', `pmAssociateManager.handleFileSelect(event, ${index})`);
            }
            
            const fileList = row.querySelector('.file-list');
            if (fileList) {
                fileList.id = `fileList-${index}`;
            }
        });
    }
    //#endregion
    
    //#region 分界线：PM List查询
    // 查询页面 - 初始化查询页面 添加查询相关的方法
    initializeSearchForm() {
        // 初始化LINE和UNIT选项
        this.loadSearchOptions();
        
        // 绑定查询表单提交事件
        const searchForm = document.getElementById('asssearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.currentPage = 1; // 重置到第一页
                this.fetchAssociateData(); // 获取查询数据
            });
        }

        // 绑定分页事件
        this.initializePagination();
    }

    // 查询页面 - 加载查询表单的选项数据
    async loadSearchOptions() {
        try {
            // 获取用户科室
            const section = this.userInfo?.section || '';
            let project = this.getProject(this.userInfo.level);
            console.log('加载页面选择前1 - project:',project);
            

            // 加载Project选项
            const projectSelect = document.getElementById('asssearch-project');
            const projectResponse = await fetch(`php/get_options.php?type=project&section=${section}`);
            const projectData = await projectResponse.json();
            if (projectData.success) {
                //const projectSelect = document.getElementById('asssearch-project');
                if (projectSelect) {
                    projectSelect.innerHTML = '<option value="">全部</option>' + 
                        projectData.data.map(project => `<option value="${project}">${project}</option>`).join('');
                }
            }

            // 添加project变化时更新line和unit的监听
            //const projectSelect = document.getElementById('asssearch-project');
            projectSelect?.addEventListener('change', async () => {
                project = projectSelect.value;
                // 如果project未选择，加载该科室下所有line 和 unit
                const url_line = project ? 
                    `php/get_options.php?type=line&section=${section}&project=${project}` :
                    `php/get_options.php?type=line&section=${section}`;
                const url_unit = project ? 
                    `php/get_options.php?type=unit&section=${section}&project=${project}` :
                    `php/get_options.php?type=unit&section=${section}`;
                try {
                    const response = await fetch(url_line);
                    const data = await response.json();
                    if (data.success) {
                        lineSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(line => `<option value="${line}">${line}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载line选项失败:', error);
                }

                try {
                    const response = await fetch(url_unit);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });
            console.log('加载页面选择后 - project:',project);
            // 加载LINE选项
            const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}&project=${project}`);
            const lineData = await lineResponse.json();
            if (lineData.success) {
                const lineSelect = document.getElementById('asssearch-line');
                if (lineSelect) {
                    lineSelect.innerHTML = '<option value="">全部</option>' + 
                        lineData.data.map(line => `<option value="${line}">${line}</option>`).join('');
                }
            }

            // 加载UNIT选项
            const unitResponse = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
            const unitData = await unitResponse.json();
            if (unitData.success) {
                const unitSelect = document.getElementById('asssearch-unit');
                if (unitSelect) {
                    unitSelect.innerHTML = '<option value="">全部</option>' + 
                        unitData.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                }
            }

            // 添加line变化时更新unit的监听
            const lineSelect = document.getElementById('asssearch-line');
            const unitSelect = document.getElementById('asssearch-unit');
            lineSelect?.addEventListener('change', async () => {
                const line = lineSelect.value;
                // 如果line未选择，加载该科室下所有unit
                const url = line ? 
                    `php/get_options.php?type=unit&section=${section}&line=${line}&project=${project}` :
                    `php/get_options.php?type=unit&section=${section}&project=${project}`;
                
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });

            

        } catch (error) {
            console.error('加载选项失败:', error);
        }
    }

    // 查询页面 - 获取查询数据
    async fetchAssociateData() {
        try {
            const form = document.getElementById('asssearchForm');
            const formData = new FormData(form);
            const project = this.getProject(this.userInfo?.level);
            
            // 构建查询参数
            const params = new URLSearchParams();
            formData.forEach((value, key) => {
                if (value) params.append(key, value);
            });
            params.append('page', this.currentPage);
            params.append('pageSize', this.pageSize);

            // 如果没有指定科室参数，默认使用当前用户的科室
            if (!params.has('section') && this.userInfo?.section) {
                params.append('section', this.userInfo.section);
            }
            // 如果没有指定project参数，默认使用当前用户的project
            if (!params.has('project') && project) {
                params.append('project', project);
            }

            const response = await fetch(`php/get_associates_pm.php?${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                this.totalPages = result.totalPages;
                this.associateData = result.data;  //查询结果获取
                this.renderAssociateData(result.data);
                this.updatePagination(result);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            alert('获取数据失败: ' + error.message);
        }
    }

    // 查询页面 - 渲染查询结果
    renderAssociateData(data) {
        const tbody = document.querySelector('.data-table tbody');
        if (!tbody) return;

        //console.log('Search data:',data);

        //<td>${this.calculateShift(item.created_at)}</td>
        tbody.innerHTML = data.map(item => `
            <tr>
                <td>${this.formatDate(item.created_at)}</td>
                <td>${item.line}</td>
                <td>${item.unit}</td>
                <td>${item.problempart || ''}</td>
                <td>${item.phenomenon || ''}</td>
                <td>${item.analysis || ''}</td>
                <td>${item.measure || ''}</td>
                <td>${item.problemtype}</td>
                <td><span class="follow-status ${item.needfollow === '是' ? 'follow-yes' : 'follow-no'}">
                        ${item.needfollow}
                    </span>
                </td>
                <td><span class="closed-status">
                    ${item.status || ''}</span>
                </td>
                <td>
                    <button onclick="pmAssociateManager.editRecord(${item.id})" class="btn-edit">处理</button>
                </td>
            </tr>
        `).join('');
    }

    // 初始化分页控件
    initializePagination() {
        // 页码大小变化
        const pageSizeSelect = document.querySelector('.page-size');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.fetchAssociateData();
            });
        }

        // 页码导航按钮
        document.querySelector('.btn-prev-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.fetchAssociateData();
            }
        });

        document.querySelector('.btn-next-page')?.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.fetchAssociateData();
            }
        });

        // 首页导航按钮
        document.querySelector('.btn-first-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage = 1;
                this.fetchAssociateData();
            }
        });

        // 末页导航按钮
        document.querySelector('.btn-last-page')?.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage = this.totalPages;
                this.fetchAssociateData();
            }
        });

        // 页码输入框
        const pageInput = document.querySelector('.current-page');
        if (pageInput) {
            pageInput.addEventListener('change', (e) => {
                const page = parseInt(e.target.value);
                if (page >= 1 && page <= this.totalPages) {
                    this.currentPage = page;
                    this.fetchAssociateData();
                } else {
                    e.target.value = this.currentPage;
                }
            });
        }
    }

    // 更新分页信息
    updatePagination(result) {
        document.querySelector('.total-count').textContent = result.total;
        document.querySelector('.total-pages').textContent = result.totalPages;
        document.querySelector('.current-page').value = result.currentPage;
    }

    // 查看图片
    async viewImages(associateId) {
        try {
            const response = await fetch(`php/get_associate_files.php?associate_id=${associateId}`);
            const result = await response.json();

            if (result.success && result.data.length > 0) {
                // 创建图片预览弹窗
                const viewer = document.createElement('div');
                viewer.className = 'image-viewer';
                viewer.style.position = 'fixed';
                viewer.style.top = '0';
                viewer.style.left = '0';
                viewer.style.width = '100%';
                viewer.style.height = '100%';
                viewer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                viewer.style.zIndex = '1000';
                viewer.style.display = 'flex';
                viewer.style.justifyContent = 'center';
                viewer.style.alignItems = 'center';

                viewer.innerHTML = `
                    <div class="image-viewer-content">
                        <span class="close-btn" style="
                            position: absolute;
                            right: 20px;
                            top: 20px;
                            color: white;
                            font-size: 30px;
                            cursor: pointer;
                            z-index: 1001;
                        ">&times;</span>
                        <div class="image-list" style="
                            display: flex;
                            flex-wrap: wrap;
                            justify-content: center;
                            gap: 10px;
                            padding: 20px;
                            max-height: 90vh;
                            overflow-y: auto;
                        ">
                            ${result.data.map(file => `
                                <img src="uploads/associates/${file.file_path}" 
                                     alt="${file.file_name}"
                                     title="${file.file_name}"
                                    style="
                                        max-width: 90vw;
                                        max-height: 80vh;
                                        object-fit: contain;
                                        cursor: pointer;
                                    "
                                    onclick="window.open(this.src, '_blank')"
                                >
                            `).join('')}
                        </div>
                    </div>
                `;

                document.body.appendChild(viewer);

                // 关闭按钮事件
                viewer.querySelector('.close-btn').onclick = () => {
                    viewer.remove();
                };

                // 点击背景关闭
                viewer.addEventListener('click', (e) => {
                    if (e.target === viewer) {
                        viewer.remove();
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', function closeOnEsc(e) {
                    if (e.key === 'Escape') {
                        viewer.remove();
                        document.removeEventListener('keydown', closeOnEsc);
                    }
                });
            } else {
                alert('没有找到相关图片');
            }
        } catch (error) {
            console.error('获取图片失败:', error);
            alert('获取图片失败');
        }
    }

    // 查询页面 - 显示list详情 & 编辑
    async editRecord(id){
        // console.log('点击了 处理 按钮');
        
        if (!this.modal){
            console.log('没有模态框 modal');
            return;
        }

        // 从已加载的数据中查找交接详情
        const data = this.associateData.find(associate => associate.id === parseInt(id));
        if (!data) {
            console.error('未找到ID为', id, '的交接数据');
            return;
        }

        // 保存当前表单的值
        const searchForm = document.getElementById('asssearchForm');
        if (searchForm) {
            this.savedFormData = new FormData(searchForm);
        }

        // 赋值
        this.curData = data;

        // 显示模态框
        this.modal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // 更新模态框内容
        this.updateModalContent(data);

        // 加载并显示附件
        await this.loadAssociateFiles(id);
    }

    // 更新模态框内容
    updateModalContent(data) {
        console.log('更新模态框内容');

        // 设置历史履历不显示
        //this.bShowHistory = false;

        // 更新标题
        this.modal.querySelector('.modal-header h2').textContent = 
            `${data.line} ${data.unit}`;

        // 更新详情表格
        const detailRow = this.modal.querySelector('.detail-table tr:nth-child(2)');
        if (detailRow) {
            detailRow.innerHTML = `
                <td>${this.safeValue(data.section)}</td>
                <td>${this.safeValue(data.line)}</td>
                <td>${this.safeValue(data.unit)}</td>
                <td>${this.safeValue(data.phenomenon)}</td>
                <td>${this.safeValue(data.analysis)}</td>
                <td>${this.safeValue(data.measure)}</td>
                <td>${this.safeValue(data.problempart)}</td>
                <td>${this.safeValue(data.problemcode)}</td>
                <td>${this.safeValue(data.needfollow)}</td>
                <td>${this.formatDate(data.created_at)}</td>
                <td>${this.safeValue(data.recorder)}</td>
            `;
        }

        // try {
        //     // console.log('0');
        //     const response = await fetch(url);
        //     // console.log('11');
        //     const data = await response.json();
        //     // console.log('登录页面 - data:',data);
        //     // console.log('登录页面 - data:',typeof data);
        //     // console.log('登录页面 - data.data:',data.data);
        //     // console.log('登录页面 - data:',typeof data.data);
        //     // console.log('登录页面 - data.data[0]:',data.data[0]);
        //     // console.log('登录页面 - data.data[0]:',typeof data.data[0]);
        //     if (data.success) {
        //         // console.log('222');
        //         project = data.data[0];
        //         // console.log('登录页面 - project:',project);
        //     }
        // } catch (error) {
        //     console.error('登录获取project失败:', error);
        //     alert('登录获取project失败:' + error.message);
        //     exit;
        // }

        // 更新其他详情部分
        const towho = data.towho ? '跟踪者：' + data.towho : '';
        console.log('模态框内容towho:',towho);

        const sections = this.modal.querySelectorAll('.detail-section');
        sections.forEach(section => {
            const title = section.querySelector('h3').textContent;
            const content = section.querySelector('.section-content');
            
            switch (title) {
                case '故障原因':
                    // content.textContent = data.analysis || '';
                    content.innerHTML =`
                    <textarea name="analysis" rows="2" style="width:550px;height:100px;">${this.safeValue(data.analysis)}</textarea>
                    `;
                    break;
                case '处理内容':
                    // if (data.measure) {
                    //     const measure = data.measure.split('\n');
                    //     content.innerHTML = measure.map(m => `<p>${m}</p>`).join('');
                    // } else {
                    //     content.innerHTML = '';
                    // }
                    content.innerHTML =`
                    <textarea name="measure" rows="2" style="width:550px;height:100px;">${this.safeValue(data.measure)}</textarea>
                    `;
                    break;
                case '需要跟进':
                    
                    if(data.needfollow === '是'){
                        content.innerHTML = `
                            <label>
                                <input type="radio" name="needfollow" value="是" checked required>
                                <span>是</span>
                            </label>
                            <label>
                                <input type="radio" name="needfollow" value="否" required>
                                <span>否</span>
                            </label>
                        `;
                    }
                    else{
                        content.innerHTML = `
                            <label>
                                <input type="radio" name="needfollow" value="是"  required disabled>
                                <span>是</span>
                            </label>
                            <label>
                                <input type="radio" name="needfollow" value="否" checked required disabled>
                                <span>否</span>
                            </label>
                        `;
                    }
                    break;
                case '是否结案':
                    if(data.status === 'open'){
                        content.innerHTML = `
                            <label>
                                <input type="radio" name="status" value="open" checked required>
                                <span>open</span>
                            </label>
                            <label>
                                <input type="radio" name="status" value="close" required>
                                <span>close</span>
                            </label>
                            <div><label>${this.safeValue(towho)}</label></div>
                        `;
                    }
                    else{
                        content.innerHTML = `
                            <label>
                                <input type="radio" name="status" value="open"  required>
                                <span>open</span>
                            </label>
                            <label>
                                <input type="radio" name="status" value="close" checked required>
                                <span>close</span>
                            </label>
                            <div><label>${this.safeValue(towho)}</label></div>
                    `;
                        // content.innerHTML = `
                        //     <label>
                        //         <input type="radio" name="status" value="open"  required disabled>
                        //         <span>open</span>
                        //     </label>
                        //     <label>
                        //         <input type="radio" name="status" value="close" checked required disabled>
                        //         <span>close</span>
                        //     </label>
                        // `;
                    }
                    break;
                case '相关附件':
                    // TODO: 处理图片显示逻辑  file-section
                    // content.innerHTML = '暂无图片';
                    content.innerHTML = `
                        <div>
                            <span>
                                <input type="file" id="fileUpload" name="files[]" multiple 
                                accept="image/jpeg,image/png,image/gif" 
                                style="width: 100px;"
                                onchange="pmAssociateManager.handleFileSelect(event,211)">
                            </span>
                        </div>
                        <div class="file-list" id="fileList-211"></div>
                        <div class="file-section"></div>
                    `;
                    break;
            }
            
            
        });
        
        // 添加按钮 & 事件
        const modalBtn = this.modal.querySelector('.modal-btn-modify');
        if(modalBtn){
            modalBtn.innerHTML=`
                <button class="btn-modify" onclick="pmAssociateManager.modifyNeedfollowMsg('${data.status}')">修改</button>
            `;
        }
        
        // 添加历史履历显示
        // const tableHistoryBody = this.modal.getElementById('history-body-table');

        // 获取历史信息的查询条件
        const params = new URLSearchParams();
        params.append('section',data.section);
        params.append('line',data.line);
        params.append('project',data.project);
        params.append('unit',data.unit);
        params.append('phenomenon',data.phenomenon);
        params.append('problemtype',data.problemtype);

        this.getHistoryContent(params);
    }

    // 模态框 修改按钮 处理事件
    async modifyNeedfollowMsg(str){
        if(str === 'open'){
            if(confirm('您确定需要对该问题点进行结案?')){
                try{
                    const formData = new FormData();
                    
                    // 获取模态框中的内容
                    const detailContent = document.getElementById('detail-sections');
                    const analysis = detailContent.querySelector('textarea[name="analysis"]').value;
                    const measure = detailContent.querySelector('textarea[name="measure"]').value;
                    const status = detailContent.querySelector('input[name="status"]:checked');
                    
                    // 添加科室信息
                    formData.append('section',this.userInfo?.section || '');
                    formData.append('project', this.curData.project);
                    formData.append('classes', this.curData.classes);

                    // 添加无需 更新 的信息
                    formData.append('line',this.curData.line);
                    formData.append('unit',this.curData.unit);
                    formData.append('phenomenon',this.curData.phenomenon);
                    formData.append('problemtype',this.curData.problemtype);
                    formData.append('problempart',this.curData.problempart);
                    // formData.append('problemcode',this.curData.problemcode);
                    formData.append('needfollow',this.curData.needfollow);
                    formData.append('towho',this.curData.towho);
        
                    // 添加 更新 的信息
                    formData.append('analysis',analysis);
                    formData.append('measure',measure);
                    formData.append('status',status? status.value : 'open');
        
                    // 添加当前用户作为recorder
                    formData.append('recorder', this.userInfo?.name || '');

                    // 创建时间不变
                    formData.append('created_at', this.curData.created_at);
                    // console.log('test created_at:', this.curData.created_at);
        
                    // 添加 step
                    // console.log('test step:', this.curData.step);
                    formData.append('step', ++this.curData.step);
                    // console.log('test step:', this.curData.step);
        
                    // 添加文件（如果有）
                    const fileInput = detailContent.querySelector('input[type="file"]');
                    if (fileInput && fileInput.files.length > 0) {
                        Array.from(fileInput.files).forEach(file => {
                            // 使用validRowCount而不是i作为索引
                            formData.append(`files[]`, file);
                        });
                    }
        
                    // 读出 formData 中的 键值对
                    for (let [key, value] of formData) { 
                        console.log(`Key: ${key}, Value: ${value}`); 
                    } 

                    const response = await fetch('php/modify_associate.php', {
                        method: 'POST',
                        body: formData
                    });
        
                    const result = await response.json();
                    if (result.success) {
                        // 修改完成，初始化查询页面，并回到查询页面
                        alert('修改成功');
                        str = '';// 这里的赋值，没有作用，该方法执行时，传入的参数没有发生改变
                        // this.initializeSearchForm(); // 初始化查询页面
                        // window.location.href = 'associate.html';
                        // 关闭模态框
                        this.modal.style.display = 'none';
                        document.body.style.overflow = 'auto';

                        // 使用保存的表单值构建查询参数
                        if (this.savedFormData) {
                            const params = new URLSearchParams();
                            for (let [key, value] of this.savedFormData) {
                                if (value) {
                                    params.append(key, value);
                                }
                            }
                            params.append('page', this.currentPage);
                            params.append('pageSize', this.pageSize);

                            // 如果没有指定科室参数，默认使用当前用户的科室
                            if (!params.has('section') && this.userInfo?.section) {
                                params.append('section', this.userInfo.section);
                            }

                            // 使用保存的参数重新加载数据
                            const response = await fetch(`php/get_associates_pm.php?${params.toString()}`);
                            const result = await response.json();

                            if (result.success) {
                                this.totalPages = result.totalPages;
                                this.associateData = result.data;
                                this.renderAssociateData(result.data);
                                this.updatePagination(result);
                            }
                        } else {
                            // 如果没有保存的表单值，则正常刷新
                            await this.fetchAssociateData();
                        }
                    } else {
                        throw new Error(result.message);
                    }
                }catch(error){
                    console.log('log:'+ error);
                    console.error('修改失败:', error);
                    alert('修改失败: ' + error.message);
                }
            }
        }
        else if(str === 'close'){
            // alert('该问题点已结案，无法再修改！');
            if(confirm('您确定需要对已结案问题点进行修改?')){
                try{
                    const formData = new FormData();
                    
                    // 获取模态框中的内容
                    const detailContent = document.getElementById('detail-sections');
                    const analysis = detailContent.querySelector('textarea[name="analysis"]').value;
                    const measure = detailContent.querySelector('textarea[name="measure"]').value;
                    const status = detailContent.querySelector('input[name="status"]:checked');
                    
                    // 添加科室信息
                    formData.append('section',this.userInfo?.section || '');
                    formData.append('project', this.curData.project);
                    formData.append('classes', this.curData.classes);

                    // 添加无需 更新 的信息
                    formData.append('line',this.curData.line);
                    formData.append('unit',this.curData.unit);
                    formData.append('phenomenon',this.curData.phenomenon);
                    formData.append('problemtype',this.curData.problemtype);
                    formData.append('problempart',this.curData.problempart);
                    formData.append('problemcode',this.curData.problemcode);
                    formData.append('needfollow',this.curData.needfollow);
                    formData.append('towho',this.curData.towho);

                    // 添加 更新 的信息
                    formData.append('analysis',analysis);
                    formData.append('measure',measure);
                    formData.append('status',status? status.value : 'close');
        
                    // 添加当前用户作为recorder
                    formData.append('recorder', this.userInfo?.name || '');

                    // 创建时间不变
                    formData.append('created_at', this.curData.created_at);
                    // console.log('test created_at:', this.curData.created_at);
        
                    // 添加 step
                    // console.log('test step:', this.curData.step);
                    formData.append('step', ++this.curData.step);
                    // console.log('test step:', this.curData.step);
        
                    // 添加文件（如果有）
                    const fileInput = detailContent.querySelector('input[type="file"]');
                    if (fileInput && fileInput.files.length > 0) {
                        Array.from(fileInput.files).forEach(file => {
                            // 使用validRowCount而不是i作为索引
                            formData.append(`files[]`, file);
                        });
                    }
        
                    // 读出 formData 中的 键值对
                    for (let [key, value] of formData) { 
                        console.log(`Key: ${key}, Value: ${value}`); 
                    } 

                    const response = await fetch('php/modify_associate.php', {
                        method: 'POST',
                        body: formData
                    });
        
                    const result = await response.json();
                    if (result.success) {
                        // 修改完成，初始化查询页面，并回到查询页面
                        alert('修改成功');
                        str = '';// 这里的赋值，没有作用，该方法执行时，传入的参数没有发生改变
                        // this.initializeSearchForm(); // 初始化查询页面
                        // window.location.href = 'associate.html';
                        // 关闭模态框
                        this.modal.style.display = 'none';
                        document.body.style.overflow = 'auto';

                        // 使用保存的表单值构建查询参数
                        if (this.savedFormData) {
                            const params = new URLSearchParams();
                            for (let [key, value] of this.savedFormData) {
                                if (value) {
                                    params.append(key, value);
                                }
                            }
                            params.append('page', this.currentPage);
                            params.append('pageSize', this.pageSize);

                            // 如果没有指定科室参数，默认使用当前用户的科室
                            if (!params.has('section') && this.userInfo?.section) {
                                params.append('section', this.userInfo.section);
                            }

                            // 使用保存的参数重新加载数据
                            const response = await fetch(`php/get_associates_pm.php?${params.toString()}`);
                            const result = await response.json();

                            if (result.success) {
                                this.totalPages = result.totalPages;
                                this.associateData = result.data;
                                this.renderAssociateData(result.data);
                                this.updatePagination(result);
                            }
                        } else {
                            // 如果没有保存的表单值，则正常刷新
                            await this.fetchAssociateData();
                        }
                    } else {
                        throw new Error(result.message);
                    }
                }catch(error){
                    console.log('log:'+ error);
                    console.error('修改失败:', error);
                    alert('修改失败: ' + error.message);
                }
            }
        }
        else{
            alert('不可重复修改哦！');
        }
    }

    // 添加加载附件的方法
    async loadAssociateFiles(associateId) {
        try {
            const response = await fetch(`php/get_associate_files.php?associate_id=${associateId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message);
            }
            console.log('获取到了文件,ID为:',associateId);
            console.log('文件个数:',result.data.length);

            const fileSection = this.modal.querySelector('.file-section');
            if (!fileSection){
                console.log('return了');
                return;
            } 

            if (result.data.length === 0) {
                fileSection.innerHTML = '<p>暂无附件</p>';
                return;
            }

            // 显示附件列表，区分图片和非图片文件的处理
            fileSection.innerHTML = result.data.map(file => {
                const isImage = this.isImageFile(file.file_name);
                const fileUrl = `uploads\\associates\\${file.file_path}`;
                const fileSize = (file.file_size / 1024).toFixed(2);
                console.log('file name:',file.file_name);
                console.log('fileUrl:',fileUrl);

                return `
                    <div class="file-item">
                        <span class="file-name" 
                              data-file-url="${fileUrl}"
                              data-file-name="${file.file_name}"
                              data-is-image="${isImage}"
                              style="cursor: pointer;">
                            ${file.file_name}
                        </span>
                        <span class="file-size">(${fileSize} KB)</span>
                    </div>
                `;

                console.log('fileSection.innerHTML:',fileSection.innerHTML);
            }).join('');

            // 为所有文件名添加点击事件监听器
            const fileNames = fileSection.querySelectorAll('.file-name');
            fileNames.forEach(fileName => {
                fileName.addEventListener('click', () => {
                    const fileUrl = fileName.dataset.fileUrl;
                    const isImage = fileName.dataset.isImage === 'true';
                    const originalFileName = fileName.dataset.fileName;
                    console.log("isImage:",isImage);
                    console.log("fileUrl:",fileUrl);

                    if (isImage) {
                        this.showImage(fileUrl);
                    } else {
                        this.downloadFile(fileUrl, originalFileName);
                    }
                });
            });
            console.log('添加文件完毕');
        } catch (error) {
            console.error('加载附件失败:', error);
            const fileSection = this.modal.querySelector('.file-section');
            if (fileSection) {
                fileSection.innerHTML = '<p class="error">加载附件失败</p>';
            }
        }
    }

    // 模态框，获取历史履历信息
    async getHistoryContent(params){
        try {
            // 打印Log： 读出 查询历史信息条件 中的 键值对
            // console.log('1');
            // for (let [key, value] of params) { 
            //     console.log(`Key: ${key}, Value: ${value}`); 
            // } 
            // console.log('2');
            const response = await fetch(`php/get_history_associates.php?${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                this.historyData = result.data;  //查询结果获取
                this.historyBodyContent(result.data);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取历史数据失败:', error);
            alert('获取历史数据失败: ' + error.message);
        }
    }

    // 显示历史履历信息 表
    historyBodyContent(data){
        console.log('历史履历信息 表');

        const historyTbody = document.querySelector('.history-body-table tbody');
        if (!historyTbody) return;

        // 时间显示天： ${this.formatDate(item.updated_at)}
        historyTbody.innerHTML = data.map(item => `
            <tr>
                <td>${item.step+1}</td>
                <td>${item.analysis}</td>
                <td>${item.measure}</td>
                <td>${item.updated_at}</td>
                <td>${item.recorder || ''}</td>
                
                <td>
                    <div class="history-file-section_${item.id}" id="history-file-section_${item.id}"></div>
                </td>
                
            </tr>
        `).join('');

        // 循环加载历史履历文件
        data.map(item=>{
            this.loadHistoryAssociateFiles(item.id);
        });
    }

    // 获取历史附件并加载的方法
    async loadHistoryAssociateFiles(associateId) {
        try {
            const response = await fetch(`php/get_associate_files.php?associate_id=${associateId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message);
            }
            // console.log('获取到了履历文件,ID为:',associateId);
            // console.log('履历文件个数:',result.data.length);

            const fileSection = this.modal.querySelector('.history-file-section_'+associateId);
            if (!fileSection){
                console.log('没有找到fileSection，return了');
                return;
            } 

            if (result.data.length === 0) {
                fileSection.innerHTML = '<p>暂无附件</p>';
                return;
            }

            // 显示附件列表，区分图片和非图片文件的处理
            fileSection.innerHTML = result.data.map(file => {
                const isImage = this.isImageFile(file.file_name);
                const fileUrl = `uploads\\associates\\${file.file_path}`;
                const fileSize = (file.file_size / 1024).toFixed(2);
                // console.log('file name:',file.file_name);
                // console.log('fileUrl:',fileUrl);

                return `
                    <div class="file-item">
                        <span class="file-name" 
                            data-file-url="${fileUrl}"
                            data-file-name="${file.file_name}"
                            data-is-image="${isImage}"
                            style="cursor: pointer;">
                            ${file.file_name}
                        </span>
                        <span class="file-size">(${fileSize} KB)</span>
                    </div>
                `;

                console.log('fileSection.innerHTML:',fileSection.innerHTML);
            }).join('');

            // 为所有文件名添加点击事件监听器
            const fileNames = fileSection.querySelectorAll('.file-name');
            fileNames.forEach(fileName => {
                fileName.addEventListener('click', () => {
                    const fileUrl = fileName.dataset.fileUrl;
                    const isImage = fileName.dataset.isImage === 'true';
                    const originalFileName = fileName.dataset.fileName;
                    // console.log("isImage:",isImage);
                    // console.log("fileUrl:",fileUrl);

                    if (isImage) {
                        this.showImage(fileUrl);
                    } else {
                        this.downloadFile(fileUrl, originalFileName);
                    }
                });
            });
            // console.log('添加文件完毕');
        } catch (error) {
            console.error('加载附件失败:', error);
            const fileSection = this.modal.querySelector('.file-section');
            if (fileSection) {
                fileSection.innerHTML = '<p class="error">加载附件失败</p>';
            }
        }
    }

    historyTitleClick(bShow){
        const historyContainer = document.getElementById('history-body');
        if(!bShow){
            this.modal.querySelector('.history-title h3').textContent = `点击历史履历信息隐藏`;
            this.bShowHistory = true;
            // 控制历史履历的 显示
            if (historyContainer) {
                historyContainer.style.display = 'inline-block';
            }
        }
        else{
            this.modal.querySelector('.history-title h3').textContent = `点击历史履历信息展开`;
            this.bShowHistory = false;
            // 控制历史履历的 隐藏
            if (historyContainer) {
                historyContainer.style.display = 'none';
            }
        }
    }

    // 添加判断文件是否为图片的方法
    isImageFile(fileName) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
        const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        return imageExtensions.includes(ext);
    }

    // 添加显示图片的方法
    showImage(imageUrl) {
        const imageWindow = window.open('', '_blank');
        imageWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>图片预览</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                        background: #f0f0f0;
                    }
                    img {
                        max-width: 100%;
                        max-height: 90vh;
                        object-fit: contain;
                        box-shadow: 0 0 20px rgba(0,0,0,0.15);
                    }
                </style>
            </head>
            <body>
                <img src="${imageUrl}" alt="预览图片">
            </body>
            </html>
        `);
    }

    // 添加下载文件的方法
    downloadFile(fileUrl, fileName) {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    //#endregion

    //#region 分界线：交接查询右键导出
    // 导出为CSV 
    exportToExcel() { 
        const rows = document.querySelectorAll("#assSearchTable tr"); 
        let csvContent = ""; 
        for (let i = 0; i < rows.length; i++) { 
            const cells = rows[i].querySelectorAll("td, th"); 
            const row = []; 
            for (let j = 0; j < cells.length; j++) { 
                // 处理特殊字符（逗号、换行符等） 
                row.push(`"${cells[j].innerText.replace(/"/g, '""')}"`); 
            } 
            csvContent += row.join(",") + "\n"; 
        } 
        // 创建下载链接 
        const blob = new Blob(["\ufeff" + csvContent], { type: "text/csv;charset=utf-8;" }); 
        const link = document.createElement("a"); 
        const url = URL.createObjectURL(blob); 
        link.href = url; 
        link.download = "表格数据.csv"; 
        link.click(); 
        URL.revokeObjectURL(url); 
    } 
    //#endregion

    //#region 分界线：交接分析页面
    // 交接分析页面 - 图表 测试函数
    initECharts(){
        // 基于准备好的dom，初始化echarts实例 
        var myChart = echarts.init(document.getElementById('EChartsDOM')); 
        // 指定图表的配置项和数据 
        var option = { 
            title: { text: 'ECharts 入门示例' }, 
            tooltip: {}, 
            legend: { data: ['销量'] }, 
            xAxis: { data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子'] }, 
                yAxis: {}, 
                series: [ { name: '销量', type: 'bar', data: [5, 20, 36, 10, 10, 20] } ] 
            }; 
        // 使用刚指定的配置项和数据显示图表。 
        myChart.setOption(option); 
    }

    // 交接分析页面 - 初始化交接分析页面
    initializeAnalysisForm() {
        // 初始化LINE和UNIT选项
        this.loadAnalysisOptions();
        
        // 绑定查询表单提交事件
        const analysisForm = document.getElementById('assAnalysisForm');
        if (analysisForm) {
            analysisForm.addEventListener('submit', (e) => {
                e.preventDefault();
                //this.currentPage = 1; // 重置到第一页
                this.fetchAnalysisData(); // 获取查询数据
            });
        }

        this.chartType = document.getElementById('assAnalysis-tableShow').value;

        console.log('this.chartType',this.chartType); 
        // 显示图表
        this.drawAnalysisECharts(this.analysisData,this.chartType);
    }

    // 交接分析页面 - 加载分析表单的选项数据
    async loadAnalysisOptions() {
        try {
            // 获取用户科室
            const section = this.userInfo?.section || '';
            let project = this.getProject(this.userInfo.level);
            console.log('加载页面选择前1 - project:',project);
            

            // 加载Project选项
            const projectSelect = document.getElementById('assAnalysis-project');
            const projectResponse = await fetch(`php/get_options.php?type=project&section=${section}`);
            const projectData = await projectResponse.json();
            if (projectData.success) {
                if (projectSelect) {
                    projectSelect.innerHTML = '<option value="">全部</option>' + 
                        projectData.data.map(project => `<option value="${project}">${project}</option>`).join('');
                }
            }

            // 添加project变化时更新line和unit的监听
            projectSelect?.addEventListener('change', async () => {
                project = projectSelect.value;
                // 如果project未选择，加载该科室下所有line 和 unit
                const url_line = project ? 
                    `php/get_options.php?type=line&section=${section}&project=${project}` :
                    `php/get_options.php?type=line&section=${section}`;
                const url_unit = project ? 
                    `php/get_options.php?type=unit&section=${section}&project=${project}` :
                    `php/get_options.php?type=unit&section=${section}`;
                try {
                    const response = await fetch(url_line);
                    const data = await response.json();
                    if (data.success) {
                        lineSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(line => `<option value="${line}">${line}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载line选项失败:', error);
                }

                try {
                    const response = await fetch(url_unit);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });
            console.log('加载页面选择后 - project:',project);
            // 加载LINE选项
            const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}&project=${project}`);
            const lineData = await lineResponse.json();
            if (lineData.success) {
                const lineSelect = document.getElementById('assAnalysis-line');
                if (lineSelect) {
                    lineSelect.innerHTML = '<option value="">全部</option>' + 
                        lineData.data.map(line => `<option value="${line}">${line}</option>`).join('');
                }
            }

            // 加载UNIT选项
            const unitResponse = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
            const unitData = await unitResponse.json();
            if (unitData.success) {
                const unitSelect = document.getElementById('assAnalysis-unit');
                if (unitSelect) {
                    unitSelect.innerHTML = '<option value="">全部</option>' + 
                        unitData.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                }
            }

            // 添加line变化时更新unit的监听
            const lineSelect = document.getElementById('assAnalysis-line');
            const unitSelect = document.getElementById('assAnalysis-unit');
            lineSelect?.addEventListener('change', async () => {
                const line = lineSelect.value;
                // 如果line未选择，加载该科室下所有unit
                const url = line ? 
                    `php/get_options.php?type=unit&section=${section}&line=${line}&project=${project}` :
                    `php/get_options.php?type=unit&section=${section}&project=${project}`;
                
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });

        } catch (error) {
            console.error('加载选项失败:', error);
        }
    }

    // 交接分析页面 - 显示图表
    async fetchAnalysisData() {
        try {
            const form = document.getElementById('assAnalysisForm');
            const formData = new FormData(form);
            const project = this.getProject(this.userInfo?.level);
            this.chartType = document.getElementById('assAnalysis-tableShow').value;    //图表类型
            const tableFor = document.getElementById('assAnalysis-tableFor').value;     //图表呈现对象
            
            console.log('tableFor',tableFor);
            // 构建查询参数
            const params = new URLSearchParams();
            formData.forEach((value, key) => {
                if (value) params.append(key, value);
            });
            // params.append('page', this.currentPage);
            // params.append('pageSize', this.pageSize);

            // 如果没有指定科室参数，默认使用当前用户的科室
            if (!params.has('section') && this.userInfo?.section) {
                params.append('section', this.userInfo.section);
            }
            // 如果没有指定project参数，默认使用当前用户的project
            if (!params.has('project') && project) {
                params.append('project', project);
            }

            const response = await fetch(`php/get_associates_analysis.php?tableFor=${tableFor}&${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                this.analysisData = result.data;  //查询结果获取
                //this.renderAssociateData(result.data);
                this.drawAnalysisECharts(result.data,this.chartType);   // 显示图表
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            alert('获取数据失败: ' + error.message);
        }
    }

    // 交接分析页面 - 画图表
    drawAnalysisECharts(data,chartType){
        var myChart=null;
        // data 准备
        const lines = data.map(item => {
            // console.log('item:',item);
            return item.name;
        });
        const qtys = data.map(item => {
            return item.value;
        });
        const pies = data.map(({ name, value }) => ({ value, name })); 
        
        // console.log('data.line:',lines );
        // console.log('data.qty:',qtys);
        // console.log('data.pies:',pies);

        switch(chartType){
            case 'bar':
                // 基于准备好的dom，初始化echarts实例 
                myChart = echarts.init(document.getElementById('EChartsDOM'));
                var option = { 
                    title: { text: '交接分析' }, 
                    tooltip: {}, 
                    legend: { data: ['交接数量'] }, 
                    xAxis: { 
                        show: true,
                        data: lines 
                    }, 
                    yAxis: {
                        show: true,
                    }, 
                    series: [ { 
                        name: '交接数量', 
                        type: 'bar', 
                        data: qtys ,
                        label:{
                            show: true,
                            position: 'top'
                        }
                        
                    } ] 
                };
                // 使用刚指定的配置项和数据显示图表。 
                myChart.setOption(option); 
                break;
            case 'line':
                // 基于准备好的dom，初始化echarts实例 
                myChart = echarts.init(document.getElementById('EChartsDOM'));
                var option = { 
                    title: { text: '交接分析' }, 
                    tooltip: {}, 
                    legend: { data: ['交接数量'] }, 
                    xAxis: {
                        show: true,
                        type: 'category',
                        data: lines
                    },
                    yAxis: {
                        show: true,
                        type: 'value'
                    },
                    series: [{
                            data:qtys,
                            type: 'line',
                            label:{
                                show: true,
                                position: 'top'
                            }
                        }
                    ]
                };
                // 使用刚指定的配置项和数据显示图表。 
                myChart.setOption(option); 
                break;
            case 'pie':
                // 基于准备好的dom，初始化echarts实例 
                myChart = echarts.init(document.getElementById('EChartsDOM'));
                var option = { 
                    title: { text: '交接分析' }, 
                    tooltip: {}, 
                    legend: {data:[
                        ''
                    ]}, 
                    xAxis: {
                        show:false
                    },
                    yAxis: {
                        show:false
                    },
                    series: [
                        {
                            type: 'pie',
                            data: pies, 
                            radius: '75%',
                            label:{
                                show: true,
                                
                                position: 'outside'    // 'inside'
                            }
                        }
                    ]
                };
                // 使用刚指定的配置项和数据显示图表。 
                myChart.setOption(option); 
                break;
            default:
                break;
        }
         
        
    }
    //#endregion

    //#region 分界线：班组交接页面
    // 班组交接页面 - 初始化 交接登录 表格行
    initializeShiftTableRows() {
        const tbody = document.getElementById('shiftTableBody');
        if (!tbody) return;
        
        // 清空现有行
        tbody.innerHTML = '';
        
        // 添加10行空表格
        for (let i = 0; i < 10; i++) {
            this.addNewShiftRow(tbody);
        }

        // 绑定添加更多行按钮事件
        const addRowsBtn = document.querySelector('.btn-add-shift-rows');
        if (addRowsBtn) {
            addRowsBtn.addEventListener('click', () => {
                for (let i = 0; i < 5; i++) {
                    this.addNewShiftRow(tbody);
                }
            });
        }

        // 绑定表单提交事件
        const form = document.querySelector('.assShift-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();//阻止连接默认的跳转行为
                this.submitShiftForm(form);
            });
        }
    }

    // 班组交接页面 - 添加新行
    addNewShiftRow(tbody) {
        const row = document.createElement('tr');
        const rowIndex = tbody.children.length;
        
        row.innerHTML = `
            <td>
                <select name="shift-line[]" required>
                    <option value="">请选择</option>
                </select>
            </td>
            <td>
                <select name="shift-unit[]" required>
                    <option value="">请选择</option>
                </select>
            </td>
            <td>
                <select name="shift-problemtype[]" required>
                    <option value="一般故障" selected>一般故障</option>
                    <option value="部件损坏">部件损坏</option>
                    <option value="品质问题">品质问题</option>
                    <option value="程序bug">程序bug</option>
                    <option value="MC相关">MC相关</option>
                    <option value="其他">其他</option>
                </select>
            </td>
            <td>
                <textarea name="shift-phenomenon[]" rows="2" required></textarea>
            </td>
            <td>
                <textarea name="shift-analysis[]" rows="2"></textarea>
            </td>
            <td>
                <textarea name="shift-measure[]" rows="2"></textarea>
            </td>
            
            <td>
                <div class="shift-radio-group">
                    <label>
                        <input type="radio" name="shift-needfollow_${rowIndex}" value="是" >
                        <span>是</span>
                    </label>
                    <label>
                        <input type="radio" name="shift-needfollow_${rowIndex}" value="否" checked >
                        <span>否</span>
                    </label>
                </div>
            </td>
            <td>
                <input type="file" id="shift-fileUpload" name="shift-files[]" multiple 
                       accept="image/jpeg,image/png,image/gif" 
                       style="width: 100px;"
                       onchange="pmAssociateManager.handleFileSelect(event, ${rowIndex})">
                <div class="shift-file-list" id="shift-fileList-${rowIndex}"></div>
            </td>
            <td class="shift-action-column">
                <button type="button" class="btn-delete-shift-row" onclick="pmAssociateManager.deleteRow(this)">
                    删除
                </button>
            </td>
        `;
        tbody.appendChild(row);

        // 加载该行的选项数据   —— 后面再修改启用
        this.loadShiftRowOptions(row);

        // 初始化文件上传处理方法
        // this.initFileUpload();
    }

    // 班组交接页面 - 加载行选项数据
    async loadShiftRowOptions(row) {
        try {
            // 获取用户科室
            const section = this.userInfo?.section || '';
            
            // 根据用户权限设置 project
            //const project = this.getProject(this.userInfo.level);
            // console.log('登录level:', this.userInfo.level);
            // console.log('登录project:', project);
            // console.log('登录project01:', project==='');

            // 加载LINE选项
            const lineSelect = row.querySelector('select[name="shift-line[]"]');
            //const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}&project=${project}`);
            const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}`);
            const lineData = await lineResponse.json();
            if (lineData.success) {
                lineSelect.innerHTML = '<option value="">请选择</option>' + 
                    lineData.data.map(line => `<option value="${line}">${line}</option>`).join('');
            }
 
            // console.log('lineSelect:',lineSelect);
            // console.log('lineSelect Count:',lineSelect.length);
            // 加载UNIT选项
            const unitSelect = row.querySelector('select[name="shift-unit[]"]');
            //const unitResponse = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
            const unitResponse = await fetch(`php/get_options.php?type=unit&section=${section}`);
            const unitData = await unitResponse.json();
            if (unitData.success) {
                unitSelect.innerHTML = '<option value="">请选择</option>' + 
                    unitData.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
            }

            // 添加line变化时更新unit的监听 Line暂时不用
            lineSelect?.addEventListener('change', async () => {
                const line = lineSelect.value;
                
                // 获取当前行及其后面的所有行
                const tbody = row.parentElement;
                const allRows = Array.from(tbody.children);
                const currentRowIndex = allRows.indexOf(row);
                const followingRows = allRows.slice(currentRowIndex + 1);
                
                // 如果选择了line，更新后续行的line选项
                if (line) {
                    followingRows.forEach(async followingRow => {
                        const followingLineSelect = followingRow.querySelector('select[name="shift-line[]"]');
                        if (followingLineSelect) {
                            try {
                                // 重新获取所有line选项
                                const response = await fetch(`php/get_options.php?type=line&section=${section}`);
                                const data = await response.json();
                                if (data.success) {
                                    followingLineSelect.innerHTML = '<option value="">请选择</option>' + 
                                        data.data.map(l => `<option value="${l}" ${l === line ? 'selected' : ''}>${l}</option>`).join('');
                                }
                            } catch (error) {
                                console.error('加载line选项失败:', error);
                            }
                            // 如果之前选中的值与新的line不同，触发change事件以更新unit
                            // followingLineSelect.dispatchEvent(new Event('change'));
                        }
                    });
                }

                // 如果line未选择，加载该科室下所有unit
                const url = line ? 
                    `php/get_options.php?type=unit&section=${section}&line=${line}` :
                    `php/get_options.php?type=unit&section=${section}`;
                
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    if (data.success) {
                        // 这里只加了第一个UnitSelect，后面的没有 for循环行不行？
                        unitSelect.innerHTML = '<option value="">请选择</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });
            // Unit选好后，更新后面的Unit，不刷新其他
            unitSelect?.addEventListener('change', async () => {
                const unit = unitSelect.value;
                
                // 获取当前行及其后面的所有行
                const tbody = row.parentElement;
                const allRows = Array.from(tbody.children);
                const currentRowIndex = allRows.indexOf(row);
                const followingRows = allRows.slice(currentRowIndex + 1);
                
                // 如果选择了unit，更新后续行的unit选项
                if (unit) {
                    followingRows.forEach(async followingRow => {
                        const followingUnitSelect = followingRow.querySelector('select[name="shift-unit[]"]');
                        if (followingUnitSelect) {
                            try {
                                // 重新获取所有unit选项
                                //const response = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
                                const response = await fetch(`php/get_options.php?type=unit&section=${section}`);
                                const data = await response.json();
                                if (data.success) {
                                    followingUnitSelect.innerHTML = '<option value="">请选择</option>' + 
                                        data.data.map(U => `<option value="${U}" ${U === unit ? 'selected' : ''}>${U}</option>`).join('');
                                }
                            } catch (error) {
                                console.error('加载line选项失败:', error);
                            }
                            // 如果之前选中的值与新的line不同，触发change事件以更新unit
                            // followingLineSelect.dispatchEvent(new Event('change'));
                        }
                    });
                }

                // 如果unit未选择，加载该科室下所有line
                // const url = unit ? 
                //     //`php/get_options.php?type=line&section=${section}&unit=${unit}&project=${project}` :
                //     `php/get_options.php?type=line&section=${section}&unit=${unit}` :
                //     //`php/get_options.php?type=line&section=${section}&project=${project}`;
                //     `php/get_options.php?type=line&section=${section}`;
                
                // try {
                //     const response = await fetch(url);
                //     const data = await response.json();
                //     if (data.success) {
                //         lineSelect.innerHTML = '<option value="">请选择</option>' + 
                //             data.data.map(line => `<option value="${line}">${line}</option>`).join('');
                //     }
                // } catch (error) {
                //     console.error('加载line选项失败:', error);
                // }
            });

            // 添加班次选择
            // const classesSelect = row.querySelector('select[name="shift-classes[]"]');
            // classesSelect?.addEventListener('change', async () => {
            //     const classesName = classesSelect.value;
                
            //     // 获取当前行及其后面的所有行
            //     const tbody = row.parentElement;
            //     const allRows = Array.from(tbody.children);
            //     const currentRowIndex = allRows.indexOf(row);
            //     const followingRows = allRows.slice(currentRowIndex + 1);
                
            //     // 如果选择了班次className，更新后续行的className选项
            //     if (classesName==='LD') {
            //         followingRows.forEach(async followingRow => {
            //             const followingClassesSelect = followingRow.querySelector('select[name="shift-classes[]"]');
            //             if (followingClassesSelect) {
            //                 followingClassesSelect.innerHTML = '<option value="LD" selected>LD</option><option value="LG">LG</option>';
            //             }
            //         });
            //     }
            //     else if(classesName==='LG') {
            //         followingRows.forEach(async followingRow => {
            //             const followingClassesSelect = followingRow.querySelector('select[name="shift-classes[]"]');
            //             if (followingClassesSelect) {
            //                 followingClassesSelect.innerHTML = '<option value="LD">LD</option><option value="LG" selected>LG</option>';
            //             }
            //         });
            //     }

            //     // // 如果unit未选择，加载该科室下所有line
            //     // const url = unit ? 
            //     //     `php/get_options.php?type=line&section=${section}&unit=${unit}&project=${project}` :
            //     //     `php/get_options.php?type=line&section=${section}&project=${project}`;
                
            //     // try {
            //     //     const response = await fetch(url);
            //     //     const data = await response.json();
            //     //     if (data.success) {
            //     //         lineSelect.innerHTML = '<option value="">请选择</option>' + 
            //     //             data.data.map(line => `<option value="${line}">${line}</option>`).join('');
            //     //     }
            //     // } catch (error) {
            //     //     console.error('加载line选项失败:', error);
            //     // }
            // });

        } catch (error) {
            console.error('加载选项失败:', error);
        }
    }

    // 班组交接页面 - 提交表单
    async submitShiftForm(form) {
        try {
            const formData = new FormData();
            let validRowCount = 0;  // 记录有效行数
            // console.log('登录页面 - 提交表单 validRowCount:',validRowCount);

            // 获取用户科室
            const section = this.userInfo?.section || '';
            // 根据用户权限设置 project
            let project = this.getProject(this.userInfo.level);

            // 收集所有行的数据
            const tbody = document.getElementById('registerTableBody');
            const rows = tbody.children;
            for (let i = 0; i < rows.length; i++) {
                // 获取行中的所有输入值
                const classes = rows[i].querySelector('select[name="classes[]"]').value;
                const line = rows[i].querySelector('select[name="line[]"]').value;
                const unit = rows[i].querySelector('select[name="unit[]"]').value;
                const problemtype = rows[i].querySelector('select[name="problemtype[]"]').value;
                const phenomenon = rows[i].querySelector('textarea[name="phenomenon[]"]').value;
                const analysis = rows[i].querySelector('textarea[name="analysis[]"]').value;
                const measure = rows[i].querySelector('textarea[name="measure[]"]').value;
                const problempart = rows[i].querySelector('input[name="problempart[]"]').value;
                const problemcode = rows[i].querySelector('input[name="problemcode[]"]').value;
                const url = `php/get_options.php?type=project&section=${section}&line=${line}&unit=${unit}`;
                // 如果project没有值，根据Line Unit，添加project信息
                //if(project ==='')
                //    url = `php/get_options.php?type=project&section=${section}&line=${line}&unit=${unit}`;
                // console.log('登录页面 - url:',url);

                try {
                    // console.log('0');
                    const response = await fetch(url);
                    // console.log('11');
                    const data = await response.json();
                    // console.log('登录页面 - data:',data);
                    // console.log('登录页面 - data:',typeof data);
                    // console.log('登录页面 - data.data:',data.data);
                    // console.log('登录页面 - data:',typeof data.data);
                    // console.log('登录页面 - data.data[0]:',data.data[0]);
                    // console.log('登录页面 - data.data[0]:',typeof data.data[0]);
                    if (data.success) {
                        // console.log('222');
                        project = data.data[0];
                        // console.log('登录页面 - project:',project);
                    }
                } catch (error) {
                    console.error('登录获取project失败:', error);
                    alert('登录获取project失败:' + error.message);
                    exit;
                }

                // 只处理有必填字段的行
                if (line && unit && problemtype && phenomenon) {
                    // 添加行数据
                    formData.append('classes[]', classes);
                    formData.append('line[]', line);
                    formData.append('unit[]', unit);
                    formData.append('problemtype[]', problemtype);
                    formData.append('phenomenon[]', phenomenon);
                    formData.append('analysis[]', analysis);
                    formData.append('measure[]', measure);
                    formData.append('problempart[]', problempart);
                    formData.append('problemcode[]', problemcode);
                    
                    // 添加科室信息 + project信息
                    formData.append('section[]', this.userInfo?.section || '');
                    formData.append('project[]', project);

                    // 添加单选按钮值
                    const selectedRadio = rows[i].querySelector('input[type="radio"]:checked');
                    formData.append('needfollow[]', selectedRadio ? selectedRadio.value : '否');

                    // 根据是否需要跟进，给出status的值open、close
                    if(selectedRadio.value === '否'){
                        formData.append('status[]', 'close');
                    }else{
                        formData.append('status[]', 'open');
                    }

                    // 添加文件（如果有）
                    const fileInput = rows[i].querySelector('input[type="file"]');
                    if (fileInput && fileInput.files.length > 0) {
                        Array.from(fileInput.files).forEach(file => {
                            // 使用validRowCount而不是i作为索引
                            formData.append(`files[${validRowCount}][]`, file);
                        });
                    }
                    validRowCount++;  // 增加有效行计数
                }
            }

            // 添加当前用户作为recorder
            formData.append('recorder', this.userInfo?.name || '');

            // 读出 formData 中的 键值对
            for (let [key, value] of formData) { 
                console.log(`Key: ${key}, Value: ${value}`); 
            } 
            
            const response = await fetch('php/submit_shift_associate.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                alert('提交成功');
                form.reset();
                this.initializeTableRows();
                window.location.href = 'associate.html';
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('提交失败:', error);
            alert('提交失败: ' + error.message);
        }
    }
    //#endregion

    //#region 分界线：其他方法
    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: '2-digit',
            month: '2-digit',
            day: '2-digit'
        });
    }

    // 计算班次
    calculateShift(dateString) {
        if (!dateString) return '';
        try {
            const [datePart, timePart] = dateString.split(' ');
            if (!timePart) return '';
            const [hours, minutes] = timePart.split(':').map(Number);
            const time = hours + minutes / 60;
            if (time >= 7.5 && time < 19.5) {
                return "LD";
            } else {
                return "LG";
            }
        } catch (error) {
            console.error('时间解析错误:', error);
            return '';
        }
    }

    // 用于html语句的传值
    safeValue(value, defaultValue = '') {
        return value || defaultValue;
    }

    // 初始化模态框
    initModal() {
        this.modal = document.getElementById('associateDetailModal');
        if (!this.modal) return;

        // 关闭模态框的方法
        const closeModal = () => {
            this.modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        };

        // 绑定关闭按钮事件
        this.modal.querySelector('.close')?.addEventListener('click', closeModal);
        this.modal.querySelector('.btn-return')?.addEventListener('click', closeModal);

        // 点击模态框外部关闭
        this.modal.addEventListener('click', e => {
            if (e.target === this.modal) closeModal();
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', e => {
            if (e.key === 'Escape' && this.modal.style.display === 'block') {
                closeModal();
            }
        });

        
        
    }

    getProject(level){
        // 根据用户权限设置 project
        if(!level) return '';
        switch(this.userInfo?.level) {
            case 21:
                return 'OC';
            case 22:
                return 'LCM';
            case 23:
                return 'LOG';//物流
            case 20:
                return '';
            default:
                return '';
        }
    }
    //#endregion
}

// 创建实例
const pmAssociateManager = new PMAssociateManager();

// 页面加载完成后初始化查询表单
document.addEventListener('DOMContentLoaded', () => {
    pmAssociateManager.initializeSearchForm();
    pmAssociateManager.initializeAnalysisForm();
    // 自动加载第一页数据
    pmAssociateManager.fetchAssociateData();
}); 