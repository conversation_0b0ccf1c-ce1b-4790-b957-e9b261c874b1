<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    $sparepartId = isset($data['sparepart_id']) ? intval($data['sparepart_id']) : 0;
    $filePath = isset($data['file_path']) ? trim($data['file_path']) : '';
    
    if ($sparepartId <= 0 || empty($filePath)) {
        throw new Exception("无效的参数");
    }
    
    // 开始事务
    $conn->begin_transaction();
    
    // 删除数据库记录
    $stmt = $conn->prepare("DELETE FROM sparepart_files WHERE sparepart_id = ? AND file_path = ?");
    $stmt->bind_param('is', $sparepartId, $filePath);
    if (!$stmt->execute()) {
        throw new Exception("删除文件记录失败: " . $stmt->error);
    }
    
    // 提交事务
    $conn->commit();
    

    $physicalPath = '../uploads/spareparts/' . $filePath;
    if (file_exists($physicalPath)) {
        if (!unlink($physicalPath)) {
            error_log("无法删除物理文件: " . $physicalPath);
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => '文件已成功删除'
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->ping()) {
        $conn->rollback();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 