<?php
session_start();
header('Content-Type: application/json');

// 设置时区为中国标准时间
date_default_timezone_set('Asia/Shanghai');

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);

// 添加调试日志
error_log('Received data: ' . print_r($data, true));

$message = trim($data['message'] ?? '');
$response = trim($data['response'] ?? '');
$username = $data['username'] ?? $_SESSION['username'] ?? 'anonymous';
$frontendTimestamp = $data['timestamp'] ?? null;

// 使用前端时间戳或服务器时间
$timestamp = $frontendTimestamp ? 
    date('Y-m-d H:i:s', strtotime($frontendTimestamp)) : 
    date('Y-m-d H:i:s');

// 只有当 message 或 response 有内容时才记录日志
if (!empty($message) || !empty($response)) {
    // 构建日志条目 - 只记录有内容的部分
    $logParts = [];
    $logParts[] = $timestamp;
    $logParts[] = "用户: " . $username;
    
    if (!empty($message)) {
        $logParts[] = "发送: " . $message;
    }
    
    if (!empty($response)) {
        $logParts[] = "接收: " . $response;
    }
    
    $logEntry = implode(" | ", $logParts) . "\n";

    // 指定日志文件路径，使用当前日期作为文件名
    $today = date('Y-m-d');
    $logFile = __DIR__ . '/../logs/' . $today . '_chat_logs.txt';

    // 确保日志目录存在
    $logDir = __DIR__ . '/../logs';
    if (!file_exists($logDir)) {
        mkdir($logDir, 0777, true);
    }

    // 写入日志
    if (file_put_contents($logFile, $logEntry, FILE_APPEND)) {
        error_log('Successfully wrote to log file: ' . $logFile);
        echo json_encode(['success' => true]);
    } else {
        error_log('Failed to write to log file: ' . $logFile);
        echo json_encode(['success' => false, 'message' => 'Failed to write log']);
    }
} else {
    // 如果没有有效内容，返回成功但不记录
    echo json_encode(['success' => true, 'message' => 'No content to log']);
}
?> 
