﻿<?php 
// 配置SQL Server连接信息 
$sqlsrv_server = '109.120.2.35'; 
$sqlsrv_db = 'bigdata'; 
$sqlsrv_user = 'epqlink'; 
$sqlsrv_pass = 'eqplink'; 
// 配置MySQL连接信息 
$mysql_server = '109.120.2.35'; 
$mysql_db = 'eap_management'; 
$mysql_user = 'eqpmanage'; 
$mysql_pass = 'eqpmanage'; 
// 连接SQL Server 
$sqlsrv_conn = sqlsrv_connect(
  $sqlsrv_server, array(
     "Database" => $sqlsrv_db,
    "UID" => $sqlsrv_user, 
    "PWD" => $sqlsrv_pass )); 
    if (!$sqlsrv_conn) { 
      die("SQL Server连接失败: " . print_r(sqlsrv_errors(), true)); } 
      // 执行查询 
      $sql = "SELECT * FROM [bigdata].[dbo].[GLASS_ID]"; 
      $stmt = sqlsrv_query($sqlsrv_conn, $sql); 
      if ($stmt === false) { 
        die("查询失败: " . print_r(sqlsrv_errors(), true)); } 
        // 连接MySQL 
        $mysql_conn = new mysqli($mysql_server, $mysql_user, $mysql_pass, $mysql_db); 
        if ($mysql_conn->connect_error) { 
          die("MySQL连接失败: " . $mysql_conn->connect_error); } 
          // 获取列名（假设目标表结构相同） 
          $columns = array(); 
          $result = sqlsrv_field_metadata($stmt); 
          foreach ($result as $field) { $columns[] = $field['Name']; } 
          $placeholders = implode(',', array_fill(0, count($columns), '?')); 
          $insert_sql = "INSERT INTO glass_id (" . implode(',', $columns) . ") VALUES ($placeholders)"; 
          $mysql_stmt = $mysql_conn->prepare($insert_sql); 
          if (!$mysql_stmt) { 
            die("准备插入语句失败: " . $mysql_conn->error); } 
            // 动态绑定参数类型 
            $types = ''; 
            $params = array(); 
            while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) { 
              foreach ($row as $key => $value) { 
                // 处理日期时间对象 
                if ($value instanceof DateTime) { 
                  $row[$key] = $value->format('Y-m-d H:i:s'); } 
                  // 处理NULL值 
                  if ($value === null) { $row[$key] = NULL; } } 
                  // 绑定参数 
                  $types = str_repeat('s', count($row)); 
                  $mysql_stmt->bind_param($types, ...array_values($row)); 
                  if (!$mysql_stmt->execute()) { 
                    echo "插入失败: " . $mysql_stmt->error . "\n"; } } 
                    // 关闭连接 
                    sqlsrv_free_stmt($stmt); 
                    sqlsrv_close($sqlsrv_conn); 
                    $mysql_stmt->close(); 
                    $mysql_conn->close(); 
                    echo "数据复制完成！"; 

?>