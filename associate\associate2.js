﻿// 现场交接管理类
class AssociateManager {
    constructor() {
        this.initializeEventListeners();
        this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.modal = null;
        this.associateData = [];
        this.curData=null;
        this.historyData=null;
        this.bShowHistory=false;    // 控制历史履历信息的显示与隐藏
        this.analysisData = [];     // 获取搜索的分析数据
        this.chartType = 'bar';     // 图表显示类型
        this.shiftDateArr=[];       // shift交接查询 日期数组
        this.currentShiftPage = 1;
        this.pageShiftSize = 1;
        this.totalShiftPages = 1;
        // 控制业务选项的显示
        const towhoContainer = document.getElementById('towhoContainer');
        if (towhoContainer && this.userInfo?.account === '268552') { // 王俊
            towhoContainer.style.display = 'inline-block';
        }
    }
    
    // 初始化所有事件监听器
    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeTableRows();     // 初始化 交接登录 表格行
            this.loadSearchOptions();       // 加载搜索选项数据
            this.initModal();               // 初始化模态框
        });
    }
    //#region 分界线：>>交接登录<<
    // 登陆页面 - 初始化 交接登录 表格行
    initializeTableRows() {
        // 更新领班信息 —— 登录界面
        const shiftLeader = document.getElementById("assShift-area");
        if(this.userInfo.section === '偏贴实装一科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="沈阳">沈阳</option>
                <option value="张宝龙">张宝龙</option>
                <option value="王宪坤">王宪坤</option>
                <option value="张帅">张帅</option> 
                <option value="钱鹏">钱鹏</option> 
                <option value="赵亚洲">赵亚洲</option> 
            `;
        }
        else if(this.userInfo.section === '偏贴实装二科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="卞伟">卞伟</option>
                <option value="汪莹">汪莹</option>
                <option value="屈锦">屈锦</option>
                <option value="杨治辉">杨治辉</option> 
                <option value="彭娅">彭娅</option> 
                <option value="王晓勇">王晓勇</option> 
            `;
        }
        else if(this.userInfo.section === '偏贴实装三科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="严瑞瑞">严瑞瑞</option>
                <option value="卢力">卢力</option>
                <option value="房华">房华</option>
                <option value="韩飞">韩飞</option> 
                <option value="朱金峰">朱金峰</option> 
                <option value="方银兵">方银兵</option> 
            `;
        }
        else if(this.userInfo.section === '检测一科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="黄辉辉">黄辉辉</option>
                <option value="黄俊华">黄俊华</option>
                <option value="滕维银">滕维银</option>
                <option value="周家柱">周家柱</option> 
                <option value="杨明福">杨明福</option> 
                <option value="踪阁">踪阁</option> 
            `;
        }
        else if(this.userInfo.section === '检测二科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="陈洁">陈洁</option>
                <option value="蒋浩浩">蒋浩浩</option>
                <option value="刘豪琰">刘豪琰</option>
                <option value="马贤瑞">马贤瑞</option> 
                <option value="张天明">张天明</option> 
                <option value="车赛">车赛</option> 
            `;
        }
        else if(this.userInfo.section === '中板切科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="黄盅盅">黄盅盅</option>
                <option value="岳成波">岳成波</option>
                <option value="程龙">程龙</option>
            `;
        }

        const tbody = document.getElementById("registerTableBody");
        if (!tbody) return;
        
        // 清空现有行
        tbody.innerHTML = '';
        
        // 添加10行空表格
        for (let i = 0; i < 5; i++) {
            this.addNewRow(tbody);
        }

        // 绑定添加更多行按钮事件
        const addRowsBtn = document.querySelector('.btn-add-rows');
        if (addRowsBtn) {
            addRowsBtn.addEventListener('click', () => {
                for (let i = 0; i < 5; i++) {
                    this.addNewRow(tbody);
                    // 初始化单选框事件,这里初始化了单选事件，其他事件也需要添加
                    this.initRadioButtonChangeEvent(tbody);
                }
            });
        }

        // 绑定表单提交事件
        const form = document.querySelector('.assregister-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();//阻止连接默认的跳转行为
                this.submitForm(form);
            });
        }

        // 初始化单选框事件
        this.initRadioButtonChangeEvent(tbody);
    }

    // 登陆页面 - 添加新行
    addNewRow(tbody) {
        const row = document.createElement('tr');
        const rowIndex = tbody.children.length;
        
        row.innerHTML = `
            <td>
                <select name="unit[]" required>
                    <option value="">请选择</option>
                </select>
            </td>
            <td>
                <select name="line[]" required>
                    <option value="">请选择</option>
                </select>
            </td>
            
            <td>
                <select name="problemtype[]" required>
                    <option value="故障-硬件" selected>故障-硬件</option>
                    <option value="故障-程序">故障-程序</option>
                    <option value="品质调试">品质调试</option>
                    <option value="切机">切机</option>
                    <option value="Tact Time">Tact Time</option>
                    <option value="原材不良">原材不良</option>
                    <option value="HD">HD</option>
                    <option value="其他">其他</option>
                </select>
            </td>
            <td>
                <textarea name="phenomenon[]" rows="2" required></textarea>
            </td>
            <td>
                <textarea name="analysis[]" rows="2"></textarea>
            </td>
            <td>
                <textarea name="measure[]" rows="2"></textarea>
            </td>
            <td>
                <div class="radio-group">
                    <label>
                        <input class="radio-follow" type="radio" name="needfollow_${rowIndex}" value="是" id="needfollowyes_${rowIndex}" >
                        <span>是</span>
                    </label>
                    <label>
                        <input class="radio-follow" type="radio" name="needfollow_${rowIndex}" value="否" checked id="needfollowno_${rowIndex}" >
                        <span>否</span>
                    </label>
                </div>
            </td>
            <td>
                <select name="towho[]" id="toWho_${rowIndex}" disabled>
                ${this.userInfo.section === '偏贴实装二科' ?
                    `
                    <option value="">请选择</option>
                    <option value="严先维">严先维</option><option value="项腾飞">项腾飞</option>
                    <option value="朱庆凯">朱庆凯</option><option value="胡文平">胡文平</option>
                    <option value="邢丹">邢丹</option><option value="袁阳光">袁阳光</option>
                    <option value="王振">王振</option><option value="赵伦">赵伦</option>
                    <option value="沈凯雷">沈凯雷</option><option value="陈振振">陈振振</option>
                    <option value="庞海龙">庞海龙</option><option value="左伟">左伟</option>
                    <option value="卞向宇">卞向宇</option><option value="杜志伟">杜志伟</option>
                    <option value="杨鑫">杨鑫</option><option value="苟振福">苟振福</option>
                    <option value="马庆森">马庆森</option><option value="徐德松">徐德松</option>
                    <option value="郁程阳">郁程阳</option><option value="朱从海">朱从海</option>
                    <option value="李杰">李杰</option><option value="冯殿超">冯殿超</option>
                    <option value="薛欢">薛欢</option>
                    <option value="Line Leader">Line Leader</option><option value="PM">PM</option>
                    <option value="O">O</option><option value="A">A</option>
                    <option value="B">B</option><option value="C">C</option>
                    ` 
                    : 
                    `<option value="">请选择</option>
                    <option value="非PM">非PM</option>
                    <option value="PM">PM</option>`}
                </select>
            </td>
            <td>
                <div class="radio-group">
                    <label>
                        <input class="radio-alarm" type="radio" name="isBigAlarm_${rowIndex}" value="是" id="isBigAlarm_yes_${rowIndex}" >
                        <span>是</span>
                    </label>
                    <label>
                        <input class="radio-alarm" type="radio" name="isBigAlarm_${rowIndex}" value="否" checked id="isBigAlarm_no_${rowIndex}" >
                        <span>否</span>
                    </label>
                </div>
            </td>
            <td>
                <div>
                    <input type="file" id="fileUpload-${rowIndex}" name="files[]" multiple 
                        accept="image/jpeg,image/png,image/gif" 
                        style="width: 100px;"
                        onchange="associateManager.handleFileSelect2(event, ${rowIndex})">
                </div>
                <div class="file-list" id="fileList-${rowIndex}"></div>
            </td>
            <td class="action-column">
                <button type="button" class="btn-delete-row" onclick="associateManager.deleteRow(this)">
                    删除
                </button>
                <button type="button" class="btn-to-big-alarm" id="btn-to-big-alarm-${rowIndex}" onclick="associateManager.toBigAlarm(this)" style="display:none">
                    大故障
                </button>
            </td>
        `;

        tbody.appendChild(row);

        // 加载该行的选项数据
        this.loadRowOptions(row);

        // 初始化文件上传处理方法
        // this.initFileUpload();
    }
    
    // 添加跳转大故障登录的方法
    toBigAlarm(button) {
        if(!confirm('您确定跳转到大故障登录界面码?')){
            return;
        }
        const row = button.closest('tr');
        if (row) {
            // 创建一个超链接，触发它，跳转到大故障页面
            // const a_bigAlarm = document.createElement('a');
            // a_bigAlarm.href="fault.html";
            // a_bigAlarm.click();

            console.log('跳转中。。。');
            // 构建带锚点的 URL 
			const targetUrl = 'fault.html?tab=register'; 
			// 进行页面跳转 
			window.location.href = targetUrl; 
        }
    }

    // 初始化单选框事件
    initRadioButtonChangeEvent(tbody){
        const rowIndex = tbody.children.length;
        const rowCount = tbody.rows.length;

        for(var i=0; i<rowCount; i++){
            // 获取初始单选框元素，为needfollow单选框添加事件
            var yesRadio = document.getElementById(`needfollowyes_${i}`); 
            var noRadio = document.getElementById(`needfollowno_${i}`); 
        
            const toWho = document.getElementById(`toWho_${i}`); 

            // 监听初始单选框的 change 事件 
            yesRadio?.addEventListener('change', function () { 
                if (this.checked) { 
                    toWho.removeAttribute('disabled'); 
                    toWho.setAttribute('required',true);
                } 
            }); 
            noRadio?.addEventListener('change', function () { 
                if (this.checked) { 
                    toWho.removeAttribute('disabled'); 
                    toWho.setAttribute('disabled',true); 
                    
                } 
            });
            
            // 获取初始单选框元素，为isBigAlarm单选框添加事件
            var yesBigAlarmRadio = document.getElementById(`isBigAlarm_yes_${i}`); 
            var noBigAlarmRadio = document.getElementById(`isBigAlarm_no_${i}`); 
        
            const btn = document.getElementById(`btn-to-big-alarm-${i}`); 

            // 监听初始单选框的 change 事件 
            yesBigAlarmRadio?.addEventListener('change', function () { 
                if (this.checked) { 
                    btn.style.display="inline-block";
                } 
            }); 
            noBigAlarmRadio?.addEventListener('change', function () { 
                if (this.checked) { 
                    btn.style.display="none";
                } 
            });

            // 获取Unit下拉框元素，为其添加事件
            // var unitSelect = document.getElementById(`unit_${i}`); 
            // unitSelect?.addEventListener('change', async () => {

            // });
        }
    }

    // 初始化文件上传处理方法
    initFileUpload() {
        const fileUpload = document.getElementById('fileUpload');
        const fileList = document.querySelector('.file-list');
        
        if (!fileUpload || !fileList) return;

        // 存储已选择的文件
        let selectedFiles = new Map();

        fileUpload.addEventListener('change', (e) => {
            const newFiles = Array.from(e.target.files);
            
            // 添加新选择的文件到已有文件列表中
            newFiles.forEach(file => {
                const fileId = Date.now() + '-' + file.name;
                selectedFiles.set(fileId, file);
                addFileToList(fileId, file);
            });

            // 清空input，允许重复选择相同文件
            fileUpload.value = '';
        });

                // 添加文件到列表的函数
                const addFileToList = (fileId, file) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span class="file-name" data-file-id="${fileId}">${file.name}</span>
                        <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                        <button type="button" class="btn-delete-file" data-file-id="${fileId}">×</button>
                    `;
        
                    // 添加文件名点击事件（预览）
                    const fileName = fileItem.querySelector('.file-name');
                    fileName.addEventListener('click', () => {
                        previewFile(file);
                    });
        
                    // 添加删除按钮事件
                    const deleteBtn = fileItem.querySelector('.btn-delete-file');
                    deleteBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        selectedFiles.delete(fileId);
                        fileItem.remove();
                    });
        
                    fileList.appendChild(fileItem);
                };
        
                // 文件预览函数
                const previewFile = (file) => {
                    // 如果是图片，创建预览
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            const previewWindow = window.open('', '_blank');
                            previewWindow.document.write(`
                                <img src="${e.target.result}" style="max-width: 100%; height: auto;">
                            `);
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // 对于其他类型的文件，尝试在新窗口中打开
                        const fileUrl = URL.createObjectURL(file);
                        window.open(fileUrl, '_blank');
                    }
                };

        // // 表单提交处理
        // const form = document.querySelector('.assregister-form');
        // if (form) {
        //     form.onsubmit = async (e) => {
        //         e.preventDefault();
                
        //         try {
        //             const formData = new FormData(form);
                    
        //             // 添加用户科室数据
        //             if (this.userInfo?.section) {
        //                 formData.set('section', this.userInfo.section);
        //             }
                    
        //             // 添加文件
        //             if (selectedFiles.size > 0) {
        //                 selectedFiles.forEach((file) => {
        //                     formData.append('files[]', file);
        //                 });
        //             }

        //             // 发送请求
        //             const response = await fetch('php/submit_fault.php', {
        //                 method: 'POST',
        //                 body: formData
        //             });

        //             const result = await response.json();
        //             if (result.success) {
        //                 alert('故障信息已成功添加');
        //                 form.reset();
        //                 fileList.innerHTML = '';
        //                 selectedFiles.clear();
        //                 this.loadFaultList();
                        
        //                 // 重新设置科室和记录人
        //                 this.initFaultRegisterForm();
        //             } else {
        //                 throw new Error(result.message);
        //             }
        //         } catch (error) {
        //             console.error('提交错误:', error);
        //             alert('提交出错：' + error.message);
        //         }
        //     };
        // }
    }

    // 加载行选项数据
    async loadRowOptions(row) {
        try {
            // 获取用户科室
            const section = this.userInfo?.section || '';
            
            // 根据用户权限设置 project
            const project = this.getProject(this.userInfo.level);

            // 加载LINE选项
            const lineSelect = row.querySelector('select[name="line[]"]');
            const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}&project=${project}`);
            const lineData = await lineResponse.json();
            if (lineData.success) {
                lineSelect.innerHTML = '<option value="">请选择</option>' + 
                    lineData.data.map(line => `<option value="${line}">${line}</option>`).join('');
            }

            // 加载UNIT选项
            const unitSelect = row.querySelector('select[name="unit[]"]');
            const unitResponse = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
            const unitData = await unitResponse.json();
            if (unitData.success) {
                unitSelect.innerHTML = '<option value="">请选择</option>' + 
                    unitData.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
            }

            // 添加Unit变化时更新unit的监听并更新Line的选项
            unitSelect?.addEventListener('change', async () => {
                const unit = unitSelect.value;
                
                // 获取当前行及其后面的所有行
                const tbody = row.parentElement;
                const allRows = Array.from(tbody.children);
                const currentRowIndex = allRows.indexOf(row);
                // const followingRows = allRows.slice(currentRowIndex + 1);
                const followingRows = allRows.slice(currentRowIndex);
                
                // 如果选择了unit，更新后续行的unit选项
                if (unit) {
                    followingRows.forEach(async followingRow => {
                        var followingUnitSelect = followingRow.querySelector('select[name="unit[]"]');
                        if (followingUnitSelect) {
                            try {
                                // 重新获取所有unit选项
                                const response = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
                                const data = await response.json();
                                if (data.success) {
                                    followingUnitSelect.innerHTML = '<option value="">请选择</option>' + 
                                        data.data.map(U => `<option value="${U}" ${U === unit ? 'selected' : ''}>${U}</option>`).join('');
                                }
                            } catch (error) {
                                console.error('加载line选项失败:', error);
                            }
                            // 如果之前选中的值与新的line不同，触发change事件以更新unit
                            // followingLineSelect.dispatchEvent(new Event('change'));
                        }
                        
                        // 如果unit未选择，加载该科室下所有line
                        // const lineSelect = row.querySelector('select[name="line[]"]');
                        // const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}&project=${project}`);
                        // const lineData = await lineResponse.json();
                        // if (lineData.success) {
                        //     lineSelect.innerHTML = '<option value="">请选择</option>' + 
                        //         lineData.data.map(line => `<option value="${line}">${line}</option>`).join('');
                        // }
                        var followingLineSelect = followingRow.querySelector('select[name="line[]"]');
                        const url = unit ? 
                            `php/get_options.php?type=line&section=${section}&unit=${unit}&project=${project}` :
                            `php/get_options.php?type=line&section=${section}&project=${project}`;
                        
                        try {
                            const response = await fetch(url);
                            const data = await response.json();
                            if (data.success) {
                                followingLineSelect.innerHTML = '<option value="">请选择</option>' + 
                                    data.data.map(line => `<option value="${line}">${line}</option>`).join('');
                            }
                        } catch (error) {
                            console.error('加载line选项失败:', error);
                        }
                    });
                }

                // 如果unit未选择，加载该科室下所有line
                // const url = unit ? 
                //     `php/get_options.php?type=line&section=${section}&unit=${unit}&project=${project}` :
                //     `php/get_options.php?type=line&section=${section}&project=${project}`;
                
                // try {
                //     const response = await fetch(url);
                //     const data = await response.json();
                //     if (data.success) {
                //         lineSelect.innerHTML = '<option value="">请选择</option>' + 
                //             data.data.map(line => `<option value="${line}">${line}</option>`).join('');
                //     }
                // } catch (error) {
                //     console.error('加载line选项失败:', error);
                // }
            });

            // 添加班次选择
            // const classesSelect = row.querySelector('select[name="classes[]"]');
            // classesSelect?.addEventListener('change', async () => {
            //     const classesName = classesSelect.value;
                
            //     // 获取当前行及其后面的所有行
            //     const tbody = row.parentElement;
            //     const allRows = Array.from(tbody.children);
            //     const currentRowIndex = allRows.indexOf(row);
            //     const followingRows = allRows.slice(currentRowIndex + 1);
                
            //     // 如果选择了班次className，更新后续行的className选项
            //     if (classesName==='LD') {
            //         followingRows.forEach(async followingRow => {
            //             const followingClassesSelect = followingRow.querySelector('select[name="classes[]"]');
            //             if (followingClassesSelect) {
            //                 followingClassesSelect.innerHTML = '<option value="LD" selected>LD</option><option value="LG">LG</option>';
            //             }
            //         });
            //     }
            //     else if(classesName==='LG') {
            //         followingRows.forEach(async followingRow => {
            //             const followingClassesSelect = followingRow.querySelector('select[name="classes[]"]');
            //             if (followingClassesSelect) {
            //                 followingClassesSelect.innerHTML = '<option value="LD">LD</option><option value="LG" selected>LG</option>';
            //             }
            //         });
            //     }
            // });

        } catch (error) {
            console.error('加载选项失败:', error);
        }
    }

    // 处理文件选择
    handleFileSelect(event, rowIndex) {
        const files = Array.from(event.target.files);
        const fileListDiv = document.getElementById(`fileList-${rowIndex}`);
        const fileInput = event.target;
        
        if (!fileListDiv) return;

        // 验证文件类型
        const invalidFiles = files.filter(file => !file.type.match(/^image\/(jpeg|png|gif)$/));
        if (invalidFiles.length > 0) {
            alert('只能上传JPG、PNG或GIF格式的图片文件！');
            fileInput.value = ''; // 清空选择
            fileListDiv.innerHTML = '';
            return;
        }

        // onclick="associateManager.previewFile(${rowIndex}, '${file.name}"
        // 显示文件列表 —— 重复上传图片，只保留最近一次的图片
        fileListDiv.innerHTML = files.map(file => `
            <div class="file-item">
                <span class="file-name" >${file.name}</span>
                <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                <button type="button" class="btn-delete-file" 
                        onclick="associateManager.removeFile(${rowIndex}, '${file.name}', this)">×</button>
            </div>
        `).join('');
    }

    // 处理文件选择2.0 —— 
    handleFileSelect2(event, rowIndex) {
        const fileUpload = document.getElementById(`fileUpload-${rowIndex}`);

        // 添加文件到列表的函数2.0
        const addFileToList2 = (fileId,file) =>{
            const fileItem = document.createElement('div');
            fileItem.className = `file-item-${rowIndex}`;
            fileItem.innerHTML=`
                <span class="file-name needPreview" data-file-id="${fileId}">${file.name}</span>
                <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                <button type="button" class="btn-delete-file" data-file-id="${fileId}">×</button>
            `;

            // 添加文件名点击事件（预览）
            const fileName = fileItem.querySelector('.file-name');
            fileName.addEventListener('click',()=>{
                previewFile2(file);
            })

            // 添加删除按钮事件
            const deleteBtn = fileItem.querySelector('.btn-delete-file');
            deleteBtn.addEventListener('click',(e)=>{
                e.stopPropagation();
                selectedFiles.delete(fileId);
                fileItem.remove();

                fileUpload.value='';
            });

            fileListDiv.appendChild(fileItem);
        }
        
        // 内部函数 - 文件预览函数
        const previewFile2 = (file) => {
            // 如果是图片，创建预览
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const previewWindow = window.open('', '_blank');
                    previewWindow.document.write(`
                        <img src="${e.target.result}" style="max-width: 100%; height: auto;">
                    `);
                };
                reader.readAsDataURL(file);
            } else {
                // 对于其他类型的文件，尝试在新窗口中打开
                const fileUrl = URL.createObjectURL(file);
                window.open(fileUrl, '_blank');
            }
        };

        const fileListDiv = document.getElementById(`fileList-${rowIndex}`);

        if (!fileListDiv) return;

        // 存储已选择的文件
        let selectedFiles = new Map();

        const files = Array.from(event.target.files);
        const fileInput = event.target;

        // 验证文件类型
        const invalidFiles = files.filter(file => !file.type.match(/^image\/(jpeg|png|gif)$/));
        if (invalidFiles.length > 0) {
            alert('只能上传JPG、PNG或GIF格式的图片文件！');
            fileInput.value = ''; // 清空选择
            fileListDiv.innerHTML = '';
            return;
        }
        files.forEach(file =>{
            const fileId = Date.now() + '-' + rowIndex + '-' + file.name;
            selectedFiles.set(fileId,file);
            addFileToList2(fileId,file);
        });

            
    }
    
    // 移除文件
    removeFile(rowIndex, fileName, button) {
        const fileInput = document.querySelector(`input[name="files[]"][onchange*="${rowIndex}"]`);
        const fileListDiv = document.getElementById(`fileList-${rowIndex}`);
        
        button.closest('.file-item').remove();
        
        if (fileListDiv.children.length === 0) {
            fileInput.value = '';
        }
    }
   
    // 登录页面 - 提交表单
    async submitForm(form) {
        try {
            const formData = new FormData();
            let validRowCount = 0;  // 记录有效行数
            console.log('登录页面 - 提交表单 validRowCount:',validRowCount);

            // 获取班组人力信息
            const shiftLeader = document.getElementById('assShift-area').value;  // 班组
            const shift = document.getElementById('assShift-shift').value;  // 班组
            const classes = document.getElementById('assShift-classes').value;  // 班次
            const manCount = document.getElementById('assShift-manCount').value; // 应到人数
            const manCountActual = document.getElementById('assShift-manCountActual').value;  // 实到人数
            const manCountExtra = document.getElementById('assShift-manCountExtra').value;  // 加班人数

            console.log('manCount:',manCount,',type:',typeof manCount);

            // 获取用户科室
            const section = this.userInfo?.section || '';
            // 根据用户权限设置 project
            let project = this.getProject(this.userInfo.level);

            // 收集所有行的数据
            const tbody = document.getElementById('registerTableBody');
            const rows = tbody.children;
            for (let i = 0; i < rows.length; i++) {
                // 获取行中的所有输入值
                // const classes = rows[i].querySelector('select[name="classes[]"]').value;
                const line = rows[i].querySelector('select[name="line[]"]').value;
                const unit = rows[i].querySelector('select[name="unit[]"]').value;
                const problemtype = rows[i].querySelector('select[name="problemtype[]"]').value;
                const phenomenon = rows[i].querySelector('textarea[name="phenomenon[]"]').value;
                const analysis = rows[i].querySelector('textarea[name="analysis[]"]').value;
                const measure = rows[i].querySelector('textarea[name="measure[]"]').value;
                // const problempart = rows[i].querySelector('input[name="problempart[]"]').value;
                // const problemcode = rows[i].querySelector('input[name="problemcode[]"]').value;
                const radioFollows = rows[i].querySelectorAll('.radio-follow');
                const towho = rows[i].querySelector('select[name="towho[]"]').value;
                const radioAlarms = rows[i].querySelectorAll('.radio-alarm');
                
                console.log('radioFollows[0].checked',radioFollows[0].checked,
                    'radioFollows[0].value',radioFollows[0].value);

                // 根据Line Unit，添加project信息
                const url = `php/get_options.php?type=project&section=${section}&line=${line}&unit=${unit}`;
                try {
                    // console.log('0');
                    const response = await fetch(url);
                    // console.log('11');
                    const data = await response.json();
                    // console.log('登录页面 - data:',data);
                    if (data.success) {
                        project = data.data[0];
                    }
                } catch (error) {
                    console.error('登录获取project失败:', error);
                    alert('登录获取project失败:' + error.message);
                    exit;
                }

                // 只处理有必填字段的行
                if (line && unit && problemtype && phenomenon) {
                    // 添加行数据
                    formData.append('line[]', line);
                    formData.append('unit[]', unit);
                    formData.append('problemtype[]', problemtype);
                    formData.append('phenomenon[]', phenomenon);
                    formData.append('analysis[]', analysis);
                    formData.append('measure[]', measure);
                    // formData.append('problempart[]', problempart);
                    // formData.append('problemcode[]', problemcode);
                    formData.append('towho[]', towho);
                    
                    // 添加project信息
                    formData.append('project[]', project);

                    // 添加单选按钮值
                    formData.append('needfollow[]', radioFollows[0].checked ? radioFollows[0].value : '否');
                    formData.append('isbigalarm[]', radioAlarms[0].checked ? radioAlarms[0].value : radioAlarms[1].value);

                    // 根据是否需要跟进，给出status的值open、close
                    if(radioFollows[0].checked === false){
                        formData.append('status[]', 'close');
                    }else{
                        formData.append('status[]', 'open');
                    }

                    // 添加文件（如果有）
                    const fileInput = rows[i].querySelector('input[type="file"]');
                    if (fileInput && fileInput.files.length > 0) {
                        Array.from(fileInput.files).forEach(file => {
                            // 使用validRowCount而不是i作为索引
                            formData.append(`files[${validRowCount}][]`, file);
                        });
                    }
                    validRowCount++;  // 增加有效行计数
                }
            }

            // 添加当前用户作为recorder
            formData.append('recorder', this.userInfo?.name || '');

            // 添加科室信息
            formData.append('section', this.userInfo?.section || '');
            
            formData.append('classes', classes);

            // 添加人力信息
            formData.append('date', this.dateToNewDate(new Date(),classes));
            formData.append('shiftLeader', shiftLeader);
            formData.append('shift', shift);
            formData.append('manCount', manCount);
            formData.append('manCountActual', manCountActual);
            formData.append('manCountExtra', manCountExtra);


            // 读出 formData 中的 键值对
            for (let [key, value] of formData) { 
                console.log(`Key: ${key}, Value: ${value}`); 
            } 

            const response = await fetch('associate/submit_associate.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                alert('提交成功');
                form.reset();
                this.initializeTableRows();
                window.location.href = 'associate2.html';
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('提交失败:', error);
            alert('提交失败: ' + error.message);
        }
    }

    // 添加删除行的方法
    deleteRow(button) {
        const row = button.closest('tr');
        if (row) {
            // 获取tbody中的行数
            const tbody = row.parentElement;
            if (tbody.children.length > 1) {  // 确保至少保留一行
                row.remove();
                // 重新排序其他行的radio name属性
                this.reorderRows(tbody);
            } else {
                alert('至少需要保留一行');
            }
        }
    }

    // 重新排序行的方法 - 带索引的元素都需要重新排序 - 重新排序后，事件也应该重新添加
    reorderRows(tbody) {
        Array.from(tbody.children).forEach((row, index) => {
            // 更新radio按钮的name属性
            const radioFollows = row.querySelectorAll('.radio-follow');
            radioFollows.forEach(follow => {
                follow.name = `needfollow_${index}`;
                if(follow.value === '是'){
                    follow.id=`needfollowyes_${index}`;
                }
                if(follow.value === '否'){
                    follow.id=`needfollowno_${index}`;
                }
            });

            //name="towho[]" id="toWho_${rowIndex}"
            const toWho=row.querySelector('select[name="towho[]"]');
            if(toWho){
                toWho.id = `toWho_${index}`;
            }

            const radioAlarms = row.querySelectorAll('.radio-alarm');
            radioAlarms.forEach(alarm => {
                alarm.name = `isBigAlarm_${index}`;
                if(alarm.value === '是'){
                    alarm.id=`isBigAlarm_yes_${index}`;
                }
                if(alarm.value === '否'){
                    alarm.id=`isBigAlarm_no_${index}`;
                }
            });
            
            // 更新文件上传相关的属性
            const fileInput = row.querySelector('input[type="file"]');
            if (fileInput) {
                fileInput.id=`fileUpload-${index}`;
                fileInput.setAttribute('onchange', `associateManager.handleFileSelect(event, ${index})`);
            }
            
            const fileList = row.querySelector('.file-list');
            if (fileList) {
                fileList.id = `fileList-${index}`;
            }

            // 按钮 class="btn-to-big-alarm" id="btn-to-big-alarm-${rowIndex}"
            const btnToAlarm = row.querySelector('.btn-to-big-alarm');
            if (btnToAlarm) {
                btnToAlarm.id = `btn-to-big-alarm--${index}`;
            }
        });
    }
    //#endregion
    
    //#region 分界线：>>交接查询<<
    // 单选按钮事件添加函数 —— 显示类型单选变化控制班次选择
    handleRadioChange(ardio1,ardio2,ardio3,div){
        const handleRadio1Change = ()=>{
            if(ardio1.checked){
                console.log('ardio1 checked');
                // div.style.display = 'inline-block';
                div.style.display = 'none';
            }
        }
        const handleRadio2Change = ()=>{
            if(ardio2.checked){
                console.log('ardio2 checked');
                div.style.display = 'inline-block';
                // div.style.display = 'none';
            }
        }
        const handleRadio3Change = ()=>{
            if(ardio3.checked){
                console.log('ardio3 checked');
                // div.style.display = 'inline-block';
                div.style.display = 'none';
            }
        }
        // 为单选添加事件监听器
        if(ardio1){
            ardio1.addEventListener('change',handleRadio1Change);
        }
        if(ardio2){
            ardio2.addEventListener('change',handleRadio2Change);
        }
        if(ardio3){
            ardio3.addEventListener('change',handleRadio3Change);
        }
    }

    // 查询页面 - 初始化查询页面 添加查询相关的方法
    initializeSearchForm() {
        // 初始化LINE和UNIT选项
        this.loadSearchOptions();
        
        // 更新领班信息 —— 查询界面
        const shiftLeader = document.getElementById("asssearch-area");
        if(this.userInfo.section === '偏贴实装一科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="沈阳">沈阳</option>
                <option value="张宝龙">张宝龙</option>
                <option value="王宪坤">王宪坤</option>
                <option value="张帅">张帅</option> 
                <option value="钱鹏">钱鹏</option> 
                <option value="赵亚洲">赵亚洲</option> 
            `;
        }
        else if(this.userInfo.section === '偏贴实装二科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="卞伟">卞伟</option>
                <option value="汪莹">汪莹</option>
                <option value="屈锦">屈锦</option>
                <option value="杨治辉">杨治辉</option> 
                <option value="彭娅">彭娅</option> 
                <option value="王晓勇">王晓勇</option> 
            `;
        }
        else if(this.userInfo.section === '偏贴实装三科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="严瑞瑞">严瑞瑞</option>
                <option value="卢力">卢力</option>
                <option value="房华">房华</option>
                <option value="韩飞">韩飞</option> 
                <option value="朱金峰">朱金峰</option> 
                <option value="方银兵">方银兵</option> 
            `;
        }
        else if(this.userInfo.section === '检测一科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="黄辉辉">黄辉辉</option>
                <option value="黄俊华">黄俊华</option>
                <option value="滕维银">滕维银</option>
                <option value="周家柱">周家柱</option> 
                <option value="杨明福">杨明福</option> 
                <option value="踪阁">踪阁</option> 
            `;
        }
        else if(this.userInfo.section === '检测二科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="陈洁">陈洁</option>
                <option value="蒋浩浩">蒋浩浩</option>
                <option value="刘豪琰">刘豪琰</option>
                <option value="马贤瑞">马贤瑞</option> 
                <option value="张天明">张天明</option> 
                <option value="车赛">车赛</option> 
            `;
        }
        else if(this.userInfo.section === '中板切科'){
            shiftLeader.innerHTML = ` 
                <option value="">请选择</option>
                <option value="黄盅盅">黄盅盅</option>
                <option value="岳成波">岳成波</option>
                <option value="程龙">程龙</option>
            `;
        }

        // 查询显示类型，单选按钮添加事件
        const showTypeDiv = document.getElementById('shiftContainer');
        const showType_alarm = document.getElementById('showtype-alarm');
        const showType_daily = document.getElementById('showtype-daily');
        const showType_man = document.getElementById('showtype-man');
        if(showType_alarm && showType_daily && showType_man && showTypeDiv){

            this.handleRadioChange(showType_alarm, showType_daily, showType_man, showTypeDiv);
        }

        // 绑定查询表单提交事件
        const searchForm = document.getElementById('asssearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.currentPage = 1; // 重置到第一页
                this.fetchAssociateData(); // 获取查询数据
            });
        }

        // 导出按钮绑定事件
        const downloadBtn = searchForm.querySelector('.assDownload-btn');
        if(downloadBtn){
            downloadBtn.addEventListener('click',(e)=>{
                e.preventDefault();
                this.downloadSearchData();
            });
        }

        // T信通报按钮 绑定事件
        const textCopyBtn = searchForm.querySelector('.assTReport-btn');
        if(textCopyBtn){
            textCopyBtn.addEventListener('click',(e)=>{
                e.preventDefault();
                this.textCopyShiftSearchData();
            });
        }

        // 邮件发送按钮 绑定事件
        const emailBtn = searchForm.querySelector('.assEmailReport-btn');
        if(emailBtn){
            emailBtn.addEventListener('click',(e)=>{
                e.preventDefault();
                this.emailShiftSearchData();
            });
        }

        // 绑定分页事件
        this.initializePagination();
    }

    // 查询页面 - 加载查询表单的选项数据
    async loadSearchOptions() {
        try {
            // 获取用户科室
            const section = this.userInfo?.section || '';
            let project = this.getProject(this.userInfo.level);
            console.log('加载页面选择前1 - project:',project);
            

            // 加载Project选项
            const projectSelect = document.getElementById('asssearch-project');
            const projectResponse = await fetch(`php/get_options.php?type=project&section=${section}`);
            const projectData = await projectResponse.json();
            if (projectData.success) {
                //const projectSelect = document.getElementById('asssearch-project');
                if (projectSelect) {
                    projectSelect.innerHTML = '<option value="">全部</option>' + 
                        projectData.data.map(project => `<option value="${project}">${project}</option>`).join('');
                }
            }

            // 添加project变化时更新line和unit的监听
            //const projectSelect = document.getElementById('asssearch-project');
            projectSelect?.addEventListener('change', async () => {
                project = projectSelect.value;
                // 如果project未选择，加载该科室下所有line 和 unit
                const url_line = project ? 
                    `php/get_options.php?type=line&section=${section}&project=${project}` :
                    `php/get_options.php?type=line&section=${section}`;
                const url_unit = project ? 
                    `php/get_options.php?type=unit&section=${section}&project=${project}` :
                    `php/get_options.php?type=unit&section=${section}`;
                try {
                    const response = await fetch(url_line);
                    const data = await response.json();
                    if (data.success) {
                        lineSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(line => `<option value="${line}">${line}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载line选项失败:', error);
                }

                try {
                    const response = await fetch(url_unit);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });
            console.log('加载页面选择后 - project:',project);
            // 加载LINE选项
            const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}&project=${project}`);
            const lineData = await lineResponse.json();
            if (lineData.success) {
                const lineSelect = document.getElementById('asssearch-line');
                if (lineSelect) {
                    lineSelect.innerHTML = '<option value="">全部</option>' + 
                        lineData.data.map(line => `<option value="${line}">${line}</option>`).join('');
                }
            }

            // 加载UNIT选项
            const unitResponse = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
            const unitData = await unitResponse.json();
            if (unitData.success) {
                const unitSelect = document.getElementById('asssearch-unit');
                if (unitSelect) {
                    unitSelect.innerHTML = '<option value="">全部</option>' + 
                        unitData.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                }
            }

            // 添加line变化时更新unit的监听
            const lineSelect = document.getElementById('asssearch-line');
            const unitSelect = document.getElementById('asssearch-unit');
            lineSelect?.addEventListener('change', async () => {
                const line = lineSelect.value;
                // 如果line未选择，加载该科室下所有unit
                const url = line ? 
                    `php/get_options.php?type=unit&section=${section}&line=${line}&project=${project}` :
                    `php/get_options.php?type=unit&section=${section}&project=${project}`;
                
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });

            

        } catch (error) {
            console.error('加载选项失败:', error);
        }
    }

    // 查询页面 - 获取查询数据
    async fetchAssociateData() {
        try {
            const form = document.getElementById('asssearchForm');
            const formData = new FormData(form);
            // const project = this.getProject(this.userInfo?.level);
            
            // 构建查询参数
            const params = new URLSearchParams();
            formData.forEach((value, key) => {
                if (value) params.append(key, value);
            });
            params.append('page', this.currentPage);
            params.append('pageSize', this.pageSize);

            // 如果没有指定科室参数，默认使用当前用户的科室
            if (!params.has('section') && this.userInfo?.section) {
                params.append('section', this.userInfo.section);
            }
            // 如果没有指定project参数，默认使用当前用户的project
            // if (!params.has('project') && project) {
            //     params.append('project', project);
            // }
            console.log('params:',params);

            const showType = params.get('showtype');
            console.log('showType:',showType);

            let response = null;
            let result = null;
            switch (showType){
                case '故障':
                    response = await fetch(`associate/get_associates.php?${params.toString()}`);
                    result = await response.json();

                    if (result.success) {
                        console.log('get_associates result:',result);
                        this.totalPages = result.totalPages;
                        this.associateData = result.data;  //查询结果获取
                        this.renderAssociateTable(result.data);
                        this.updatePagination(result);
                    } else {
                        throw new Error(result.message);
                    }
                    break;
                case '日期':
                    response = await fetch(`associate/get_shift_associates_date.php?${params.toString()}`);
                    result = await response.json();

                    if (result.success) {
                        console.log('get_shift_associates result:',result);
                        this.shiftDateArr = this.sqlDataToArray(result.data);
                        this.totalPages = result.totalPages;
                        this.currentPage = result.currentPage;// 一个日期， 可能 对应多个 结果， LD LG 
                        console.log('传入的date:',this.shiftDateArr[this.currentShiftPage - 1]); 

                        this.associateData = result.data;  //查询结果获取
                        this.renderAssociateTable(result.data);
                        this.updatePagination(result);
                    } else {
                        throw new Error(result.message);
                    }
                    break;
                case '人力':
                    break;
                default:
                    break;
            }
            
        } catch (error) {
            console.error('获取数据失败:', error);
            alert('获取数据失败: ' + error.message);
        }
    }

    // 查询页面 - 渲染查询结果
    renderAssociateData(data) {
        const tbody = document.querySelector('.data-table tbody');
        if (!tbody) return;
        tbody.innerHTML= ''; //内容先清空，再添加

        data.forEach(item=>{
            const tr=document.createElement('tr');
            if(this.userInfo?.account === '268552'){ // 王俊
                tr.innerHTML=`
                    <td>${this.formatNewDate(item.created_at,item.classes)}</td>
                    <td>${item.classes}</td>
                    <td>${item.line}</td>
                    <td>${item.unit}</td>
                    <td>${item.phenomenon || ''}
                        <span>${item.step > 0 ? '('+(item.step+1)+')' : ''}</span>
                    </td>
                    <td>${item.analysis || ''}</td>
                    <td>${item.measure || ''}</td>
                    <td>${item.problemtype}</td>
                    <td>${item.problempart || ''}</td>
                    <td>${item.problemcode || ''}</td>
                    <td><span class="follow-status ${item.needfollow === '是' ? 'follow-yes' : 'follow-no'}">
                            ${item.needfollow}
                        </span>
                    </td>
                    <td>${item.towho || ''}</td>
                    <td><span class="closed-status">
                        ${item.status || ''}</span>
                    </td>
                    <td>
                        <button onclick="associateManager.editRecord(${item.id})" class="btn-edit">处理</button>
                        <button onclick="associateManager.deleteRecord(${item.id})" class="btn-delete">删除</button>
                        <span>${item.number_files > 0 ? '📎'+ item.number_files : ''}</span>
                        <div id="container-${item.id}"></div>
                    </td>
                `;
            }
            else{
                tr.innerHTML=`
                    <td>${this.formatNewDate(item.created_at,item.classes)}</td>
                    <td>${item.classes}</td>
                    <td>${item.line}</td>
                    <td>${item.unit}</td>
                    <td>${item.phenomenon || ''}
                        <span>${item.step > 0 ? '('+(item.step+1)+')' : ''}</span>
                    </td>
                    <td>${item.analysis || ''}</td>
                    <td>${item.measure || ''}</td>
                    <td>${item.problemtype}</td>
                    <td>${item.problempart || ''}</td>
                    <td>${item.problemcode || ''}</td>
                    <td><span class="follow-status ${item.needfollow === '是' ? 'follow-yes' : 'follow-no'}">
                            ${item.needfollow}
                        </span>
                    </td>
                    <td>${item.towho || ''}</td>
                    <td><span class="closed-status">
                        ${item.status || ''}</span>
                    </td>
                    <td>
                        <button onclick="associateManager.editRecord(${item.id})" class="btn-edit">处理</button>
                        <span>${item.number_files > 0 ? '📎'+ item.number_files : ''}</span>
                        <div id="container-${item.id}"></div>
                    </td>
                `;
            }

            tbody.appendChild(tr);
        });
    }
    // 查询页面 - 渲染查询结果 - 绘制整张表格
    renderAssociateTable(assData){
        const assListContent = document.getElementById('asslist-content');
        if(!assListContent) return;

        // 清空子元素
        while(assListContent.firstChild){
            assListContent.removeChild(assListContent.firstChild);
        }

        if(assData.length > 0){
            assListContent.appendChild(document.createElement('br'));
            // 创建表格元素 —— 表格
            const tableList = document.createElement('table');
            tableList.className = 'data-table2';
            tableList.id = 'data-table-alarm';

            // 创建表头元素 —— 表头
            const theadList = document.createElement('thead');   
            const headerRowList = document.createElement('tr');
            // 创建表头单元格
            const headersList = ['日期', '班次', '分类', 'LINE', 'UNIT', 
                '问题点','原因','处理内容','需要跟进','是否结案','谁跟进',
                '大故障', '操作']; 
            headersList.forEach( hearerText =>{
                const th = document.createElement('th');
                th.textContent = hearerText;
                headerRowList.appendChild(th);
            });
            // 将表头行添加到表头元素中
            theadList.appendChild(headerRowList);

            // 创建表体元素 —— 表体
            const tbodyList = document.createElement('tbody');
            // 创建表体数据
            assData.forEach(rowData => {
                let tr = document.createElement('tr');
                tr.innerHTML=`
                    <td>${this.formatNewDate(rowData.created_at,rowData.classes)}</td>
                    <td>${rowData.classes}</td>
                    <td>${rowData.problemtype}</td>
                    <td>${rowData.line}</td>
                    <td>${rowData.unit}</td>
                    <td>${rowData.phenomenon || ''}
                        <span>${rowData.step > 0 ? '('+(rowData.step+1)+')' : ''}</span>
                    </td>
                    <td>${rowData.analysis || ''}</td>
                    <td>${rowData.measure || ''}</td>
                    <td><span class="follow-status ${rowData.needfollow === '是' ? 'follow-yes' : 'follow-no'}">
                            ${rowData.needfollow}
                        </span>
                    </td>
                    <td><span class="closed-status">${rowData.status || ''}</span></td>
                    <td>${rowData.towho || ''}</td>
                    <td><span class="follow-status ${rowData.isbigalarm === '是' ? 'follow-yes' : 'follow-no'}">
                            ${rowData.isbigalarm}
                        </span>
                        <span>${rowData.isbigalarm === '是' ? 
                            '<button type="button" class="btn-to-big-alarm"  onclick="associateManager.toBigAlarm(this)">大故障</button>'
                            : 
                            ''
                            }
                        </span>
                    </td>
                `;
                if(this.userInfo?.account === '268552'){ // 王俊
                    tr.innerHTML +=`
                        <td>
                            <button onclick="associateManager.editRecord(${rowData.id})" class="btn-edit">处理</button>
                            <button onclick="associateManager.deleteRecord(${rowData.id})" class="btn-delete">删除</button>
                            <span>${rowData.number_files > 0 ? '📎'+ rowData.number_files : ''}</span>
                        </td>
                    `;
                }else{
                    tr.innerHTML +=`
                        <td>
                            <button onclick="associateManager.editRecord(${rowData.id})" class="btn-edit">处理</button>
                            <span>${rowData.number_files > 0 ? '📎'+ rowData.number_files : ''}</span>
                        </td>
                    `;
                }
                tbodyList.appendChild(tr);
            });
            // 将表头、表体 添加到表格中
            tableList.appendChild(theadList);
            tableList.appendChild(tbodyList);

            // 将表格添加到页面的容器中
            assListContent.appendChild(tableList);
        }
    }

    // 数据库数据转换成数组
    sqlDataToArray(arr){
        console.log('sqlDataToArray-1');
        return arr.map(item => {
            console.log('sqlDataToArray-item:',item);
            return item;
        });
    }
    
    // 文本复制数据 当前页面 当前表格的数据发送
    async textCopyShiftSearchData(){
        if(!confirm('您确定复制为文本内容?')){
            return;
        }

        // 获取人力表格元素
        const manpowerTable = document.getElementById('assShiftSearchTable-manpower'); 
        if(!manpowerTable){
            alert('无人力数据');
            return;
        }
        // 获取人力表格元素
        const shiftAssociateTable = document.getElementById('assShiftSearchTable'); 
        if(!shiftAssociateTable){
            alert('无交接数据');
            return;
        }

        // 初始化表格内容字符串 
		let manpowerTableContent = manpowerTable.rows[1].cells[0].textContent //日期
                                    + ' ' + manpowerTable.rows[1].cells[3].textContent //班次
                                    + ' ' + manpowerTable.rows[1].cells[1].textContent //区域
                                    + '日常交接问题点\n';
        ; 
        manpowerTableContent = manpowerTableContent 
                                + '本班应到人力:' + manpowerTable.rows[1].cells[4].textContent + ' '
                                + '本班实到人力:' + manpowerTable.rows[1].cells[5].textContent + ' '
                                + '加班人力:' + manpowerTable.rows[1].cells[6].textContent + '。\n'
                                ;

        let shiftAssContent='问题点：\n';
        let lineFlag='';
        let index=1;
        // 遍历表格的每一行 呈现的不是表格形式，为文本形式
        for (let i = 1; i < shiftAssociateTable.rows.length; i++) { 
            const row = shiftAssociateTable.rows[i]; 
            // 遍历每一行的每个单元格 
            // for (let j = 0; j < row.cells.length; j++) { 
            //     const cell = row.cells[j]; 
            //     // 将单元格内容添加到表格内容字符串中，用制表符分隔 
            //     shiftAssContent += cell.textContent + '\t'; 
            // } 
            if(lineFlag != row.cells[2].textContent){
                shiftAssContent += '>' + row.cells[2].textContent+':\n';
                lineFlag = row.cells[2].textContent;
                index = 1;
            }
            shiftAssContent += '(' + index + ')' + '、' + row.cells[3].textContent + ' ' + row.cells[4].textContent 
                                + '；' + row.cells[5].textContent + '；' + row.cells[6].textContent;
            // 每行结束后添加换行符 
            shiftAssContent += '\n'; 
            index++;
        }

        let assContent = manpowerTableContent + shiftAssContent;
        // Test
        if (navigator.clipboard) {
            console.log('剪贴板 API 支持');
        } else {
            console.log('剪贴板 API 不支持');
        }
        // 使用剪贴板 API 复制文字 —— 不支持
        // navigator.clipboard.writeText(assContent).then(function() {
        //     // 复制成功
        //     alert('已复制到剪贴板');
        // }).catch(function(error) {
        //     // 复制失败
        //     alert('复制失败: ' + error);
        // });
        this.fallbackCopy(assContent).then(() => {
            alert('已复制到剪贴板（直接粘贴即可）');
        }).catch(err => {
            alert('复制失败: ' + err.message);
        });
    }

    // 邮件发送数据 当前页面 当前表格的数据发送， 邮件编写部分文字，图片需要手动添加
    async emailShiftSearchData(){
        try {
            let bDownlod=false;
            if(!confirm('您确定邮件发送这些内容?')){
                return;
            }
            // if(confirm('表格是否需要下载为图片?'))
            // {
            //     bDownlod = true;
            // }

            // 获取人力表格元素
            const manpowerTable = document.getElementById('assShiftSearchTable-manpower'); 
            if(!manpowerTable){
                alert('无人力数据');
                return;
            }
            // 获取交接表格元素
            const shiftAssociateTable = document.getElementById('assShiftSearchTable'); 
            if(!shiftAssociateTable){
                alert('无交接数据');
                return;
            }
            
            await html2canvas(manpowerTable).then(canvas =>{
                const imgManpowerData = canvas.toDataURL('image/png');

                if(bDownlod){
                    // 创建一个新的 a 标签用于下载图片
                    const a = document.createElement('a');
                    a.href = imgManpowerData;
                    a.download = `人力_${new Date().toLocaleDateString()}.png`;
                    a.click();
                }
                
            });
            await html2canvas(shiftAssociateTable).then(canvas =>{
                const imgAssociateData = canvas.toDataURL('image/png');

                if(bDownlod){
                    // 创建一个新的 a 标签用于下载图片
                    const a = document.createElement('a');
                    a.href = imgAssociateData;
                    a.download = `问题点_${new Date().toLocaleDateString()}.png`;
                    a.click();
                }
                
            });

            // 以下测试可行
            // 获取表格内容
            var manpowerTableContent = document.getElementById('assShiftSearchTable-manpower').outerHTML;
            var shiftAssociateTableContent = document.getElementById('assShiftSearchTable').outerHTML;

            // 创建一个临时的容器来复制内容
            var tempDiv = document.createElement("div");
            tempDiv.innerHTML = '<label>Dear ALL:</label>';
            tempDiv.innerHTML += `<p>人力交接如下：</p>`;
            tempDiv.innerHTML += manpowerTableContent;
            tempDiv.innerHTML += `<p>问题点交接如下：</p>`;
            tempDiv.innerHTML += shiftAssociateTableContent;
            tempDiv.innerHTML += '<label>以上，感谢</label>';


            document.body.appendChild(tempDiv);

            // 选中临时容器的内容
            var range = document.createRange();
            range.selectNodeContents(tempDiv);
            var selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // 执行复制命令
            try {
                document.execCommand("copy");
                alert("邮件内容已复制到剪贴板，在邮件中直接ctrl+v即可！");
            } catch (error) {
                alert("复制失败：" + error.message);
            }

            // 移除临时容器
            document.body.removeChild(tempDiv);
            // +++++++++++++++

            // 替代方法，待测试
            // try { 
			// 	// 获取表格元素 
			// 	const table = document.getElementById('myTable'); 
			// 	// 获取表格的 outerHTML，包含表格的所有 HTML 结构 
			// 	const tableHtml = manpowerTableContent; 
			// 	// 创建一个新的 ClipboardItem 对象，包含表格的 HTML 数据 
			// 	const clipboardItem = new ClipboardItem({ 
			// 		'text/html': new Blob([tableHtml], { 
			// 			type: 'text/html' 
			// 		}) 
			// 	}); 
			// 	// 使用 Clipboard API 的 write 方法将 ClipboardItem 对象写入剪贴板 
			// 	await navigator.clipboard.write([clipboardItem]); 
			// 	console.log('表格已成功复制到剪贴板！'); 
			// } 
			// catch (error) { 
			// 	console.error('复制表格时出错：', error); 
			// } 
            // +++++++++++++++++++++


			// 收件人邮箱地址 
            const recipient = '<EMAIL>; 偏贴实装一科_50165496'; 
			// 邮件主题 
			const subject = 'Shift Associate'; 
			// 编码邮件内容，确保特殊字符正确处理 
            // const body = `D:\\Users\\chenzhen22\\Downloads\\image.png`;
			// const body = `<!DOCTYPE html> <html lang="zh-CN"> <head> <meta charset="UTF-8"> <title>邮件标题</title> </head> <body> ${manpowerTableHtml} </body> </html>`; 
			// const body = `<html><body><img src="D:\\Users\\chenzhen22\\Downloads\\image.png" /></body></html>`; 
// 			 const body = `<table 
//   border="1" 
//   cellpadding="8" 
//   cellspacing="0" 
//   style="
//     border-collapse: collapse; 
//     border: 1px solid #000; 
//     mso-table-lspace: 0pt; 
//     mso-table-rspace: 0pt;
//   "
// >
//   <tr>
//     <td 
//       style="
//         border: 1px solid #000; 
//         padding: 8px; 
//         text-align: left;
//       "
//     >
//       aaa
//     </td>
//   </tr>
// </table>`
// ; 
            const body = ''; 
            //  const body = '<!DOCTYPE html><html><body><table><tr><td>2222</td></tr></table></body> </html>'; 
             // const body = manpowerTableHtml; //输出的是html语句
			// const body = encodeURIComponent(manpowerTableContent); //输出的字符串
			//const body = 'Dear ALL:\n\t今日交接如下：\n1、人力：\n【图片位置：此电脑->下载->人力.png】\n2、问题点：\n【图片位置：此电脑->下载->问题点.png】\n以上，感谢'; 

			// 构建邮件链接 
			const mailtoLink = `mailto:${recipient}?subject=${encodeURIComponent(subject)}&body=${body}`; 
			// const mailtoLink = `mailto:${recipient}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`; 
			// const mailtoLink = `mailto:${recipient}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`; 
			// 打开邮件客户端 
			window.location.href = mailtoLink; 
            // window.open(mailtoLink,'_blank');
            
        } catch (error) {
            console.error('邮箱打开失败:', error);
            alert('邮箱打开失败: ' + error.message);
        }
    }

    // 初始化分页控件
    initializePagination() {
        // 页码大小变化
        const pageSizeSelect = document.querySelector('.page-size');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.fetchAssociateData();
            });
        }

        // 页码导航按钮
        document.querySelector('.btn-prev-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.fetchAssociateData();
            }
        });

        document.querySelector('.btn-next-page')?.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.fetchAssociateData();
            }
        });

        // 首页导航按钮
        document.querySelector('.btn-first-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage = 1;
                this.fetchAssociateData();
            }
        });

        // 末页导航按钮
        document.querySelector('.btn-last-page')?.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage = this.totalPages;
                this.fetchAssociateData();
            }
        });

        // 页码输入框
        const pageInput = document.querySelector('.current-page');
        if (pageInput) {
            pageInput.addEventListener('change', (e) => {
                const page = parseInt(e.target.value);
                if (page >= 1 && page <= this.totalPages) {
                    this.currentPage = page;
                    this.fetchAssociateData();
                } else {
                    e.target.value = this.currentPage;
                }
            });
        }
    }

    // 更新分页信息
    updatePagination(result) {
        document.querySelector('.total-count').textContent = result.total;
        document.querySelector('.total-pages').textContent = result.totalPages;
        document.querySelector('.current-page').value = result.currentPage;
    }

    // 查看图片
    async viewImages(associateId) {
        try {
            const response = await fetch(`php/get_associate_files.php?associate_id=${associateId}`);
            const result = await response.json();

            if (result.success && result.data.length > 0) {
                // 创建图片预览弹窗
                const viewer = document.createElement('div');
                viewer.className = 'image-viewer';
                viewer.style.position = 'fixed';
                viewer.style.top = '0';
                viewer.style.left = '0';
                viewer.style.width = '100%';
                viewer.style.height = '100%';
                viewer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                viewer.style.zIndex = '1000';
                viewer.style.display = 'flex';
                viewer.style.justifyContent = 'center';
                viewer.style.alignItems = 'center';

                viewer.innerHTML = `
                    <div class="image-viewer-content">
                        <span class="close-btn" style="
                            position: absolute;
                            right: 20px;
                            top: 20px;
                            color: white;
                            font-size: 30px;
                            cursor: pointer;
                            z-index: 1001;
                        ">&times;</span>
                        <div class="image-list" style="
                            display: flex;
                            flex-wrap: wrap;
                            justify-content: center;
                            gap: 10px;
                            padding: 20px;
                            max-height: 90vh;
                            overflow-y: auto;
                        ">
                            ${result.data.map(file => `
                                <img src="uploads/associates/${file.file_path}" 
                                     alt="${file.file_name}"
                                     title="${file.file_name}"
                                    style="
                                        max-width: 90vw;
                                        max-height: 80vh;
                                        object-fit: contain;
                                        cursor: pointer;
                                    "
                                    onclick="window.open(this.src, '_blank')"
                                >
                            `).join('')}
                        </div>
                    </div>
                `;

                document.body.appendChild(viewer);

                // 关闭按钮事件
                viewer.querySelector('.close-btn').onclick = () => {
                    viewer.remove();
                };

                // 点击背景关闭
                viewer.addEventListener('click', (e) => {
                    if (e.target === viewer) {
                        viewer.remove();
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', function closeOnEsc(e) {
                    if (e.key === 'Escape') {
                        viewer.remove();
                        document.removeEventListener('keydown', closeOnEsc);
                    }
                });
            } else {
                alert('没有找到相关图片');
            }
        } catch (error) {
            console.error('获取图片失败:', error);
            alert('获取图片失败');
        }
    }

    // 查询页面 - 显示list详情 & 编辑
    async editRecord(id){
        if (!this.modal){
            console.log('没有模态框 modal');
            return;
        }

        // 从已加载的数据中查找交接详情
        const data = this.associateData.find(associate => associate.id === parseInt(id));
        if (!data) {
            console.error('未找到ID为', id, '的交接数据');
            return;
        }

        // 保存当前表单的值
        const searchForm = document.getElementById('asssearchForm');
        if (searchForm) {
            this.savedFormData = new FormData(searchForm);
        }

        // 赋值
        this.curData = data;

        // 显示模态框
        this.modal.style.display = 'block';
        document.body.style.overflow = 'hidden';

        // 更新模态框内容
        this.updateModalContent(data);

        // 加载并显示附件
        // await this.loadAssociateFiles(id);
        // 加载并显示所有附件
        await this.loadAssociateAllFiles(data);
    }

    // 查询页面 - 删除该List
    async deleteRecord(id){
        // 从已加载的数据中查找交接详情
        const data = this.associateData.find(associate => associate.id === parseInt(id));
        if (!data) {
            console.error('未找到ID为', id, '的交接数据');
            return;
        }

        // 保存当前表单的值
        const searchForm = document.getElementById('asssearchForm');
        if (searchForm) {
            this.savedFormData = new FormData(searchForm);
        }

        // 赋值
        // this.curData = data;

        // 加载并显示附件
        await this.deleteAssociateAndFiles(id);
    }

    // 更新模态框内容
    updateModalContent(data) {
        console.log('更新模态框内容');

        // 设置历史履历不显示
        //this.bShowHistory = false;

        // 更新标题
        this.modal.querySelector('.modal-header h2').textContent = 
            `${data.line} ${data.unit}`;

        // 更新详情表格
        const detailRow = this.modal.querySelector('.detail-table tr:nth-child(2)');
        if (detailRow) {
            detailRow.innerHTML = `
                <td>${this.safeValue(data.section)}</td>
                <td>${this.safeValue(data.line)}</td>
                <td>${this.safeValue(data.unit)}</td>
                <td>${this.safeValue(data.phenomenon)}</td>
                <td>${this.safeValue(data.problempart)}</td>
                <td>${this.safeValue(data.problemcode)}</td>
                <td>${this.safeValue(data.needfollow)}</td>
                <td>${this.formatDate(data.created_at)}</td>
                <td>${this.safeValue(data.recorder)}</td>
            `;
        }

        // 更新其他详情部分
        const towho = data.towho ? '跟踪者：' + data.towho : '';
        console.log('模态框内容towho:',towho);

        const sections = this.modal.querySelectorAll('.detail-section');
        sections.forEach(section => {
            const title = section.querySelector('h3').textContent;
            const content = section.querySelector('.section-content');
            
            switch (title) {
                case '故障原因':
                    // content.textContent = data.analysis || '';
                    content.innerHTML =`
                    <label>${this.safeValue(data.analysis)}</label>
                    <textarea name="analysis" rows="2" style="width:550px;height:100px;">${this.safeValue(data.analysis)}</textarea>
                    `;
                    break;
                case '处理内容':
                    // if (data.measure) {
                    //     const measure = data.measure.split('\n');
                    //     content.innerHTML = measure.map(m => `<p>${m}</p>`).join('');
                    // } else {
                    //     content.innerHTML = '';
                    // }
                    content.innerHTML =`
                    <label>${this.safeValue(data.measure)}</label>
                    <textarea name="measure" rows="2" style="width:550px;height:100px;"></textarea>
                    `;
                    break;
                case '需要跟进':
                    
                    if(data.needfollow === '是'){
                        content.innerHTML = `
                            <label>
                                <input type="radio" name="needfollow" value="是" checked required>
                                <span>是</span>
                            </label>
                            <label>
                                <input type="radio" name="needfollow" value="否" required>
                                <span>否</span>
                            </label>
                        `;
                    }
                    else{
                        content.innerHTML = `
                            <label>
                                <input type="radio" name="needfollow" value="是"  required disabled>
                                <span>是</span>
                            </label>
                            <label>
                                <input type="radio" name="needfollow" value="否" checked required disabled>
                                <span>否</span>
                            </label>
                        `;
                    }
                    break;
                case '是否结案':
                    if(data.status === 'open'){
                        content.innerHTML = `
                            <label>
                                <input type="radio" name="status" value="open" checked required>
                                <span>open</span>
                            </label>
                            <label>
                                <input type="radio" name="status" value="close" required>
                                <span>close</span>
                            </label>
                            <div><label>${this.safeValue(towho)}</label></div>
                        `;
                    }
                    else{
                        content.innerHTML = `
                            <label>
                                <input type="radio" name="status" value="open"  required>
                                <span>open</span>
                            </label>
                            <label>
                                <input type="radio" name="status" value="close" checked required>
                                <span>close</span>
                            </label>
                            <div><label>${this.safeValue(towho)}</label></div>
                    `;
                    }
                    break;
                case '相关附件':
                    // TODO: 处理图片显示逻辑  file-section
                    // content.innerHTML = '暂无图片';
                    // fileList-211 内存放刚选择的文件
                    // file-section 内存放该条数据 id 对应的文件 => 修改为显示过往历史所有文件 - 20250602
                    content.innerHTML = `
                        <div>
                            <span>
                                <input type="file" id="fileUpload" name="files[]" multiple 
                                accept="image/jpeg,image/png,image/gif" 
                                style="width: 100px;"
                                onchange="associateManager.handleFileSelect(event,211)" >
                            </span>
                        </div>
                        <div class="file-list" id="fileList-211"></div>
                        <div class="file-section"></div>
                    `;
                    break;
            }
        });
        
        // 添加按钮 & 事件
        const modalBtn = this.modal.querySelector('.modal-btn-modify');
        if(modalBtn){
            modalBtn.innerHTML=`
                <button class="btn-modify" onclick="associateManager.modifyNeedfollowMsg('${data.status}')">修改</button>
            `;
        }
        
        // 添加历史履历显示
        // const tableHistoryBody = this.modal.getElementById('history-body-table');

        // 获取历史信息的查询条件
        const params = new URLSearchParams();
        params.append('section',data.section);
        params.append('line',data.line);
        params.append('project',data.project);
        params.append('unit',data.unit);
        params.append('phenomenon',data.phenomenon);
        params.append('problemtype',data.problemtype);
        // 需要添加创建时间以 区分 上述7同数据  - 20250602
        params.append('created_at',data.created_at);

        this.getHistoryContent(params);
    }

    // 模态框 修改按钮 处理事件
    async modifyNeedfollowMsg(str){
        if(!confirm('您确定需要对该问题点进行修改?')){
            return;
        }
        try{
            // 获取模态框中的内容
            const detailContent = document.getElementById('detail-sections');
            const analysis = detailContent.querySelector('textarea[name="analysis"]').value;
            const measure = detailContent.querySelector('textarea[name="measure"]').value;
            const status = detailContent.querySelector('input[name="status"]:checked');
            
            if(measure.trim().length === 0){
                alert('【处理项】未做修改，不可更新!!!请做处理后，再进行更新...');
                return;
            }

            const formData = new FormData();

            // 添加科室信息
            formData.append('section',this.userInfo?.section || '');
            formData.append('project', this.curData.project);
            formData.append('classes', this.curData.classes);

            // 添加无需 更新 的信息
            formData.append('line',this.curData.line);
            formData.append('unit',this.curData.unit);
            formData.append('phenomenon',this.curData.phenomenon);
            formData.append('problemtype',this.curData.problemtype);
            formData.append('problempart',this.curData.problempart);
            formData.append('problemcode',this.curData.problemcode);
            formData.append('needfollow',this.curData.needfollow);
            formData.append('towho',this.curData.towho);
            formData.append('isbigalarm',this.curData.isbigalarm);

            // 添加 更新 的信息
            formData.append('analysis',analysis);
            formData.append('measure',measure);
            formData.append('status',status? status.value : 'open');

            // 添加当前用户作为recorder
            formData.append('recorder', this.userInfo?.name || '');

            // 创建时间不变
            formData.append('created_at', this.curData.created_at);
            // console.log('test created_at:', this.curData.created_at);

            // 添加 step
            // console.log('test step:', this.curData.step);
            formData.append('step', this.curData.step+1);
            // console.log('test step:', this.curData.step);

            // 添加文件（如果有）
            const fileInput = detailContent.querySelector('input[type="file"]');
            if (fileInput && fileInput.files.length > 0) {
                Array.from(fileInput.files).forEach(file => {
                    // 使用validRowCount而不是i作为索引
                    formData.append(`files[]`, file);
                });
            }

            // 读出 formData 中的 键值对
            // for (let [key, value] of formData) { 
            //     console.log(`Key: ${key}, Value: ${value}`); 
            // } 

            const response = await fetch('associate/modify_associate.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                // 修改完成，初始化查询页面，并回到查询页面
                alert('修改成功');
                str = '';// 这里的赋值，没有作用，该方法执行时，传入的参数没有发生改变
                // this.initializeSearchForm(); // 初始化查询页面
                // window.location.href = 'associate.html';
                // 关闭模态框
                this.modal.style.display = 'none';
                document.body.style.overflow = 'auto';

                // 使用保存的表单值构建查询参数
                if (this.savedFormData) {
                    const params = new URLSearchParams();
                    for (let [key, value] of this.savedFormData) {
                        if (value) {
                            params.append(key, value);
                        }
                    }
                    params.append('page', this.currentPage);
                    params.append('pageSize', this.pageSize);

                    // 如果没有指定科室参数，默认使用当前用户的科室
                    if (!params.has('section') && this.userInfo?.section) {
                        params.append('section', this.userInfo.section);
                    }

                    // 使用保存的参数重新加载数据
                    const response = await fetch(`associate/get_associates.php?${params.toString()}`);
                    const result = await response.json();

                    if (result.success) {
                        this.totalPages = result.totalPages;
                        this.associateData = result.data;
                        this.renderAssociateTable(result.data);
                        this.updatePagination(result);
                    }
                } else {
                    // 如果没有保存的表单值，则正常刷新
                    await this.fetchAssociateData();
                }
            } else {
                throw new Error(result.message);
            }
        }catch(error){
            console.log('log:'+ error);
            console.error('修改失败:', error);
            alert('修改失败: ' + error.message);
        }
    }

    // 添加加载附件的方法
    async loadAssociateFiles(associateId) {
        try {
            const response = await fetch(`php/get_associate_files.php?associate_id=${associateId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message);
            }
            console.log('获取到了文件,ID为:',associateId);
            console.log('文件个数:',result.data.length);

            const fileSection = this.modal.querySelector('.file-section');
            if (!fileSection){
                console.log('return了');
                return;
            } 

            if (result.data.length === 0) {
                fileSection.innerHTML = '<p>暂无附件</p>';
                return;
            }

            // 显示附件列表，区分图片和非图片文件的处理
            fileSection.innerHTML = result.data.map(file => {
                const isImage = this.isImageFile(file.file_name);
                const fileUrl = `uploads\\associates\\${file.file_path}`;
                const fileSize = (file.file_size / 1024).toFixed(2);
                console.log('file name:',file.file_name);
                console.log('fileUrl:',fileUrl);

                return `
                    <div class="file-item">
                        <span class="file-name" 
                              data-file-url="${fileUrl}"
                              data-file-name="${file.file_name}"
                              data-is-image="${isImage}"
                              style="cursor: pointer;">
                            ${file.file_name}
                        </span>
                        <span class="file-size">(${fileSize} KB)</span>
                    </div>
                `;

                console.log('fileSection.innerHTML:',fileSection.innerHTML);
            }).join('');

            // 为所有文件名添加点击事件监听器
            const fileNames = fileSection.querySelectorAll('.file-name');
            fileNames.forEach(fileName => {
                fileName.addEventListener('click', () => {
                    const fileUrl = fileName.dataset.fileUrl;
                    const isImage = fileName.dataset.isImage === 'true';
                    const originalFileName = fileName.dataset.fileName;
                    console.log("isImage:",isImage);
                    console.log("fileUrl:",fileUrl);

                    if (isImage) {
                        this.showImage(fileUrl);
                    } else {
                        this.downloadFile(fileUrl, originalFileName);
                    }
                });
            });
            console.log('添加文件完毕');
        } catch (error) {
            console.error('加载附件失败:', error);
            const fileSection = this.modal.querySelector('.file-section');
            if (fileSection) {
                fileSection.innerHTML = '<p class="error">加载附件失败</p>';
            }
        }
    }

    // 添加加载所有附件的方法（包含整个交接跟踪过程中的所有附件）
    async loadAssociateAllFiles(associateData) {
        try {
            // 获取查询条件
            const params = new URLSearchParams();
            params.append('associate_id',associateData.id);
            params.append('section',associateData.section);
            params.append('line',associateData.line);
            params.append('classes',associateData.classes);
            params.append('project',associateData.project);
            params.append('unit',associateData.unit);
            params.append('phenomenon',associateData.phenomenon);
            params.append('problemtype',associateData.problemtype);
            params.append('created_at',associateData.created_at);
            
            // 打印Log： 读出 查询条件 中的 键值对
            console.log('loadAssociateAllFiles 1');
            for (let [key, value] of params) { 
                console.log(`Key: ${key}, Value: ${value}`); 
            } 
            console.log('2');

            const response = await fetch(`php/get_associate_all_files.php?${params.toString()}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message);
            }
            console.log('ALL文件个数:',result.data.length);

            const fileSection = this.modal.querySelector('.file-section');
            if (!fileSection){
                console.log('return了');
                return;
            } 

            if (result.data.length === 0) {
                fileSection.innerHTML = '<p>暂无附件</p>';
                return;
            }

            // 显示附件列表，区分图片和非图片文件的处理
            fileSection.innerHTML = result.data.map(file => {
                const isImage = this.isImageFile(file.file_name);
                const fileUrl = `uploads\\associates\\${file.file_path}`;
                const fileSize = (file.file_size / 1024).toFixed(2);
                console.log('file name:',file.file_name);
                console.log('fileUrl:',fileUrl);

                return `
                    <div class="file-item">
                        <span class="file-name" 
                              data-file-url="${fileUrl}"
                              data-file-name="${file.file_name}"
                              data-is-image="${isImage}"
                              style="cursor: pointer;">
                            ${file.file_name}
                        </span>
                        <span class="file-size">(${fileSize} KB)</span>
                    </div>
                `;

                console.log('fileSection.innerHTML:',fileSection.innerHTML);
            }).join('');

            // 为所有文件名添加点击事件监听器
            const fileNames = fileSection.querySelectorAll('.file-name');
            fileNames.forEach(fileName => {
                fileName.addEventListener('click', () => {
                    const fileUrl = fileName.dataset.fileUrl;
                    const isImage = fileName.dataset.isImage === 'true';
                    const originalFileName = fileName.dataset.fileName;
                    console.log("isImage:",isImage);
                    console.log("fileUrl:",fileUrl);

                    if (isImage) {
                        this.showImage(fileUrl);
                    } else {
                        this.downloadFile(fileUrl, originalFileName);
                    }
                });
            });
            console.log('添加文件完毕');
        } catch (error) {
            console.error('加载附件失败:', error);
            const fileSection = this.modal.querySelector('.file-section');
            if (fileSection) {
                fileSection.innerHTML = '<p class="error">加载附件失败</p>';
            }
        }
    }

    // 添加删除List和附件的方法
    async deleteAssociateAndFiles(associateId) {
        try {
            if (!confirm('确定要删除这条记录吗【包括关联的附件】？')) {
                return;
            }
    
            console.log('11111:'.associateId);

            const response = await fetch('php/delete_associate_and_file.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: associateId })
            });
            console.log('1122222:');
            const result = await response.json();
            if (result.success) {
                alert('删除成功');
                this.fetchAssociateData();
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('删除失败:', error);
            alert('删除失败：' + error.message);
        }
    }

    // 模态框，获取历史履历信息
    async getHistoryContent(params){
        try {
            // 打印Log： 读出 查询历史信息条件 中的 键值对
            // console.log('1');
            // for (let [key, value] of params) { 
            //     console.log(`Key: ${key}, Value: ${value}`); 
            // } 
            // console.log('2');
            const response = await fetch(`php/get_history_associates.php?${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                this.historyData = result.data;  //查询结果获取
                this.historyBodyContent(result.data);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取历史数据失败:', error);
            alert('获取历史数据失败: ' + error.message);
        }
    }

    // 显示历史履历信息 表
    historyBodyContent(data){
        console.log('历史履历信息 表');

        const historyTbody = document.querySelector('.history-body-table tbody');
        if (!historyTbody) return;

        // 时间显示天： ${this.formatDate(item.updated_at)}
        historyTbody.innerHTML = data.map(item => `
            <tr>
                <td>${item.step+1}</td>
                <td>${item.analysis}</td>
                <td>${item.measure}</td>
                <td>${item.updated_at}</td>
                <td>${item.recorder || ''}</td>
                
                <td>
                    <div class="history-file-section_${item.id}" id="history-file-section_${item.id}"></div>
                </td>
                
            </tr>
        `).join('');

        // 循环加载历史履历文件
        data.map(item=>{
            this.loadHistoryAssociateFiles(item.id);
        });
    }

    // 获取历史附件并加载的方法
    async loadHistoryAssociateFiles(associateId) {
        try {
            const response = await fetch(`php/get_associate_files.php?associate_id=${associateId}`);
            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message);
            }
            // console.log('获取到了履历文件,ID为:',associateId);
            // console.log('履历文件个数:',result.data.length);

            const fileSection = this.modal.querySelector('.history-file-section_'+associateId);
            if (!fileSection){
                console.log('没有找到fileSection，return了');
                return;
            } 

            if (result.data.length === 0) {
                fileSection.innerHTML = '<p>暂无附件</p>';
                return;
            }

            // 显示附件列表，区分图片和非图片文件的处理
            fileSection.innerHTML = result.data.map(file => {
                const isImage = this.isImageFile(file.file_name);
                const fileUrl = `uploads\\associates\\${file.file_path}`;
                const fileSize = (file.file_size / 1024).toFixed(2);
                // console.log('file name:',file.file_name);
                // console.log('fileUrl:',fileUrl);

                return `
                    <div class="file-item">
                        <span class="file-name" 
                            data-file-url="${fileUrl}"
                            data-file-name="${file.file_name}"
                            data-is-image="${isImage}"
                            style="cursor: pointer;">
                            ${file.file_name}
                        </span>
                        <span class="file-size">(${fileSize} KB)</span>
                    </div>
                `;

                console.log('fileSection.innerHTML:',fileSection.innerHTML);
            }).join('');

            // 为所有文件名添加点击事件监听器
            const fileNames = fileSection.querySelectorAll('.file-name');
            fileNames.forEach(fileName => {
                fileName.addEventListener('click', () => {
                    const fileUrl = fileName.dataset.fileUrl;
                    const isImage = fileName.dataset.isImage === 'true';
                    const originalFileName = fileName.dataset.fileName;
                    // console.log("isImage:",isImage);
                    // console.log("fileUrl:",fileUrl);

                    if (isImage) {
                        this.showImage(fileUrl);
                    } else {
                        this.downloadFile(fileUrl, originalFileName);
                    }
                });
            });
            // console.log('添加文件完毕');
        } catch (error) {
            console.error('加载附件失败:', error);
            const fileSection = this.modal.querySelector('.file-section');
            if (fileSection) {
                fileSection.innerHTML = '<p class="error">加载附件失败</p>';
            }
        }
    }

    historyTitleClick(bShow){
        const historyContainer = document.getElementById('history-body');
        if(!bShow){
            this.modal.querySelector('.history-title h3').textContent = `点击历史履历信息隐藏`;
            this.bShowHistory = true;
            // 控制历史履历的 显示
            if (historyContainer) {
                historyContainer.style.display = 'inline-block';
            }
        }
        else{
            this.modal.querySelector('.history-title h3').textContent = `点击历史履历信息展开`;
            this.bShowHistory = false;
            // 控制历史履历的 隐藏
            if (historyContainer) {
                historyContainer.style.display = 'none';
            }
        }
    }

    // 添加判断文件是否为图片的方法
    isImageFile(fileName) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
        const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        return imageExtensions.includes(ext);
    }

    // 添加显示图片的方法
    showImage(imageUrl) {
        const imageWindow = window.open('', '_blank');
        imageWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>图片预览</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        min-height: 100vh;
                        background: #f0f0f0;
                    }
                    img {
                        max-width: 100%;
                        max-height: 90vh;
                        object-fit: contain;
                        box-shadow: 0 0 20px rgba(0,0,0,0.15);
                    }
                </style>
            </head>
            <body>
                <img src="${imageUrl}" alt="预览图片">
            </body>
            </html>
        `);
    }

    // 添加下载文件的方法
    downloadFile(fileUrl, fileName) {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // 导出搜索数据
    async downloadSearchData(){
        try {
            if(!confirm('您确定导出搜索的所有数据?')){
                return;
            }

            const form = document.getElementById('asssearchForm');
            const formData = new FormData(form);
            const project = this.getProject(this.userInfo?.level);
            
            // 构建查询参数
            const params = new URLSearchParams();
            formData.forEach((value, key) => {
                if (value) params.append(key, value);
            });
            params.append('page', 1);
            params.append('pageSize', 0);

            // 如果没有指定科室参数，默认使用当前用户的科室
            if (!params.has('section') && this.userInfo?.section) {
                params.append('section', this.userInfo.section);
            }
            // 如果没有指定project参数，默认使用当前用户的project
            if (!params.has('project') && project) {
                params.append('project', project);
            }

            const response = await fetch(`associate/get_associates.php?${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                const ws = XLSX.utils.json_to_sheet(result.data.map(item => ({
                    "日期": this.formatNewDate(item.created_at,item.classes),
                    "科室": item.section,
                    "班次": item.classes,
                    "LINE": item.line,
                    "UNIT": item.unit,
                    "故障分类": item.problemtype,
                    "现象": item.phenomenon,
                    "原因": item.analysis,
                    "处理内容": item.measure,
                    "故障部件": item.problempart,
                    "故障代码": item.problemcode,
                    "需要跟进": item.needfollow,
                    "谁跟进": item.towho,
                    "是否大故障": item.isbigalarm,
                    "是否结案": item.status,
                    "跟进次数": item.step,
                    "跟进时间": this.formatDate(item.updated_at),
                    "记录人": item.recorder
                    })));
                    const wb = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(wb, ws, "交接数据");
                    XLSX.writeFile(wb, `交接列表_${new Date().toLocaleDateString()}.xlsx`
                );
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            alert('获取数据失败: ' + error.message);
        }
    }
    //#endregion
    
    //#region 分界线：>>交接分析页面<<
    // 交接分析页面 - 图表 测试函数
    initECharts(){
        // 基于准备好的dom，初始化echarts实例 
        var myChart = echarts.init(document.getElementById('EChartsDOM')); 
        // 指定图表的配置项和数据 
        var option = { 
            title: { text: 'ECharts 入门示例' }, 
            tooltip: {}, 
            legend: { data: ['销量'] }, 
            xAxis: { data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子'] }, 
                yAxis: {}, 
                series: [ { name: '销量', type: 'bar', data: [5, 20, 36, 10, 10, 20] } ] 
            }; 
        // 使用刚指定的配置项和数据显示图表。 
        myChart.setOption(option); 
    }

    // 交接分析页面 - 初始化交接分析页面
    initializeAnalysisForm() {
        // 初始化LINE和UNIT选项
        this.loadAnalysisOptions();
        
        // 绑定查询表单提交事件
        const analysisForm = document.getElementById('assAnalysisForm');
        if (analysisForm) {
            analysisForm.addEventListener('submit', (e) => {
                e.preventDefault();
                //this.currentPage = 1; // 重置到第一页
                this.fetchAnalysisData(); // 获取查询数据
            });
        }

        this.chartType = document.getElementById('assAnalysis-tableShow').value;

        console.log('this.chartType',this.chartType); 
        // 显示图表
        this.drawAnalysisECharts(this.analysisData,this.chartType);
    }

    // 交接分析页面 - 加载分析表单的选项数据
    async loadAnalysisOptions() {
        try {
            // 获取用户科室
            const section = this.userInfo?.section || '';
            let project = this.getProject(this.userInfo.level);
            console.log('加载页面选择前1 - project:',project);
            

            // 加载Project选项
            const projectSelect = document.getElementById('assAnalysis-project');
            const projectResponse = await fetch(`php/get_options.php?type=project&section=${section}`);
            const projectData = await projectResponse.json();
            if (projectData.success) {
                if (projectSelect) {
                    projectSelect.innerHTML = '<option value="">全部</option>' + 
                        projectData.data.map(project => `<option value="${project}">${project}</option>`).join('');
                }
            }

            // 添加project变化时更新line和unit的监听
            projectSelect?.addEventListener('change', async () => {
                project = projectSelect.value;
                // 如果project未选择，加载该科室下所有line 和 unit
                const url_line = project ? 
                    `php/get_options.php?type=line&section=${section}&project=${project}` :
                    `php/get_options.php?type=line&section=${section}`;
                const url_unit = project ? 
                    `php/get_options.php?type=unit&section=${section}&project=${project}` :
                    `php/get_options.php?type=unit&section=${section}`;
                try {
                    const response = await fetch(url_line);
                    const data = await response.json();
                    if (data.success) {
                        lineSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(line => `<option value="${line}">${line}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载line选项失败:', error);
                }

                try {
                    const response = await fetch(url_unit);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });
            console.log('加载页面选择后 - project:',project);
            // 加载LINE选项
            const lineResponse = await fetch(`php/get_options.php?type=line&section=${section}&project=${project}`);
            const lineData = await lineResponse.json();
            if (lineData.success) {
                const lineSelect = document.getElementById('assAnalysis-line');
                if (lineSelect) {
                    lineSelect.innerHTML = '<option value="">全部</option>' + 
                        lineData.data.map(line => `<option value="${line}">${line}</option>`).join('');
                }
            }

            // 加载UNIT选项
            const unitResponse = await fetch(`php/get_options.php?type=unit&section=${section}&project=${project}`);
            const unitData = await unitResponse.json();
            if (unitData.success) {
                const unitSelect = document.getElementById('assAnalysis-unit');
                if (unitSelect) {
                    unitSelect.innerHTML = '<option value="">全部</option>' + 
                        unitData.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                }
            }

            // 添加line变化时更新unit的监听
            const lineSelect = document.getElementById('assAnalysis-line');
            const unitSelect = document.getElementById('assAnalysis-unit');
            lineSelect?.addEventListener('change', async () => {
                const line = lineSelect.value;
                // 如果line未选择，加载该科室下所有unit
                const url = line ? 
                    `php/get_options.php?type=unit&section=${section}&line=${line}&project=${project}` :
                    `php/get_options.php?type=unit&section=${section}&project=${project}`;
                
                try {
                    const response = await fetch(url);
                    const data = await response.json();
                    if (data.success) {
                        unitSelect.innerHTML = '<option value="">全部</option>' + 
                            data.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                    }
                } catch (error) {
                    console.error('加载unit选项失败:', error);
                }
            });

        } catch (error) {
            console.error('加载选项失败:', error);
        }
    }

    // 交接分析页面 - 显示图表
    async fetchAnalysisData() {
        try {
            const form = document.getElementById('assAnalysisForm');
            const formData = new FormData(form);
            const project = this.getProject(this.userInfo?.level);
            this.chartType = document.getElementById('assAnalysis-tableShow').value;    //图表类型
            const tableFor = document.getElementById('assAnalysis-tableFor').value;     //图表呈现对象
            
            console.log('tableFor',tableFor);
            // 构建查询参数
            const params = new URLSearchParams();
            formData.forEach((value, key) => {
                if (value) params.append(key, value);
            });
            // params.append('page', this.currentPage);
            // params.append('pageSize', this.pageSize);

            // 如果没有指定科室参数，默认使用当前用户的科室
            if (!params.has('section') && this.userInfo?.section) {
                params.append('section', this.userInfo.section);
            }
            // 如果没有指定project参数，默认使用当前用户的project
            if (!params.has('project') && project) {
                params.append('project', project);
            }

            const response = await fetch(`php/get_associates_analysis.php?tableFor=${tableFor}&${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                this.analysisData = result.data;  //查询结果获取
                //this.renderAssociateData(result.data);
                this.drawAnalysisECharts(result.data,this.chartType);   // 显示图表
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            alert('获取数据失败: ' + error.message);
        }
    }

    // 交接分析页面 - 画图表
    drawAnalysisECharts(data,chartType){
        var myChart=null;
        // data 准备
        const lines = data.map(item => {
            // console.log('item:',item);
            return item.name;
        });
        const qtys = data.map(item => {
            return item.value;
        });
        const pies = data.map(({ name, value }) => ({ value, name })); 
        
        // console.log('data.line:',lines );
        // console.log('data.qty:',qtys);
        // console.log('data.pies:',pies);

        switch(chartType){
            case 'bar':
                // 基于准备好的dom，初始化echarts实例 
                myChart = echarts.init(document.getElementById('EChartsDOM'));
                var option = { 
                    title: { text: '交接分析' }, 
                    tooltip: {}, 
                    legend: { data: ['交接数量'] }, 
                    xAxis: { 
                        show: true,
                        data: lines 
                    }, 
                    yAxis: {
                        show: true,
                    }, 
                    series: [ { 
                        name: '交接数量', 
                        type: 'bar', 
                        data: qtys ,
                        label:{
                            show: true,
                            position: 'top'
                        }
                        
                    } ] 
                };
                // 使用刚指定的配置项和数据显示图表。 
                myChart.setOption(option); 
                break;
            case 'line':
                // 基于准备好的dom，初始化echarts实例 
                myChart = echarts.init(document.getElementById('EChartsDOM'));
                var option = { 
                    title: { text: '交接分析' }, 
                    tooltip: {}, 
                    legend: { data: ['交接数量'] }, 
                    xAxis: {
                        show: true,
                        type: 'category',
                        data: lines
                    },
                    yAxis: {
                        show: true,
                        type: 'value'
                    },
                    series: [{
                            data:qtys,
                            type: 'line',
                            label:{
                                show: true,
                                position: 'top'
                            }
                        }
                    ]
                };
                // 使用刚指定的配置项和数据显示图表。 
                myChart.setOption(option); 
                break;
            case 'pie':
                // 基于准备好的dom，初始化echarts实例 
                myChart = echarts.init(document.getElementById('EChartsDOM'));
                var option = { 
                    title: { text: '交接分析' }, 
                    tooltip: {}, 
                    legend: {data:[
                        ''
                    ]}, 
                    xAxis: {
                        show:false
                    },
                    yAxis: {
                        show:false
                    },
                    series: [
                        {
                            type: 'pie',
                            data: pies, 
                            radius: '75%',
                            label:{
                                show: true,
                                
                                position: 'outside'    // 'inside'
                            }
                        }
                    ]
                };
                // 使用刚指定的配置项和数据显示图表。 
                myChart.setOption(option); 
                break;
            default:
                break;
        }
         
        
    }
    //#endregion

    //#region 分界线：>>其他方法<<
    // 格式化日期 参数 字符串
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: '2-digit',
            month: '2-digit',
            day: '2-digit'
        });
    }

    // 格式化日期 参数 Date 类型
    formatDate1(date) {
        //const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: '2-digit',
            month: '2-digit',
            day: '2-digit'
        });
    }

    // 计算LG日期
    formatNewDate(dateString,classes) {
        const date = new Date(dateString);
        if(classes === 'LG'){
            // 获取当前小时数（24小时制） 
	        const hours = date.getHours(); 
            // 如果小时数小于12，返回前一天的日期 
            if (hours < 12) { 
                // 创建一个新的 Date 对象，将时间设置为前一天 
                const previousDay = new Date(date);
                previousDay.setDate(date.getDate() - 1); 
                return this.formatDate1(previousDay); 
                // return this.dateToDate(previousDay); 
            } else { 
                // 否则返回当前日期 
                return this.formatDate1(date); 
                // return this.dateToDate(date); 
            } 
        }
        return this.formatDate1(date); 
        // return this.dateToDate(date); 
    }

    dateToNewDate(date,classes) {
        //const date = new Date(dateString);
        if(classes === 'LG'){
            // console.log('Test1 date:',date);
            // 获取当前小时数（24小时制） 
	        const hours = date.getHours(); 
            // 如果小时数小于12，返回前一天的日期 
            if (hours < 12) { 
                // 创建一个新的 Date 对象，将时间设置为前一天 
                const previousDay = new Date(date);
                previousDay.setDate(date.getDate() - 1); 
                // console.log('Test2 previousDay:',previousDay);
                // return this.formatDate1(previousDay); 
                return this.dateToDate(previousDay); 
            } else { 
                // 否则返回当前日期 
                // return this.formatDate1(date); 
                return this.dateToDate(date); 
            } 
        }
        // console.log('Test3 date:',date);
        // return this.formatDate1(date); 
        return this.dateToDate(date); 
    }

    dateToDate(date){
        // console.log('Test11 date:',date);
        const year = date.getFullYear(); 
        const month = String(date.getMonth() + 1).padStart(2, '0'); 
        const day = String(date.getDate()).padStart(2, '0'); 
        return year+'/'+month+'/'+day; 
    }
    // 计算班次
    calculateShift(dateString) {
        if (!dateString) return '';
        try {
            const [datePart, timePart] = dateString.split(' ');
            if (!timePart) return '';
            const [hours, minutes] = timePart.split(':').map(Number);
            const time = hours + minutes / 60;
            if (time >= 7.5 && time < 19.5) {
                return "LD";
            } else {
                return "LG";
            }
        } catch (error) {
            console.error('时间解析错误:', error);
            return '';
        }
    }

    // 用于html语句的传值
    safeValue(value, defaultValue = '') {
        return value || defaultValue;
    }

    // 初始化模态框
    initModal() {
        this.modal = document.getElementById('associateDetailModal');
        if (!this.modal) return;

        // 关闭模态框的方法
        const closeModal = () => {
            this.modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        };

        // 绑定关闭按钮事件
        this.modal.querySelector('.close')?.addEventListener('click', closeModal);
        this.modal.querySelector('.btn-return')?.addEventListener('click', closeModal);

        // 点击模态框外部关闭
        this.modal.addEventListener('click', e => {
            if (e.target === this.modal) closeModal();
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', e => {
            if (e.key === 'Escape' && this.modal.style.display === 'block') {
                closeModal();
            }
        });

        
        
    }

    getProject(level){
        // 根据用户权限设置 project
        if(!level) return '';
        switch(this.userInfo?.level) {
            case 21:
                return 'OC';
            case 22:
                return 'LCM';
            case 23:
                return 'LOG';//物流
            case 20:
                return '';
            default:
                return '';
        }
    }
    //#endregion
}

// 创建实例
const associateManager = new AssociateManager();

// 页面加载完成后初始化查询表单
document.addEventListener('DOMContentLoaded', () => {
    associateManager.initializeSearchForm();
    associateManager.initializeAnalysisForm();

    // 自动加载第一页数据
    associateManager.fetchAssociateData();
}); 