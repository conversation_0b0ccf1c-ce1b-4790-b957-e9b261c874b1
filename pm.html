﻿<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"> 
    <meta http-equiv="Pragma" content="no-cache"> <meta http-equiv="Expires" content="0"> 
    <title>PM管理 - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/tabs.js" defer></script>
    <script src="js/pm.js" defer></script>
    <script src="js/echarts.js" defer></script>
    <script src="js/xlsx.js"></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'pmSearch':
                        switchTab('pmSearch');
                        break;
                    case 'pmRegister':
                        switchTab('pmRegister');
                        break;
                    default:
                        switchTab('pmSearch');
                }
            }
        });
    </script>
</head>
<body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <div class="tabs">
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('pmSearch')">PM查询</button>
                <button class="tab-button" onclick="switchTab('pmRegister')">PM登录</button>
            </div>
        </div>
            
        <!-- PM查询选项卡 -->
        <div id="pmSearchTab" class="tab-content active">
            <div class="asssearch">
                <form id="asssearchForm" action="#" method="get">
                    <div class="asssearch-row">
                        
                        <div id="projectSelect">
                            <label for="asssearch-project">工程</label>
                            <select id="asssearch-project" name="project" style="width: 90px;">
                            </select>
                        </div>

                        <label for="asssearch-line">LINE</label>
                        <select id="asssearch-line" name="line" style="width: 90px;">
                        </select>
                        
                        <label for="asssearch-unit">UNIT</label>
                        <select id="asssearch-unit" name="unit">
                        </select>

                        <label for="asssearch-problemtype">故障分类</label>
                        <select id="asssearch-problemtype" name="problemtype">
                            <option value="">请选择</option>
                            <option value="一般">一般</option>
                            <option value="硬件干涉">硬件干涉</option>
                            <option value="程序bug">程序bug</option>
                            <option value="部件损坏">部件损坏</option>
                        </select>

                        <label for="asssearch-needfollow">需要跟进</label>
                        <select id="asssearch-needfollow" name="needfollow">
                            <option value="">请选择</option>
                            <option value="是">是</option>
                            <option value="否">否</option>
                        </select>

                        <label for="asssearch-status">是否结案</label>
                        <select id="asssearch-status" name="status">
                            <option value="">请选择</option>
                            <option value="open">open</option>
                            <option value="close">close</option>
                        </select>

                        <label for="asssearch-problemKeyword">关键词</label>
                        <input type="text" id="asssearch-problemKeyword" name="problemKeyword" placeholder="请输入关键词查询">
                        <label for="asssearch-start-date">日期</label>
                        <input type="date" id="asssearch-start-date" name="start_date">
                        
                        <label for="asssearch-end-date">~</label>
                        <input type="date" id="asssearch-end-date" name="end_date">

                        

                        <div class="button-group">
                            <button type="submit" class="asssearch-btn">查询</button>
                            <button type="reset" class="assreset-btn">重置</button>
                            <button type="button" class="assDownload-btn">导出</button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="asslist">
                <table class="data-table" id="assSearchTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>LINE</th>
                            <th>UNIT</th>
                            <th>故障部位</th>
                            <th>现象</th>
                            <th>原因</th>
                            <th>处理内容</th>
                            <th>故障分类</th>
                            <th>需要跟进</th>
                            <th>是否结案</th>
                            <!-- <th>故障图片</th> -->
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过 JavaScript 动态加载 -->
                    </tbody>
                </table>
                <!-- 右键菜单 -->
                <div id="contextMenu" style="display:none; position:absolute; background:#fff; border:1px solid #ccc; padding:5px;"> 
                    <button onclick="associateManager.exportToExcel()">导出Excel</button> 
                </div>                 
            </div>

            <!-- 在表格后添加分页控件 -->
            <div class="pagination">
                <div class="pagination-info">
                    共 <span class="total-count">0</span> 条记录，
                    每页 <select class="page-size">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select> 条
                </div>
                <div class="pagination-controls">
                    <button class="btn-first-page">首页</button>
                    <button class="btn-prev-page">上一页</button>
                    <span class="page-info">
                        第 <input type="number" class="current-page" min="1"> 页，
                        共 <span class="total-pages">0</span> 页
                    </span>
                    <button class="btn-next-page">下一页</button>
                    <button class="btn-last-page">末页</button>
                </div>
            </div>
        </div>

        <!-- PM登录选项卡 -->
        <div id="pmRegisterTab" class="tab-content">
            <div class="assregister">
                <form action="#" method="post" class="assregister-form">
                    <!-- 表头行 -->
                    <table class="register-table">
                        <tr>
                            <th>LINE</th>
                            <th>UNIT</th>
                            <th>故障分类</th>
                            <th>故障部位</th>
                            <th>现象</th>
                            <th>原因</th>
                            <th>处理内容</th>
                            <th>需要跟进</th>
                            <th>故障图片</th>
                            <th>操作</th>
                        </tr>
                        <!-- 初始10行空表格 -->
                        <tbody id="registerTableBody">
                            <!-- 这里将通过JavaScript动态生成10行空表格 -->
                        </tbody>
                    </table>

                    <div class="form-actions">
                        <button type="button" class="btn-add-rows">添加更多行</button>
                        <button type="submit" class="btn-submit">提交</button>
                        <button type="reset" class="btn-reset">重置</button>
                        <p style="color: red;">*请删除多余空行后提交！</p>
                    </div>
                </form>
            </div>
        </div>

        <!-- 交接分析选项卡 -->
        <div id="assAnalysisTab" class="tab-content">
            <div class="assAnalysis">
                <form id="assAnalysisForm" action="#" method="get">
                    <div class="assAnalysis-row">
                        
                        <div id="projectSelect">
                            <label for="assAnalysis-project">工程</label>
                            <select id="assAnalysis-project" name="project" style="width: 90px;">
                            </select>
                        </div>

                        <label for="assAnalysis-line">LINE</label>
                        <select id="assAnalysis-line" name="line" style="width: 90px;">
                        </select>
                        
                        <label for="assAnalysis-unit">UNIT</label>
                        <select id="assAnalysis-unit" name="unit">
                        </select>

                        <label for="assAnalysis-problemtype">故障分类</label>
                        <select id="assAnalysis-problemtype" name="problemtype">
                            <option value="">请选择</option>
                            <option value="一般">一般</option>
                            <option value="硬件干涉">硬件干涉</option>
                            <option value="程序bug">程序bug</option>
                            <option value="部件损坏">部件损坏</option>
                        </select>

                        <label for="assAnalysis-needfollow">需要跟进</label>
                        <select id="assAnalysis-needfollow" name="needfollow">
                            <option value="">请选择</option>
                            <option value="是">是</option>
                            <option value="否">否</option>
                        </select>

                        <label for="assAnalysis-status">是否结案</label>
                        <select id="assAnalysis-status" name="status">
                            <option value="">请选择</option>
                            <option value="open">open</option>
                            <option value="close">close</option>
                        </select>

                        <label for="assAnalysis-start-date">日期</label>
                        <input type="date" id="assAnalysis-start-date" name="start_date">
                        
                        <label for="assAnalysis-end-date">~</label>
                        <input type="date" id="assAnalysis-end-date" name="end_date">

                        <label for="assAnalysis-tableShow">图表呈现</label>
                        <select id="assAnalysis-tableShow" name="tableShow">
                            <option value="bar">柱状图</option>
                            <option value="line">折线图</option>
                            <option value="pie">饼图</option>
                        </select>

                        <label for="assAnalysis-tableFor">呈现对象</label>
                        <select id="assAnalysis-tableFor" name="tableFor">
                            <option value="forLine">LINE</option>
                            <option value="forUnit">UNIT</option>
                        </select>

                        <div class="button-group">
                            <button type="submit" class="assAnalysis-btn">查询</button>
                            <button type="reset" class="assAnalysisReset-btn">重置</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="assAnalysisECharts">
                <!-- 为 ECharts 准备一个定义了宽高的 DOM -->
                <div id="EChartsDOM" style="width: 800px;height:600px;"></div>
            </div>
        </div>

        <!-- 班组交接选项卡 -->
        <div id="assShiftTab" class="tab-content">
            <div class="shift-manpower">
                <form class="shift-manpower-form">
                    <div class="shift-manpower-row">
                        <label for="shift-manpower-date">日期</label>
                        <input type="date" id="shift-manpower-date" name="shift_manpower_date">

                        <label for="assShift-shift">班组</label>
                        <select id="assShift-shift" name="shift" style="width: 90px;">
                            <option value="A" selected>A</option>
                            <option value="B">B</option>
                            <option value="C">C</option>
                        </select>

                        <label for="assShift-classes">班次</label>
                        <select id="assShift-classes" name="classes" style="width: 90px;">
                            <option value="LD" selected>LD</option>
                            <option value="LG">LG</option>
                        </select>

                        <label for="assShift-manCount">本班应到人数</label>
                        <input type="text" name="assShift-manCount" style="width: 50px;">

                        <label for="assShift-manCountActual">本班实到人数</label>
                        <input type="text" name="assShift-manCountActual" style="width: 50px;">

                        <label for="assShift-manCountExtra">加班人数</label>
                        <input type="text" name="assShift-manCountExtra" style="width: 50px;">

                        <div class="shift-button-group">
                            <button type="submit" class="assShift-submit-btn">确定</button>
                            <button type="reset" class="assShift-reset-btn">重置</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="assShift">
                <form action="#" method="post" class="assShift-form">
                    <!-- 表头行 -->
                    <table class="shift-table">
                        <tr>
                            <th>LINE</th>
                            <th>UNIT</th>
                            <th>故障分类</th>
                            <th>现象</th>
                            <th>原因</th>
                            <th>处理内容</th>
                            <th>需要跟进</th>
                            <th>故障图片</th>
                            <th>操作</th>
                        </tr>
                        <!-- 初始10行空表格 -->
                        <tbody id="shiftTableBody">
                            <!-- 这里将通过JavaScript动态生成10行空表格 -->
                        </tbody>
                    </table>

                    <div class="shift-form-actions">
                        <button type="button" class="btn-add-shift-rows">添加更多行</button>
                        <button type="submit" class="btn-shift-submit">提交</button>
                        <button type="reset" class="btn-shift-reset">重置</button>
                        <p style="color: red;">*请删除多余空行后提交！</p>
                    </div>
                </form>
            </div>

            <div class="assShiftECharts">
                <!-- 为 ECharts 准备一个定义了宽高的 DOM -->
                <div id="ShiftEChartsDOM" style="width: 800px;height:600px;"></div>
            </div>
        </div>
    </div>

    <!-- 在交接列表表格后添加 处理 弹窗结构 -->
    <div id="associateDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>标题,line unit 故障现象</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <table class="detail-table">
                    <tr>
                        <th>科室</th>
                        <th>LINE</th>
                        <th>UNIT</th>
                        <th>故障现象</th>
                        <!-- <th>故障原因</th> -->
                        <!-- <th>处理内容</th> -->
                        <th>故障部件</th>
                        <th>故障代码</th>
                        <th>需要跟进</th>
                        <th>发生时间</th>
                        <th>交接担当</th>
                    </tr>
                    <tr>
                        <!-- 这里将通过JavaScript动态生成 -->
                    </tr>
                </table>

                <div class="detail-sections" id="detail-sections">
                    
                    <div class="detail-section">
                        <h3>故障原因</h3>
                        <div class="section-content">
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>处理内容</h3>
                        <div class="section-content">
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>是否结案</h3>
                        <div class="section-content">
                            
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>相关附件</h3>
                        
                        <div class="section-content">
                            <!-- 附件将在这里动态显示 -->
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn-return">返回</button>
                    <!-- <button class="btn-modify" id="modal-btn-modify">修改</button> -->
                     <div class="modal-btn-modify">

                     </div>
                </div>
                <!-- 历史履历显示在下方 -->
                <div class="history" id="history" style="overflow: scroll; max-height: 250px;">
                    <div class="history-title">
                        <h3 onclick="pmAssociateManager.historyTitleClick(pmAssociateManager.bShowHistory)">点击历史履历信息展开</h3>
                    </div>
                    <div class="history-body" id="history-body" style="display: none;">
                        <table class="history-body-table" id="history-body-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>故障原因</th>
                                    <th>处理内容</th>
                                    <th>发生时间</th>
                                    <th>交接担当</th>
                                    <th>相关附件</th>
                                </tr>
                            </thead>
                            
                            <tbody>
                                <!-- 数据将通过 JavaScript 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
          </div>
      </div>
    </div>
</body>
</html> 