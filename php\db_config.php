﻿<?php
// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

$servername = "localhost";
$username = "root";
$password = "";
$dbname = "equipment_management";

//  $servername = "************";
//  $username = "eqpmanage";
//  $password = "eqpmanage";
//  $dbname = "eqp_management";

 

try {
    // 创建连接
    $conn = new mysqli($servername, $username, $password, $dbname);

    // 检查连接
    if ($conn->connect_error) {
        throw new Exception("连接失败: " . $conn->connect_error);
    }

    // 设置字符集
    if (!$conn->set_charset("utf8mb4")) {
        throw new Exception("设置字符集失败: " . $conn->error);
    }

} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    die("数据库连接错误，请查看错误日志");
}
?> 