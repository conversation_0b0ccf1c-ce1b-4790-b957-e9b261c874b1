﻿/* 模态框样式 */ 
.modal { 
    display: none; 
    position: fixed; 
    z-index: 1; 
    left: 0; 
    top: 0; 
    width: 100%; 
    height: 100%; 
    overflow: auto; 
    background-color: rgba(0, 0, 0, 0.4); 
} 
.modal-content { 
    background-color: #fefefe; 
    margin: 15% auto; 
    padding: 20px; 
    border: 1px solid #888; 
    width: 80%; 
} 
.close { 
    color: #aaa; 
    float: right; 
    font-size: 28px; 
    font-weight: bold; 
} 
.close:hover, 
.close:focus { 
    color: black; 
    text-decoration: none; 
    cursor: pointer; 
} 
/* 红色文本样式 */ 
.error { 
    color: red; 
} 