<?php
header('Content-Type: application/json');
require_once 'db_config.php';

try {
    $associate_id = $_GET['associate_id'] ?? null;

    if (!$associate_id) {
        throw new Exception('缺少associate_id参数');
    }

    $sql = "SELECT * FROM associate_files WHERE associate_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $associate_id);
    $stmt->execute();
    $result = $stmt->get_result();

    $files = [];
    while ($row = $result->fetch_assoc()) {
        $files[] = [
            'id' => $row['id'],
            'file_name' => $row['file_name'],
            'file_path' => $row['file_path'],
            'file_type' => $row['file_type'],
            'file_size' => $row['file_size']
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => $files
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 