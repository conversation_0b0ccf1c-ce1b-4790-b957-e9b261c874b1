<?php
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// 数据库配置信息
$serverName = "109.120.2.35";
$connectionOptions = array(
    "Database" => "Tact_Time",
    "Uid" => "eqplink",
    "PWD" => "eqplink",
    "CharacterSet" => "UTF-8"
);

$conn = null; // 初始化连接变量

try {
    $conn = sqlsrv_connect($serverName, $connectionOptions);

    if ($conn === false) {
        throw new Exception("Connection failed: " . print_r(sqlsrv_errors(), true));
    }

    $response = ['success' => true];

    if (isset($_GET['line']) && !empty($_GET['line'])) {
        // 如果提供了line参数，则获取该line下的units
        $selectedLine = $_GET['line'];
        $unitQuery = "SELECT DISTINCT UNITS FROM dbo.TT_Main WHERE UNITS IS NOT NULL AND LINE = ? ORDER BY UNITS";
        $params = [$selectedLine];
        $unitResult = sqlsrv_query($conn, $unitQuery, $params);

        if ($unitResult === false) {
            throw new Exception("Unit query failed: " . print_r(sqlsrv_errors(), true));
        }

        $units = [];
        while($row = sqlsrv_fetch_array($unitResult, SQLSRV_FETCH_ASSOC)) {
            $units[] = $row['UNITS'];
        }
        $response['units'] = $units;

    } else {
        // 如果没有提供line参数，则获取所有的lines
        $lineQuery = "SELECT DISTINCT LINE FROM dbo.TT_Main WHERE LINE IS NOT NULL ORDER BY LINE";
        $lineResult = sqlsrv_query($conn, $lineQuery);

        if ($lineResult === false) {
            throw new Exception("Line query failed: " . print_r(sqlsrv_errors(), true));
        }

        $lines = [];
        while($row = sqlsrv_fetch_array($lineResult, SQLSRV_FETCH_ASSOC)) {
            $lines[] = $row['LINE'];
        }
        $response['lines'] = $lines;
    }

    echo json_encode($response);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} finally {
    // 确保无论如何都关闭连接
    if ($conn !== null) {
        sqlsrv_close($conn);
    }
}
?>