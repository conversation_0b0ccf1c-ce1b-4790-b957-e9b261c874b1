<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 从数据库shift表格获取日期、班组、班次信息
    $sql = "SELECT date, class, shift FROM shift WHERE date <= CURDATE() AND shift !='休' ORDER BY date DESC";
    $result = $conn->query($sql);
    
    if (!$result) {
        throw new Exception("查询失败: " . $conn->error);
    }
    
    $data = [];
    
    // 班组和楼层对应的领班
    $leaderMapping = [
        'A_1F' => '沈阳',
        'A_2F' => '张宝龙',
        'B_1F' => '王宪坤',
        'B_2F' => '张帅',
        'C_1F' => '赵亚洲',
        'C_2F' => '钱鹏'
    ];
    
    while ($row = $result->fetch_assoc()) {
        $date = $row['date'];
        $class = $row['class'];
        $shift = $row['shift'];
        
        // 为每条记录创建1F和2F两条数据
        $floors = ['1F', '2F'];
        
        foreach ($floors as $floor) {
            // 根据班组和楼层匹配领班
            $leaderKey = $class . '_' . $floor;
            $leader = isset($leaderMapping[$leaderKey]) ? $leaderMapping[$leaderKey] : '';
            
            // 从kpi表格查询对应的数据
            $kpiSql = "SELECT FP, QSM, quality, `5S3D`, env, info FROM kpi WHERE date = ? AND leader = ?";
            $stmt = $conn->prepare($kpiSql);
            $stmt->bind_param("ss", $date, $leader);
            $stmt->execute();
            $kpiResult = $stmt->get_result();
            
            $kpiData = [
                'FP' => null,
                'QSM' => null,
                'quality' => null,
                '5S3D' => null,
                'env' => null,
                'info' => null,
                'kpi' => null
            ];
            
            if ($kpiRow = $kpiResult->fetch_assoc()) {
                $kpiData['FP'] = $kpiRow['FP'];
                $kpiData['QSM'] = $kpiRow['QSM'];
                $kpiData['quality'] = $kpiRow['quality'];
                $kpiData['5S3D'] = $kpiRow['5S3D'];
                $kpiData['env'] = $kpiRow['env'];
                $kpiData['info'] = $kpiRow['info'];
                
                // 计算业绩
                $kpiData['kpi'] = $kpiRow['FP'] - $kpiRow['QSM'] - $kpiRow['quality'] - $kpiRow['5S3D'] - $kpiRow['env'] - $kpiRow['info'];
            }
            
            $data[] = [
                'date' => $date,
                'class' => $class,
                'shift' => $shift,
                'floor' => $floor,
                'leader' => $leader,
                'FP' => $kpiData['FP'],
                'QSM' => $kpiData['QSM'],
                'quality' => $kpiData['quality'],
                '5S3D' => $kpiData['5S3D'],
                'env' => $kpiData['env'],
                'info' => $kpiData['info'],
                'kpi' => $kpiData['kpi']
            ];
            
            $stmt->close();
        }
    }
    
    // 获取所有领班列表（用于下拉选择）
    $leaderList = array_unique(array_values($leaderMapping));
    
    echo json_encode([
        'success' => true,
        'data' => $data,
        'leaders' => $leaderList
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 