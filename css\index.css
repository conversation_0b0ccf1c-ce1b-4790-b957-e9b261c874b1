/* 
 * 首页专用样式文件
 * 实现模块化布局设计，统一视觉风格
 */

/* ===== 顶部导航栏样式 ===== */
.layui-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* ===== 侧边栏样式 ===== */
.layui-side {
    background-color: #f8f9fa !important;
    border-right: 1px solid #e9ecef;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
}

.layui-side-scroll {
    background-color: #f8f9fa !important;
}

/* 侧边栏导航项样式优化 */
.layui-nav-tree .layui-nav-item a {
    color: #495057;
    transition: all 0.3s ease;
}

.layui-nav-tree .layui-nav-item a:hover {
    background-color: #e9ecef;
    color: #007bff;
}

/* ===== 首页主内容区域样式 ===== */
.layui-body {
    background-color: #f8f9fa !important;
}

.main-content {
    background-color: #f8f9fa !important;
    padding: 16px;
    min-height: 100vh;
    box-sizing: border-box;
}

/* ===== 模块化布局设计 ===== */
.content-module {
    background-color: #ffffff;
    border-radius: 8px;
    margin-bottom: 16px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: box-shadow 0.3s ease;
}

.content-module:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* 模块标题样式 */
.module-title {
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
    display: flex;
    align-items: center;
}

.module-title::before {
    content: '';
    width: 4px;
    height: 20px;
    background-color: #007bff;
    margin-right: 12px;
    border-radius: 2px;
}

/* ===== 表格样式优化 ===== */
.content-module .data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background-color: #ffffff;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.content-module .data-table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    padding: 12px 16px;
    text-align: center;
    border: 1px solid #e9ecef;
    white-space: nowrap;
}

.content-module .data-table td {
    padding: 12px 16px;
    border: 1px solid #e9ecef;
    vertical-align: middle;
    color: #495057;
}

.content-module .data-table tbody tr:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

/* ===== 状态标签样式 ===== */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.status-completed {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-expired {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* ===== 操作按钮样式 ===== */
.btn-action {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-view {
    background-color: #007bff;
    color: white;
}

.btn-view:hover {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.btn-edit {
    background-color: #28a745;
    color: white;
}

.btn-edit:hover {
    background-color: #1e7e34;
    transform: translateY(-1px);
}

.btn-delete {
    background-color: #dc3545;
    color: white;
}

.btn-delete:hover {
    background-color: #c82333;
    transform: translateY(-1px);
}

/* ===== 空状态样式 ===== */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state h4 {
    color: #6c757d;
    margin-bottom: 8px;
    font-weight: 500;
}

.empty-state p {
    color: #adb5bd;
    font-size: 14px;
    margin: 0;
}

/* ===== 分隔线样式 ===== */
hr {
    border: none;
    height: 1px;
    background: linear-gradient(to right, transparent, #e9ecef, transparent);
    margin: 24px 0;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .main-content {
        padding: 12px;
    }
    
    .content-module {
        margin-bottom: 12px;
        padding: 16px;
    }
    
    .module-title {
        font-size: 16px;
    }
    
    .content-module .data-table th,
    .content-module .data-table td {
        padding: 8px 12px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 8px;
    }
    
    .content-module {
        margin-bottom: 8px;
        padding: 12px;
    }
    
    .module-title {
        font-size: 14px;
    }
    
    .content-module .data-table {
        font-size: 12px;
    }
    
    .content-module .data-table th,
    .content-module .data-table td {
        padding: 6px 8px;
    }
}

/* ===== 加载动画 ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== 首页特定模块样式 ===== */
.notice-module {
    border-left: 4px solid #ffc107;
}

.notice-module .module-title::before {
    background-color: #ffc107;
}

.notice-module .module-title {
    border-bottom-color: #ffc107;
}

.problem-module {
    border-left: 4px solid #dc3545;
}

.problem-module .module-title::before {
    background-color: #dc3545;
}

.problem-module .module-title {
    border-bottom-color: #dc3545;
}

.feedback-module {
    border-left: 4px solid #28a745;
}

.feedback-module .module-title::before {
    background-color: #28a745;
}

.feedback-module .module-title {
    border-bottom-color: #28a745;
}

.plan-module {
    border-left: 4px solid #17a2b8;
}

.plan-module .module-title::before {
    background-color: #17a2b8;
}

.plan-module .module-title {
    border-bottom-color: #17a2b8;
}

.history-module {
    border-left: 4px solid #6f42c1;
}

.history-module .module-title::before {
    background-color: #6f42c1;
}

.history-module .module-title {
    border-bottom-color: #6f42c1;
}

.progress-module {
    border-left: 4px solid #fd7e14;
}

.progress-module .module-title::before {
    background-color: #fd7e14;
}

.progress-module .module-title {
    border-bottom-color: #fd7e14;
}

/* ===== 首页内容区域特殊处理 ===== */
#homeContent {
    background-color: transparent;
}

/* 重写首页的h3标题样式 */
#homeContent h3 {
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
    display: flex;
    align-items: center;
}

#homeContent h3::before {
    content: '';
    width: 4px;
    height: 20px;
    background-color: #007bff;
    margin-right: 12px;
    border-radius: 2px;
}

/* 为不同模块设置不同的颜色主题 */
#homeContent h3:nth-of-type(1) {
    border-bottom-color: #ffc107;
}

#homeContent h3:nth-of-type(1)::before {
    background-color: #ffc107;
}

#homeContent h3:nth-of-type(2) {
    border-bottom-color: #dc3545;
}

#homeContent h3:nth-of-type(2)::before {
    background-color: #dc3545;
}

#homeContent h3:nth-of-type(3) {
    border-bottom-color: #28a745;
}

#homeContent h3:nth-of-type(3)::before {
    background-color: #28a745;
}

#homeContent h3:nth-of-type(4) {
    border-bottom-color: #17a2b8;
}

#homeContent h3:nth-of-type(4)::before {
    background-color: #17a2b8;
}

#homeContent h3:nth-of-type(5) {
    border-bottom-color: #6f42c1;
}

#homeContent h3:nth-of-type(5)::before {
    background-color: #6f42c1;
}

#homeContent h3:nth-of-type(6) {
    border-bottom-color: #fd7e14;
}

#homeContent h3:nth-of-type(6)::before {
    background-color: #fd7e14;
}

/* 为每个模块区域添加白色背景和圆角 */
.notice-list,
.associate-list,
.associate-list-closed,
.modelchange-list,
.history-list,
.progress-list {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: box-shadow 0.3s ease;
}

.notice-list:hover,
.associate-list:hover,
.associate-list-closed:hover,
.modelchange-list:hover,
.history-list:hover,
.progress-list:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* 为独立的表格也添加模块样式 */
#homeContent > table.data-table {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    transition: box-shadow 0.3s ease;
}

#homeContent > table.data-table:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* ===== 优化表格内容显示 ===== */
.notice-list .data-table td:nth-child(2),
.associate-list .data-table td:nth-child(3),
.associate-list .data-table td:nth-child(4),
.associate-list .data-table td:nth-child(5),
.associate-list-closed .data-table td:nth-child(3),
.associate-list-closed .data-table td:nth-child(4),
.associate-list-closed .data-table td:nth-child(5) {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 鼠标悬停时显示完整内容 */
.notice-list .data-table td:nth-child(2):hover,
.associate-list .data-table td:nth-child(3):hover,
.associate-list .data-table td:nth-child(4):hover,
.associate-list .data-table td:nth-child(5):hover,
.associate-list-closed .data-table td:nth-child(3):hover,
.associate-list-closed .data-table td:nth-child(4):hover,
.associate-list-closed .data-table td:nth-child(5):hover {
    white-space: normal;
    word-wrap: break-word;
    position: relative;
    z-index: 10;
}

/* ===== 空状态内容优化 ===== */
.modelchange-list .data-table td,
.history-list .data-table td,
.progress-list .data-table td {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.modelchange-list .data-table h4,
.history-list .data-table h4,
.progress-list .data-table h4 {
    color: #adb5bd;
    font-weight: 400;
    margin: 0;
}

/* ===== 表格行间距优化 ===== */
.content-module .data-table tbody tr {
    border-bottom: 1px solid #f8f9fa;
}

.content-module .data-table tbody tr:last-child {
    border-bottom: none;
}

/* ===== 滚动条样式优化 ===== */
.main-content::-webkit-scrollbar {
    width: 8px;
}

.main-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== 页面加载动画 ===== */
.content-module {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 为不同模块添加延迟动画 */
.notice-list {
    animation-delay: 0.1s;
}

.associate-list {
    animation-delay: 0.2s;
}

.associate-list-closed {
    animation-delay: 0.3s;
}

.modelchange-list {
    animation-delay: 0.4s;
}

.history-list {
    animation-delay: 0.5s;
}

.progress-list {
    animation-delay: 0.6s;
}
