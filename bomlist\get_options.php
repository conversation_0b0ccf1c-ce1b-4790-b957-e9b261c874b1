<?php
header('Content-Type: application/json');
require_once '../php/db_config.php';

try {
    $type = $_GET['type'] ?? '';
    $section = $_GET['section'] ?? '';
    $line = $_GET['line'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $project = $_GET['project'] ?? '';
    
    if (empty($type)) {
        throw new Exception('参数类型不能为空');
    }

    $sql = '';
    if ($type === 'section') {
        $sql = "SELECT DISTINCT section FROM framework ORDER BY section";
    } elseif ($type === 'line') {
        $sql = "SELECT DISTINCT line FROM framework";
        $conditions = [];
        $params = [];
        $types = '';
        
        if (!empty($section)) {
            $conditions[] = "section = ?";
            $params[] = $section;
            $types .= 's';
        }
        if (!empty($unit)) {
            $conditions[] = "unit = ?";
            $params[] = $unit;
            $types .= 's';
        }
        if (!empty($project)) {
            $conditions[] = "project = ?";
            $params[] = $project;
            $types .= 's';
        }
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        $sql .= " ORDER BY line";
    } elseif ($type === 'unit') {
        $sql = "SELECT DISTINCT unit FROM framework";
        $conditions = [];
        $params = [];
        $types = '';
        
        if (!empty($section)) {
            $conditions[] = "section = ?";
            $params[] = $section;
            $types .= 's';
        }
        if (!empty($line)) {
            $conditions[] = "line = ?";
            $params[] = $line;
            $types .= 's';
        }
        if (!empty($project)) {
            $conditions[] = "project = ?";
            $params[] = $project;
            $types .= 's';
        }
        
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        $sql .= " ORDER BY unit";
    } elseif ($type === 'project') {
        $sql = "SELECT DISTINCT project FROM framework";
        $conditions = [];
        $params = [];
        $types = '';
        
        if (!empty($section)) {
            $conditions[] = "section = ?";
            $params[] = $section;
            $types .= 's';
        }
        if (!empty($line)) {
            $conditions[] = "line = ?";
            $params[] = $line;
            $types .= 's';
        }
        if (!empty($unit)) {
            $conditions[] = "unit = ?";
            $params[] = $unit;
            $types .= 's';
        }
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }
        $sql .= " ORDER BY project";
    } else {
        throw new Exception('不支持的选项类型');
    }

    $stmt = $conn->prepare($sql);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_array(MYSQLI_NUM)) {
        $data[] = $row[0];
    }

    echo json_encode([
        'success' => true,
        'data' => $data
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 