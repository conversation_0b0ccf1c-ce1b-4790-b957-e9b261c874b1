document.addEventListener('DOMContentLoaded', function() {
    loadDemandTable();
});

async function loadDemandTable() {
    const table = document.querySelector('.data-table');
    try {
        showLoading(table);
        const response = await fetch('http://**************:7070/equipment_management/php/get_jig.php');
        const { demand, inventory } = await response.json();
        const { dates, cuts, matrix } = transformDemandData(demand, inventory);
        renderDemandTable(dates, cuts, matrix);
    } catch (error) {
        console.error('数据加载失败:', error);
        showError(table, error);
    }
}

// function transformDemandData(demandData, inventoryData) {
//     const dates = [...new Set(demandData.map(d => d.rdate))]
//                  .sort((a, b) => new Date(a) - new Date(b));
    
//     // 获取所有金型并排序
//     const cuts = [...new Set(demandData.map(d => d.CUT))]
//                 .sort((a, b) => a.localeCompare(b));

//     const matrix = cuts.map(cut => {
//         const row = {
//             cut,
//             inventory: inventoryData[cut] || 0
//         };
        
//         dates.forEach(date => {
//             const demand = demandData.find(d => 
//                 d.CUT === cut && d.rdate === date
//             );
//             row[date] = demand ? demand.TotalDemand : 0;
//         });
        
//         return row;
//     });

//     return { dates, cuts, matrix };
// }


function transformDemandData(demandData, inventoryData) {
    const dates = [...new Set(demandData.map(d => d.rdate))].sort((a, b) => new Date(a) - new Date(b));
    const cuts = [...new Set(demandData.map(d => d.CUT))].sort((a, b) => a.localeCompare(b));

    const matrix = cuts.map(cut => {
        const row = { cut, inventory: inventoryData[cut] || 0 };
        const demands = [];
        dates.forEach(date => {
            const demand = demandData.find(d => d.CUT === cut && d.rdate === date);
            const value = demand ? parseInt(demand.TotalDemand, 10) : 0;
            row[date] = value;
            demands.push(value);
        });
        const sortedDemands = [...demands].sort((a, b) => b - a);
        row.maxDemand = sortedDemands[0] || 0;
        return row;
    });

    return { dates, cuts, matrix };
}

// 表格函数
// function renderDemandTable(dates, cuts, matrix) {
//     const table = document.querySelector('.data-table');
//     table.innerHTML = '';

//     // 创建表头
//     const thead = document.createElement('thead');
//     const headerRow = document.createElement('tr');
//     headerRow.innerHTML = `
//         <th>金型</th>
//         <th>当前库存</th>
//         ${dates.map(d => `<th>${d}</th>`).join('')}
        
//     `;
//     thead.appendChild(headerRow);
//     table.appendChild(thead);

//     // 创建表格主体
//     const tbody = document.createElement('tbody');
//     matrix.forEach(rowData => {
//         const tr = document.createElement('tr');
//         tr.innerHTML = `
//             <td>${rowData.cut}</td>
//             <td class="inventory-cell">${rowData.inventory}</td>
//             ${dates.map(date => `
//                 <td>${rowData[date]}</td>
//             `).join('')}
            
//         `;
//         tbody.appendChild(tr);
//     });
//     table.appendChild(tbody);
// }

function renderDemandTable(dates, cuts, matrix) {
    const table = document.querySelector('.data-table');
    table.innerHTML = '';

    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>金型</th>
            <th>当前库存</th>
            <th>最大需求</th>
            <th>差异</th>
            ${dates.map(date => `<th>${date}</th>`).join('')}
        </tr>
    `;
    table.appendChild(thead);

    const tbody = document.createElement('tbody');
    matrix.forEach(rowData => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${rowData.cut}</td>
            <td class="inventory-cell">${rowData.inventory}</td>
            <td class="inventory-cell">${rowData.maxDemand}</td>
            <td class=" ${rowData.inventory - rowData.maxDemand<0 ? 'highlight':'inventory-cell'}">${rowData.inventory - rowData.maxDemand}</td>
            ${dates.map(date => `
                <td class="${rowData[date] > rowData.inventory ? 'highlight' : ''}">
                    ${rowData[date]}
                </td>
            `).join('')}
        `;
        tbody.appendChild(tr);
    });
    table.appendChild(tbody);
}


function showLoading(table) {
    table.innerHTML = `
        <tr>
            <td colspan="100%" style="text-align: center; padding: 40px;">
                <div class="loading-spinner"></div>
                <div>数据加载中...</div>
            </td>
        </tr>
    `;
}

function showError(table, error) {
    table.innerHTML = `
        <tr>
            <td colspan="100%" style="color: red; padding: 20px;">
                数据加载失败：${error.message}
            </td>
        </tr>
    `;
}