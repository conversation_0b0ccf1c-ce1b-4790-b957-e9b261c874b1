<?php
header('Content-Type: application/json');
require_once 'db_config.php';

try {
    $associate_id = $_GET['associate_id'] ?? null;
    $section = $_GET['section'] ?? '';
    $line = $_GET['line'] ?? '';
    $classes = $_GET['classes'] ?? '';
    $project = $_GET['project'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $phenomenon = $_GET['phenomenon'] ?? '';
    $problemtype = $_GET['problemtype'] ?? '';
    $created_at = $_GET['created_at'] ?? '';

    if (!$associate_id) {
        throw new Exception('缺少associate_id参数');
    }

    // 构建WHERE子句
    $where = [];
    $params = [];
    $types = '';

    if (!empty($section)) {
        $where[] = "section = ?";
        $params[] = $section;
        $types .= 's';
    }

    if (!empty($classes)) {
        $where[] = "classes = ?";
        $params[] = $classes;
        $types .= 's';
    }

    if (!empty($line)) {
        $where[] = "line = ?";
        $params[] = $line;
        $types .= 's';
    }

    if (!empty($project)) {
        $where[] = "project = ?";
        $params[] = $project;
        $types .= 's';
    }

    if (!empty($unit)) {
        $where[] = "unit = ?";
        $params[] = $unit;
        $types .= 's';
    }

    if (!empty($phenomenon)) {
        $where[] = "phenomenon = ?";
        $params[] = $phenomenon;
        $types .= 's';
    }
    
    if (!empty($problemtype)) {
        $where[] = "problemtype = ?";
        $params[] = $problemtype;
        $types .= 's';
    }

    if (!empty($created_at)) {
        $where[] = "created_at = ?";
        $params[] = $created_at;
        $types .= 's';
    }

    $idSql = "SELECT id FROM associatelist";
    if (!empty($where)) {
        $idSql .= " WHERE " . implode(" AND ", $where);
    }
    $idStmt = $conn->prepare($idSql);
    if (!empty($params)) {
        $idStmt->bind_param($types, ...$params);
    }
    $idStmt->execute();
    $idResult = $idStmt->get_result();

    $files = [];
    while($ids = $idResult->fetch_assoc()){
        $fileSql = "SELECT * FROM associate_files where associate_id = ?";
        $fileStmt = $conn->prepare($fileSql);
        $fileStmt->bind_param("i", $ids['id']);
        $fileStmt->execute();
        $fileResult = $fileStmt->get_result();
        while($row = $fileResult->fetch_assoc()){
            $files[] = [
                'id' => $row['id'],
                'file_name' => $row['file_name'],
                'file_path' => $row['file_path'],
                'file_type' => $row['file_type'],
                'file_size' => $row['file_size']
            ];
        }
    }

    // $sql = "SELECT * FROM associate_files WHERE associate_id = ?";
    // $stmt = $conn->prepare($sql);
    // $stmt->bind_param('i', $associate_id);
    // $stmt->execute();
    // $result = $stmt->get_result();

    // $files = [];
    // while ($row = $result->fetch_assoc()) {
    //     $files[] = [
    //         'id' => $row['id'],
    //         'file_name' => $row['file_name'],
    //         'file_path' => $row['file_path'],
    //         'file_type' => $row['file_type'],
    //         'file_size' => $row['file_size']
    //     ];
    // }

    echo json_encode([
        'success' => true,
        'data' => $files
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 