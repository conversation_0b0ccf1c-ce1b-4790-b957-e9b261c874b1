﻿// 工具函数
const utils = {
    formatDateTime(date) {
        return new Date(date).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    },
    
    safeValue(value, defaultValue = '') {
        return value || defaultValue;
    }
};

// 资料管理类
class TeachingManager {
    constructor() {
        this.initializeEventListeners();
        this.teachingData = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 0;
        this.totalRecords = 0;
        // 获取用户信息
        this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
    }

    // 初始化所有事件监听器
    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            // 检查用户是否已登录
            if (!this.userInfo) {
                window.location.href = 'login.html';
                return;
            }

            // 加载科室对应的unit选项
            this.loadUnitOptions();
            // 初始化搜索选项
            this.initSearchOptions();

            this.initSearchForm();
            this.initFileUpload();
            this.initPagination();
            this.loadTeachingList();
        });
    }

    // 初始化搜索选项
    async initSearchOptions() {
        try {
            // 加载科室选项
            const sectionSelect = document.getElementById('teachingsearch-section');
            if (sectionSelect) {
                const sectionResponse = await fetch('php/get_options.php?type=section');
                const sectionResult = await sectionResponse.json();
                if (sectionResult.success) {
                    sectionSelect.innerHTML = '<option value="">全部</option>' + 
                        sectionResult.data.map(section => `<option value="${section}">${section}</option>`).join('');
                    
                    // 如果用户有科室，默认选中
                    if (this.userInfo?.section) {
                        sectionSelect.value = this.userInfo.section;
                    }
                }
            }

            // 加载unit选项（基于当前科室）
            await this.loadSearchUnitOptions(this.userInfo?.section || '');

            // 添加科室变化时更新unit的事件监听
            sectionSelect?.addEventListener('change', async () => {
                await this.loadSearchUnitOptions(sectionSelect.value);
            });

        } catch (error) {
            console.error('加载搜索选项失败：', error);
        }
    }

    // 加载搜索表单的unit选项
    async loadSearchUnitOptions(section) {
        try {
            const unitSelect = document.getElementById('teachingsearch-unit');
            if (!unitSelect) return;

            if (section) {
                const response = await fetch(`php/get_options.php?type=unit&section=${section}`);
                const result = await response.json();
                if (result.success) {
                    unitSelect.innerHTML = '<option value="">全部</option>' + 
                        result.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
                }
            } else {
                unitSelect.innerHTML = '<option value="">全部</option>';
            }
        } catch (error) {
            console.error('加载unit选项失败：', error);
        }
    }

    // 加载科室对应的unit选项
    async loadUnitOptions() {
        try {
            const fileUnit = document.getElementById('fileUnit');
            if (!fileUnit || !this.userInfo?.section) return;

            const response = await fetch(`php/get_options.php?type=unit&section=${this.userInfo.section}`);
            const result = await response.json();

            if (result.success) {
                fileUnit.innerHTML = '<option value="">请选择Unit</option>' + 
                    result.data.map(unit => `<option value="${unit}">${unit}</option>`).join('');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载unit选项失败：', error);
            alert('加载unit选项失败：' + error.message);
        }
    }

    // 初始化搜索表单
    initSearchForm() {
        const searchForm = document.getElementById('teachingsearchForm');
        if (!searchForm) return;

        searchForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(searchForm);
            const searchParams = new URLSearchParams();

            for (let [key, value] of formData.entries()) {
                if (value) {
                    searchParams.append(key, value);
                }
            }

            await this.loadTeachingList(searchParams.toString());
        });

        // 重置按钮处理
        const resetBtn = searchForm.querySelector('.teachingreset-btn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                searchForm.reset();
                this.loadTeachingList();
            });
        }
    }

    // 加载资料列表
    async loadTeachingList(queryString = '') {
        try {
            const params = new URLSearchParams(queryString);
            params.set('page', this.currentPage);
            params.set('limit', this.pageSize);

            // 只在没有任何查询参数时，默认使用用户科室
            // if (queryString === '' && this.userInfo?.section) {
            //     params.set('section', this.userInfo.section);
                
            //     // 同时更新搜索表单中的科室选择
            //     const sectionSelect = document.getElementById('teachingsearch-section');
            //     if (sectionSelect) {
            //         sectionSelect.value = this.userInfo.section;
            //         // 触发 change 事件以更新相关的 unit 选项
            //         sectionSelect.dispatchEvent(new Event('change'));
            //     }
            // }

            const response = await fetch(`php/get_teaching.php?${params.toString()}`);
            if (!response.ok) throw new Error('网络响应错误');

            const result = await response.json();
            if (!result.success) throw new Error(result.message);

            this.teachingData = result.data;
            this.updateTeachingTable();
            this.updatePagination(result.pagination);

        } catch (error) {
            console.error('加载资料列表失败：', error);
            alert('加载失败：' + error.message);
        }
    }

    // 更新资料表格
    updateTeachingTable() {
        const tbody = document.querySelector('.teachinglist .data-table tbody');
        if (!tbody) return;

        if (!this.teachingData || this.teachingData.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" style="text-align: center;">暂无数据</td></tr>';
            return;
        }

        tbody.innerHTML = this.teachingData.map(item => `
            <tr>
                <td>${item.title}</td>
                <td>${this.getFilesList(item.files)}</td>
                <td>${this.getCategoryName(item.category)}</td>
                <td>${item.section}</td>
                <td>${item.unit}</td>
                <td>${item.upload_user}</td>
                <td>${utils.formatDateTime(item.upload_time)}</td>
                <td>
                    ${this.getFileActions(item.files)}
                </td>
            </tr>
        `).join('');

        // 绑定文件名点击事件
        this.bindFileNameClick();
        this.bindDownloadButtons();
    }

    // 获取分类名称
    getCategoryName(category) {
        const categoryMap = {
            'operation': '操作手册',
            'courseware': '教育资料',
            'other': '其他'
        };
        return categoryMap[category] || category;
    }

    // 绑定下载按钮事件
    bindDownloadButtons() {
        const buttons = document.querySelectorAll('.btn-download');
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                const filePath = button.dataset.file;
                if (filePath) {
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = `uploads/teaching/${filePath}`;
                    // 从文件路径中提取原始文件名
                    const originalFileName = filePath.substring(filePath.indexOf('_') + 1);
                    link.download = originalFileName;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            });
        });
    }

    // 初始化文件上传
    initFileUpload() {
        const uploadForm = document.getElementById('uploadForm');
        if (!uploadForm) return;

        const fileInput = document.getElementById('fileUpload');
        const fileList = document.getElementById('fileList');

        // 存储已选择的文件
        let selectedFiles = new Map();

        fileInput?.addEventListener('change', (e) => {
            if (!fileList) return;
            
            const newFiles = Array.from(e.target.files);
            
            // 添加新选择的文件到已有文件列表中
            newFiles.forEach(file => {
                const fileId = Date.now() + '-' + file.name; // 创建唯一文件ID
                selectedFiles.set(fileId, file);
                addFileToList(fileId, file);
            });

            // 清空input，允许重复选择相同文件
            fileInput.value = '';
        });

        // 添加文件到列表的函数
        const addFileToList = (fileId, file) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span class="file-name" data-file-id="${fileId}">${file.name}</span>
                <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                <button type="button" class="btn-delete-file" data-file-id="${fileId}">×</button>
            `;

            // 添加文件名点击事件（预览）
            const fileName = fileItem.querySelector('.file-name');
            fileName.addEventListener('click', () => {
                previewFile(file);
            });

            // 添加删除按钮事件
            const deleteBtn = fileItem.querySelector('.btn-delete-file');
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                selectedFiles.delete(fileId);
                fileItem.remove();
            });

            fileList.appendChild(fileItem);
        };

        // 文件预览函数
        const previewFile = (file) => {
            // 如果是图片，创建预览
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const previewWindow = window.open('', '_blank');
                    previewWindow.document.write(`
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>图片预览</title>
                            <style>
                                body {
                                    margin: 0;
                                    padding: 20px;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    min-height: 100vh;
                                    background: #f0f0f0;
                                }
                                img {
                                    max-width: 100%;
                                    max-height: 90vh;
                                    object-fit: contain;
                                    box-shadow: 0 0 20px rgba(0,0,0,0.15);
                                }
                            </style>
                        </head>
                        <body>
                            <img src="${e.target.result}" alt="预览图片">
                        </body>
                        </html>
                    `);
                };
                reader.readAsDataURL(file);
            } else {
                // 对于其他类型的文件，尝试在新窗口中打开
                const fileUrl = URL.createObjectURL(file);
                window.open(fileUrl, '_blank');
            }
        };

        // 表单提交处理
        uploadForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            if (selectedFiles.size === 0) {
                alert('请选择要上传的文件');
                return;
            }

            const formData = new FormData(uploadForm);
            
            // 添加用户信息
            if (this.userInfo?.name) {
                formData.append('upload_user', this.userInfo.name);
            }

            if (this.userInfo?.section) {
                formData.append('fileSection', this.userInfo.section);
            }

            // 添加所有选择的文件
            selectedFiles.forEach((file) => {
                formData.append('files[]', file);
            });

            try {
                const response = await fetch('php/submit_teaching.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                if (result.success) {
                    alert('资料上传成功');
                    uploadForm.reset();
                    fileList.innerHTML = '';
                    selectedFiles.clear();
                    this.loadTeachingList();
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('上传错误:', error);
                alert('上传失败：' + error.message);
            }
        });
    }

    // 初始化分页控件
    initPagination() {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;

        // 页码输入框处理
        const currentPageInput = pagination.querySelector('.current-page');
        currentPageInput?.addEventListener('change', () => {
            const page = parseInt(currentPageInput.value);
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
                this.loadTeachingList();
            } else {
                currentPageInput.value = this.currentPage;
            }
        });

        // 每页条数选择
        const pageSizeSelect = pagination.querySelector('.page-size');
        if (pageSizeSelect) {
            pageSizeSelect.value = this.pageSize;
            pageSizeSelect.addEventListener('change', () => {
                this.pageSize = parseInt(pageSizeSelect.value);
                this.currentPage = 1;
                this.loadTeachingList();
            });
        }

        // 分页按钮处理
        this.bindPaginationButtons(pagination);
    }

    // 绑定分页按钮事件
    bindPaginationButtons(pagination) {
        pagination.querySelector('.btn-first-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage = 1;
                this.loadTeachingList();
            }
        });

        pagination.querySelector('.btn-prev-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadTeachingList();
            }
        });

        pagination.querySelector('.btn-next-page')?.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadTeachingList();
            }
        });

        pagination.querySelector('.btn-last-page')?.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage = this.totalPages;
                this.loadTeachingList();
            }
        });
    }

    // 更新分页信息
    updatePagination(paginationData) {
        const pagination = document.querySelector('.pagination');
        if (!pagination) return;

        this.totalPages = paginationData.total_pages;
        this.totalRecords = paginationData.total;

        pagination.querySelector('.total-count').textContent = this.totalRecords;
        pagination.querySelector('.total-pages').textContent = this.totalPages;
        pagination.querySelector('.current-page').value = this.currentPage;

        // 更新按钮状态
        pagination.querySelector('.btn-first-page').disabled = this.currentPage === 1;
        pagination.querySelector('.btn-prev-page').disabled = this.currentPage === 1;
        pagination.querySelector('.btn-next-page').disabled = this.currentPage === this.totalPages;
        pagination.querySelector('.btn-last-page').disabled = this.currentPage === this.totalPages;
    }

    // 获取文件列表显示
    getFilesList(files) {
        if (!files || files.length === 0) return '无文件';
        return files.map(file => `
            <div class="file-item">
                <span class="file-name" data-file="uploads/teaching/${file.path}" style="cursor: pointer; color: #0066cc;">
                    ${file.name}
                </span>
                <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
            </div>
        `).join('');
    }

    // 获取文件操作按钮
    getFileActions(files) {
        if (!files || files.length === 0) return '';
        return files.map(file => `
            <button class="btn-download" 
                    data-id="${file.id}" 
                    data-file="${file.path}">
                下载
            </button>
        `).join('');
    }

    // 添加新方法：绑定文件名点击事件
    bindFileNameClick() {
        const fileNames = document.querySelectorAll('.file-name');
        fileNames.forEach(fileName => {
            fileName.addEventListener('click', () => {
                const filePath = fileName.dataset.file;
                if (filePath) {
                    // 在新窗口中打开文件
                    window.open(filePath, '_blank');
                }
            });
        });
    }
}

// 创建资料管理器实例
const teachingManager = new TeachingManager(); 