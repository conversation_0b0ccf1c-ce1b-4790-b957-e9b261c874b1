[01-Jan-2025 14:52:23 Europe/Berlin] POST array: Array
(
)

[01-Jan-2025 14:52:23 Europe/Berlin] POST array: Array
(
    [project] => OLB
    [line] => 1S09
    [unit] => SS
    [category] => 故障
    [direction] => 硬件
    [datetime] => 2025-01-01T21:52
    [status] => CLOSE
    [keyword] => 测试
    [responsible] => 张新宇
    [recorder] => 张新宇
    [issue] => sensor损坏
    [phenomenon] => sensor损坏
    [analysis] => sensor损坏
    [measures] => sensor损坏
    [relatedParts] => sensor损坏
    [model] => sensor损坏
)

[01-Jan-2025 14:52:23 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 14:52:23 Europe/Berlin] FILES array: Array
(
    [files] => Array
        (
            [name] => Array
                (
                    [0] => 
                )

            [full_path] => Array
                (
                    [0] => 
                )

            [type] => Array
                (
                    [0] => 
                )

            [tmp_name] => Array
                (
                    [0] => 
                )

            [error] => Array
                (
                    [0] => 4
                )

            [size] => Array
                (
                    [0] => 0
                )

        )

)

[01-Jan-2025 14:52:23 Europe/Berlin] Error in submit_fault.php: 以下字段为空: project, line, unit, category, direction, datetime, status, responsible, recorder
[01-Jan-2025 14:54:23 Europe/Berlin] POST array: Array
(
)

[01-Jan-2025 14:54:23 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 14:54:23 Europe/Berlin] Error in submit_fault.php: 以下字段为空: project, line, unit, category, direction, datetime, status, responsible, recorder
[01-Jan-2025 14:54:23 Europe/Berlin] POST array: Array
(
    [project] => CP
    [line] => 1S04
    [unit] => CL
    [category] => 故障
    [direction] => 硬件
    [datetime] => 2025-01-01T21:54
    [status] => OPEN
    [keyword] => 123123
    [responsible] => 张新宇
    [recorder] => 张新宇
    [issue] => sensor损坏
    [phenomenon] => sensor损坏
    [analysis] => sensor损坏
    [measures] => sensor损坏
    [relatedParts] => sensor损坏
    [model] => sensor损坏
)

[01-Jan-2025 14:54:23 Europe/Berlin] FILES array: Array
(
    [files] => Array
        (
            [name] => Array
                (
                    [0] => 
                )

            [full_path] => Array
                (
                    [0] => 
                )

            [type] => Array
                (
                    [0] => 
                )

            [tmp_name] => Array
                (
                    [0] => 
                )

            [error] => Array
                (
                    [0] => 4
                )

            [size] => Array
                (
                    [0] => 0
                )

        )

)

[01-Jan-2025 14:57:59 Europe/Berlin] POST array: Array
(
)

[01-Jan-2025 14:57:59 Europe/Berlin] POST array: Array
(
    [project] => OLB
    [line] => 1S03
    [unit] => LD
    [category] => 品质
    [direction] => 软件
    [datetime] => 2025-01-01T21:56
    [status] => OPEN
    [keyword] => 123123
    [responsible] => 张新宇
    [recorder] => 张新宇
    [issue] => sensor损坏
    [phenomenon] => sensor损坏
    [analysis] => sensor损坏
    [measures] => sensor损坏
    [relatedParts] => sensor损坏
    [model] => sensor损坏
)

[01-Jan-2025 14:57:59 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 14:57:59 Europe/Berlin] FILES array: Array
(
    [files] => Array
        (
            [name] => Array
                (
                    [0] => 
                )

            [full_path] => Array
                (
                    [0] => 
                )

            [type] => Array
                (
                    [0] => 
                )

            [tmp_name] => Array
                (
                    [0] => 
                )

            [error] => Array
                (
                    [0] => 4
                )

            [size] => Array
                (
                    [0] => 0
                )

        )

)

[01-Jan-2025 14:57:59 Europe/Berlin] Field project: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field line: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field project: 'OLB'
[01-Jan-2025 14:57:59 Europe/Berlin] Field unit: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field line: '1S03'
[01-Jan-2025 14:57:59 Europe/Berlin] Field category: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field unit: 'LD'
[01-Jan-2025 14:57:59 Europe/Berlin] Field direction: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field category: '品质'
[01-Jan-2025 14:57:59 Europe/Berlin] Field datetime: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field direction: '软件'
[01-Jan-2025 14:57:59 Europe/Berlin] Field status: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field datetime: '2025-01-01T21:56'
[01-Jan-2025 14:57:59 Europe/Berlin] Field responsible: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field status: 'OPEN'
[01-Jan-2025 14:57:59 Europe/Berlin] Field recorder: not set
[01-Jan-2025 14:57:59 Europe/Berlin] Field responsible: '张新宇'
[01-Jan-2025 14:57:59 Europe/Berlin] Error in submit_fault.php: 以下字段为空: project, line, unit, category, direction, datetime, status, responsible, recorder
[01-Jan-2025 14:57:59 Europe/Berlin] Field recorder: '张新宇'
[01-Jan-2025 14:57:59 Europe/Berlin] Final parameters: Array
(
    [0] => OLB
    [1] => 1S03
    [2] => LD
    [3] => 123123
    [4] => 品质
    [5] => 软件
    [6] => 2025-01-01T21:56
    [7] => OPEN
    [8] => 张新宇
    [9] => 张新宇
    [10] => sensor损坏
    [11] => sensor损坏
    [12] => sensor损坏
    [13] => sensor损坏
    [14] => sensor损坏
    [15] => sensor损坏
)

[01-Jan-2025 15:01:26 Europe/Berlin] POST array: Array
(
)

[01-Jan-2025 15:01:26 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:01:26 Europe/Berlin] POST array: Array
(
    [project] => OLB
    [line] => 1S08
    [unit] => SS
    [category] => 故障
    [direction] => 硬件
    [datetime] => 2025-01-01T22:01
    [status] => CLOSE
    [responsible] => 张新宇
    [recorder] => 张新宇
    [keyword] => 123123
    [issue] => sensor损坏
    [phenomenon] => sensor损坏
    [analysis] => sensor损坏
    [measures] => sensor损坏
    [relatedParts] => sensor损坏
    [model] => sensor损坏
)

[01-Jan-2025 15:01:26 Europe/Berlin] Field project: not set
[01-Jan-2025 15:01:26 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:01:26 Europe/Berlin] Field line: not set
[01-Jan-2025 15:01:26 Europe/Berlin] Field unit: not set
[01-Jan-2025 15:01:26 Europe/Berlin] Field project: 'OLB'
[01-Jan-2025 15:01:26 Europe/Berlin] Field category: not set
[01-Jan-2025 15:01:26 Europe/Berlin] Field line: '1S08'
[01-Jan-2025 15:01:26 Europe/Berlin] Field direction: not set
[01-Jan-2025 15:01:26 Europe/Berlin] Field unit: 'SS'
[01-Jan-2025 15:01:26 Europe/Berlin] Field datetime: not set
[01-Jan-2025 15:01:26 Europe/Berlin] Field category: '故障'
[01-Jan-2025 15:01:26 Europe/Berlin] Field status: not set
[01-Jan-2025 15:01:26 Europe/Berlin] Field direction: '硬件'
[01-Jan-2025 15:01:26 Europe/Berlin] Field responsible: not set
[01-Jan-2025 15:01:26 Europe/Berlin] Field datetime: '2025-01-01T22:01'
[01-Jan-2025 15:01:26 Europe/Berlin] Field recorder: not set
[01-Jan-2025 15:01:26 Europe/Berlin] Field status: 'CLOSE'
[01-Jan-2025 15:01:26 Europe/Berlin] Field responsible: '张新宇'
[01-Jan-2025 15:01:26 Europe/Berlin] Error in submit_fault.php: 以下字段为空: project, line, unit, category, direction, datetime, status, responsible, recorder
[01-Jan-2025 15:01:26 Europe/Berlin] Field recorder: '张新宇'
[01-Jan-2025 15:01:26 Europe/Berlin] Final parameters: Array
(
    [0] => OLB
    [1] => 1S08
    [2] => SS
    [3] => 123123
    [4] => 故障
    [5] => 硬件
    [6] => 2025-01-01T22:01
    [7] => CLOSE
    [8] => 张新宇
    [9] => 张新宇
    [10] => sensor损坏
    [11] => sensor损坏
    [12] => sensor损坏
    [13] => sensor损坏
    [14] => sensor损坏
    [15] => sensor损坏
)

[01-Jan-2025 15:03:21 Europe/Berlin] POST array: Array
(
    [project] => OLB
    [line] => 1S25
    [unit] => CL
    [category] => 故障
    [direction] => 硬件
    [datetime] => 2025-01-01T22:03
    [status] => CLOSE
    [responsible] => 张新宇
    [recorder] => 张新宇
    [keyword] => 123123
    [issue] => sensor损坏
    [phenomenon] => sensor损坏
    [analysis] => sensor损坏
    [measures] => sensor损坏
    [relatedParts] => sensor损坏
    [model] => sensor损坏
)

[01-Jan-2025 15:03:21 Europe/Berlin] POST array: Array
(
)

[01-Jan-2025 15:03:21 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:03:21 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:03:21 Europe/Berlin] Field project: 'OLB'
[01-Jan-2025 15:03:21 Europe/Berlin] Field project: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Field line: '1S25'
[01-Jan-2025 15:03:21 Europe/Berlin] Field line: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Field unit: 'CL'
[01-Jan-2025 15:03:21 Europe/Berlin] Field unit: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Field category: '故障'
[01-Jan-2025 15:03:21 Europe/Berlin] Field category: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Field direction: '硬件'
[01-Jan-2025 15:03:21 Europe/Berlin] Field direction: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Field datetime: '2025-01-01T22:03'
[01-Jan-2025 15:03:21 Europe/Berlin] Field datetime: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Field status: 'CLOSE'
[01-Jan-2025 15:03:21 Europe/Berlin] Field status: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Field responsible: '张新宇'
[01-Jan-2025 15:03:21 Europe/Berlin] Field responsible: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Field recorder: '张新宇'
[01-Jan-2025 15:03:21 Europe/Berlin] Field recorder: not set
[01-Jan-2025 15:03:21 Europe/Berlin] Final parameters: Array
(
    [0] => OLB
    [1] => 1S25
    [2] => CL
    [3] => 123123
    [4] => 故障
    [5] => 硬件
    [6] => 2025-01-01T22:03
    [7] => CLOSE
    [8] => 张新宇
    [9] => 张新宇
    [10] => sensor损坏
    [11] => sensor损坏
    [12] => sensor损坏
    [13] => sensor损坏
    [14] => sensor损坏
    [15] => sensor损坏
)

[01-Jan-2025 15:03:21 Europe/Berlin] Error in submit_fault.php: 以下字段为空: project, line, unit, category, direction, datetime, status, responsible, recorder
[01-Jan-2025 15:05:42 Europe/Berlin] POST array: Array
(
)

[01-Jan-2025 15:05:42 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:05:42 Europe/Berlin] Field project: not set
[01-Jan-2025 15:05:42 Europe/Berlin] POST array: Array
(
    [project] => OLB
    [line] => 1S24
    [unit] => LB
    [category] => 故障
    [direction] => 硬件
    [datetime] => 2025-01-01T22:05
    [status] => CLOSE
    [responsible] => 张新宇
    [recorder] => 张新宇
    [keyword] => 123123
    [issue] => sensor损坏
    [phenomenon] => sensor损坏
    [analysis] => sensor损坏
    [measures] => sensor损坏
    [relatedParts] => sensor损坏
    [model] => sensor损坏
)

[01-Jan-2025 15:05:42 Europe/Berlin] Field line: not set
[01-Jan-2025 15:05:42 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:05:42 Europe/Berlin] Field unit: not set
[01-Jan-2025 15:05:42 Europe/Berlin] Field category: not set
[01-Jan-2025 15:05:42 Europe/Berlin] Field project: 'OLB'
[01-Jan-2025 15:05:42 Europe/Berlin] Field direction: not set
[01-Jan-2025 15:05:42 Europe/Berlin] Field line: '1S24'
[01-Jan-2025 15:05:42 Europe/Berlin] Field datetime: not set
[01-Jan-2025 15:05:42 Europe/Berlin] Field unit: 'LB'
[01-Jan-2025 15:05:42 Europe/Berlin] Field status: not set
[01-Jan-2025 15:05:42 Europe/Berlin] Field category: '故障'
[01-Jan-2025 15:05:42 Europe/Berlin] Field responsible: not set
[01-Jan-2025 15:05:42 Europe/Berlin] Field direction: '硬件'
[01-Jan-2025 15:05:42 Europe/Berlin] Field recorder: not set
[01-Jan-2025 15:05:42 Europe/Berlin] Field datetime: '2025-01-01T22:05'
[01-Jan-2025 15:05:42 Europe/Berlin] Field status: 'CLOSE'
[01-Jan-2025 15:05:42 Europe/Berlin] Error in submit_fault.php: 以下字段为空: project, line, unit, category, direction, datetime, status, responsible, recorder
[01-Jan-2025 15:05:42 Europe/Berlin] Field responsible: '张新宇'
[01-Jan-2025 15:05:42 Europe/Berlin] Field recorder: '张新宇'
[01-Jan-2025 15:05:42 Europe/Berlin] Final parameters: Array
(
    [0] => OLB
    [1] => 1S24
    [2] => LB
    [3] => 123123
    [4] => 故障
    [5] => 硬件
    [6] => 2025-01-01T22:05
    [7] => CLOSE
    [8] => 张新宇
    [9] => 张新宇
    [10] => sensor损坏
    [11] => sensor损坏
    [12] => sensor损坏
    [13] => sensor损坏
    [14] => sensor损坏
    [15] => sensor损坏
)

[01-Jan-2025 15:08:52 Europe/Berlin] POST array: Array
(
    [project] => CP
    [line] => 1S06
    [unit] => LB
    [category] => 品质
    [direction] => 硬件
    [datetime] => 2025-01-01T22:08
    [status] => CLOSE
    [responsible] => 张新宇
    [recorder] => 张新宇
    [keyword] => 123123
    [issue] => sensor损坏
    [phenomenon] => sensor损坏
    [analysis] => sensor损坏
    [measures] => sensor损坏
    [relatedParts] => sensor损坏
    [model] => sensor损坏
)

[01-Jan-2025 15:08:52 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:08:52 Europe/Berlin] POST array: Array
(
)

[01-Jan-2025 15:08:52 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:10:31 Europe/Berlin] POST array: Array
(
)

[01-Jan-2025 15:10:31 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:10:31 Europe/Berlin] POST array: Array
(
    [project] => OLB
    [line] => 1S05
    [unit] => LC
    [category] => 品质
    [direction] => 软件
    [datetime] => 2025-01-01T22:10
    [status] => CLOSE
    [responsible] => 张新宇
    [recorder] => 张新宇
    [keyword] => 123123
    [issue] => 重复上传测试
    [phenomenon] => 重复上传测试
    [analysis] => 重复上传测试
    [measures] => 重复上传测试
    [relatedParts] => 重复上传测试
    [model] => 重复上传测试
)

[01-Jan-2025 15:10:31 Europe/Berlin] FILES array: Array
(
)

[01-Jan-2025 15:15:47 Europe/Berlin] POST array: Array
(
    [project] => OLB
    [line] => 1S04
    [unit] => AC
    [category] => 品质
    [direction] => 硬件
    [datetime] => 2025-01-01T22:15
    [status] => CLOSE
    [keyword] => 123123
    [responsible] => 张新宇
    [recorder] => 张新宇
    [issue] => 重复上传测试
    [phenomenon] => 重复上传测试
    [analysis] => 重复上传测试
    [measures] => 重复上传测试
    [relatedParts] => 重复上传测试
    [model] => 重复上传测试
)

[01-Jan-2025 15:15:47 Europe/Berlin] FILES array: Array
(
    [files] => Array
        (
            [name] => Array
                (
                    [0] => 
                )

            [full_path] => Array
                (
                    [0] => 
                )

            [type] => Array
                (
                    [0] => 
                )

            [tmp_name] => Array
                (
                    [0] => 
                )

            [error] => Array
                (
                    [0] => 4
                )

            [size] => Array
                (
                    [0] => 0
                )

        )

)

[04-Jan-2025 13:47:29 Europe/Berlin] POST array: Array
(
    [project] => OLB
    [line] => 1S06
    [unit] => LD
    [category] => 故障
    [direction] => 硬件
    [datetime] => 2025-01-04T20:47
    [status] => CLOSE
    [keyword] => 文件下载测试
    [responsible] => 张新宇
    [recorder] => 张新宇
    [issue] => 文件下载测试
    [phenomenon] => 文件下载测试
    [analysis] => 文件下载测试
    [measures] => 文件下载测试
    [relatedParts] => 文件下载测试
    [model] => 文件下载测试
)

[04-Jan-2025 13:47:29 Europe/Berlin] FILES array: Array
(
    [files] => Array
        (
            [name] => Array
                (
                    [0] => 
                    [1] => 微信图片_20240728205525.jpg
                    [2] => InvoiceIntroduce.pdf
                )

            [full_path] => Array
                (
                    [0] => 
                    [1] => 微信图片_20240728205525.jpg
                    [2] => InvoiceIntroduce.pdf
                )

            [type] => Array
                (
                    [0] => 
                    [1] => image/jpeg
                    [2] => application/pdf
                )

            [tmp_name] => Array
                (
                    [0] => 
                    [1] => D:\xampp\tmp\php1F63.tmp
                    [2] => D:\xampp\tmp\php1F93.tmp
                )

            [error] => Array
                (
                    [0] => 4
                    [1] => 0
                    [2] => 0
                )

            [size] => Array
                (
                    [0] => 0
                    [1] => 9980825
                    [2] => 1500984
                )

        )

)

