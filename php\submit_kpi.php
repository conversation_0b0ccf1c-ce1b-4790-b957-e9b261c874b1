<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['date']) || !isset($data['leader'])) {
        throw new Exception("缺少必要数据");
    }
    
    $date = $data['date'];
    $leader = $data['leader'];
    $FP = isset($data['FP']) ? $data['FP'] : 0;
    $QSM = isset($data['QSM']) ? $data['QSM'] : 0;
    $quality = isset($data['quality']) ? $data['quality'] : 0;
    $S5D3 = isset($data['5S3D']) ? $data['5S3D'] : 0;
    $env = isset($data['env']) ? $data['env'] : 0;
    $info = isset($data['info']) ? $data['info'] : 0;
    
    // 检查是更新还是新增
    $checkSql = "SELECT COUNT(*) as count FROM kpi WHERE date = ? AND leader = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param("ss", $date, $leader);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    $row = $checkResult->fetch_assoc();
    $exists = $row['count'] > 0;
    $checkStmt->close();
    
    if ($exists) {
        // 更新现有记录
        $sql = "UPDATE kpi SET FP = ?, QSM = ?, quality = ?, `5S3D` = ?, env = ?, info = ? 
                WHERE date = ? AND leader = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ddddddss", $FP, $QSM, $quality, $S5D3, $env, $info, $date, $leader);
    } else {
        // 插入新记录
        $sql = "INSERT INTO kpi (date, leader, FP, QSM, quality, `5S3D`, env, info) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssdddddd", $date, $leader, $FP, $QSM, $quality, $S5D3, $env, $info);
    }
    
    if (!$stmt->execute()) {
        throw new Exception("保存数据失败: " . $stmt->error);
    }
    
    echo json_encode([
        'success' => true,
        'message' => $exists ? '数据更新成功' : '数据添加成功'
    ]);
    
    $stmt->close();

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 