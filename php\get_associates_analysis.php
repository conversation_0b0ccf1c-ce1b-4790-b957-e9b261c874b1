<?php
header('Content-Type: application/json');
require_once 'db_config.php';

try {
    // 获取查询参数
    $section = $_GET['section'] ?? '';
    $classes = $_GET['classes'] ?? '';
    $line = $_GET['line'] ?? '';
    $project = $_GET['project'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $problemtype = $_GET['problemtype'] ?? '';
    $needfollow = $_GET['needfollow'] ?? '';
    $status = $_GET['status'] ?? '';
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';

    $tableFor = $_GET['tableFor'] ?? '';

    // 构建WHERE子句
    $where = [];
    $params = [];
    $types = '';

    if (!empty($section)) {
        $where[] = "section = ?";
        $params[] = $section;
        $types .= 's';
    }

    if (!empty($classes)) {
        $where[] = "classes = ?";
        $params[] = $classes;
        $types .= 's';
    }

    if (!empty($line)) {
        $where[] = "line = ?";
        $params[] = $line;
        $types .= 's';
    }

    if (!empty($project)) {
        $where[] = "project = ?";
        $params[] = $project;
        $types .= 's';
    }

    if (!empty($unit)) {
        $where[] = "unit = ?";
        $params[] = $unit;
        $types .= 's';
    }

    if (!empty($problemtype)) {
        $where[] = "problemtype = ?";
        $params[] = $problemtype;
        $types .= 's';
    }

    if (!empty($needfollow)) {
        $where[] = "needfollow = ?";
        $params[] = $needfollow;
        $types .= 's';
    }

    if (!empty($status)) {
        $where[] = "status = ?";
        $params[] = $status;
        $types .= 's';
    }

    if (!empty($startDate)) {
        $where[] = "DATE(created_at) >= ?";
        $params[] = $startDate;
        $types .= 's';
    }

    if (!empty($endDate)) {
        $where[] = "DATE(created_at) <= ?";
        $params[] = $endDate;
        $types .= 's';
    }

    // 获取数据
    // $sql = "select id,section,classes,line,project,unit,problemtype,phenomenon,analysis,measure,problempart,
    // problempart,problemcode,needfollow,status,step,recorder,created_at,updated_at 
    // from (select * from associatelist t1 
    // JOIN ( SELECT MAX(step) AS step1, section as section1, classes as classes1, line as line1, project as project1, unit as unit1, phenomenon as phenomenon1, created_at as  created_at1
    // FROM associatelist 
    // GROUP BY section, classes, line, project, unit, problemtype, phenomenon, problempart, problemcode, created_at ) t2 
    // ON t1.step = t2.step1 
    // AND t1.section = t2.section1
    // AND t1.classes = t2.classes1
    // AND t1.line = t2.line1
    // AND t1.project = t2.project1  
    // AND t1.unit = t2.unit1 
    // AND t1.phenomenon = t2.phenomenon1 
    // AND t1.created_at = t2.created_at1) t3";
    if($tableFor === 'forUnit'){
        $sql = "select unit as name, count(*) as value
        from (select * from associatelist t1 
        JOIN ( SELECT MAX(step) AS step1, section as section1, classes as classes1, line as line1, project as project1, unit as unit1, phenomenon as phenomenon1, created_at as  created_at1
        FROM associatelist 
        GROUP BY section, classes, line, project, unit, problemtype, phenomenon, problempart, problemcode, created_at ) t2 
        ON t1.step = t2.step1 
        AND t1.section = t2.section1
        AND t1.classes = t2.classes1
        AND t1.line = t2.line1
        AND t1.project = t2.project1  
        AND t1.unit = t2.unit1 
        AND t1.phenomenon = t2.phenomenon1 
        AND t1.created_at = t2.created_at1) t3";
        if (!empty($where)) {
            $sql .= " WHERE " . implode(" AND ", $where);
        }
        $sql .= " GROUP BY unit order by unit";
    }
    else{
        $sql = "select line as name, count(*) as value
        from (select * from associatelist t1 
        JOIN ( SELECT MAX(step) AS step1, section as section1, classes as classes1, line as line1, project as project1, unit as unit1, phenomenon as phenomenon1, created_at as  created_at1
        FROM associatelist 
        GROUP BY section, classes, line, project, unit, problemtype, phenomenon, problempart, problemcode, created_at ) t2 
        ON t1.step = t2.step1 
        AND t1.section = t2.section1
        AND t1.classes = t2.classes1
        AND t1.line = t2.line1
        AND t1.project = t2.project1  
        AND t1.unit = t2.unit1 
        AND t1.phenomenon = t2.phenomenon1 
        AND t1.created_at = t2.created_at1) t3";
        if (!empty($where)) {
            $sql .= " WHERE " . implode(" AND ", $where);
        }
        $sql .= " GROUP BY line order by line";
    }
    

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 