<?php
header('Content-Type: application/json');
require_once 'db_config.php';

try {
    // 获取查询参数
    $section = $_GET['section'] ?? '';
    $line = $_GET['line'] ?? '';
    $project = $_GET['project'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $phenomenon = $_GET['phenomenon'] ?? '';
    $problemtype = $_GET['problemtype'] ?? '';
    // 需要添加创建时间以 区分 上述7同数据  - 20250602
    $created_at = $_GET['created_at'] ?? '';


    // 构建WHERE子句
    $where = [];
    $params = [];
    $types = '';

    if (!empty($section)) {
        $where[] = "section = ?";
        $params[] = $section;
        $types .= 's';
    }

    if (!empty($line)) {
        $where[] = "line = ?";
        $params[] = $line;
        $types .= 's';
    }

    if (!empty($project)) {
        $where[] = "project = ?";
        $params[] = $project;
        $types .= 's';
    }

    if (!empty($unit)) {
        $where[] = "unit = ?";
        $params[] = $unit;
        $types .= 's';
    }

    if (!empty($phenomenon)) {
        $where[] = "phenomenon = ?";
        $params[] = $phenomenon;
        $types .= 's';
    }

    if (!empty($problemtype)) {
        $where[] = "problemtype = ?";
        $params[] = $problemtype;
        $types .= 's';
    }
    // 需要添加创建时间以 区分 上述7同数据  - 20250602
    if (!empty($created_at)) {
        $where[] = "created_at = ?";
        $params[] = $created_at;
        $types .= 's';
    }

    
    $sql = "select id,section,project,classes,line,unit,problemtype,phenomenon,analysis,measure,problempart,
    problempart,problemcode,needfollow,status,step,recorder,created_at,updated_at 
    from associatelist";
    if (!empty($where)) {
        $sql .= " WHERE " . implode(" AND ", $where);
    }
    $sql .= " ORDER BY step DESC";
    //$sql .= " group by section,line,unit ORDER BY created_at DESC LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 