<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 检查必填字段
    $required_fields = ['related', 'content', 'days', 'publisher'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("缺少必填字段: $field");
        }
    }

    // 获取表单数据
    $related = $_POST['related'];
    $content = $_POST['content'];
    $days = intval($_POST['days']);
    $publisher = $_POST['publisher'];
    $section = $_POST['section'];
    $upload_time = date('Y-m-d H:i:s');
    $project =$_POST['project'];
    
    // 计算结束时间
    $end_time = null;
    if ($days > 0) {
        $end_time = date('Y-m-d H:i:s', strtotime("+$days days"));
    }

    // 准备SQL语句
    $sql = "INSERT INTO notice (related, content, display_days, publisher, section, project, upload_time, end_time) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("准备语句失败: " . $conn->error);
    }

    // 绑定参数
    $stmt->bind_param('ssisssss', 
        $related,
        $content,
        $days,
        $publisher,
        $section,
        $project,
        $upload_time,
        $end_time
    );

    // 执行插入
    if (!$stmt->execute()) {
        throw new Exception("执行失败: " . $stmt->error);
    }


    $notice_id = $stmt->insert_id;

    // 处理文件上传
    if (!empty($_FILES['files'])) {
        $uploadDir = '../uploads/notice/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        foreach ($_FILES['files']['tmp_name'] as $key => $tmp_name) {
            if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
                $fileName = $_FILES['files']['name'][$key];
                $fileType = $_FILES['files']['type'][$key];
                $fileSize = $_FILES['files']['size'][$key];
                
                // 生成唯一文件名
                $uniqueName = uniqid() . '_' . $fileName;
                $filePath = $uploadDir . $uniqueName;
                
                if (move_uploaded_file($tmp_name, $filePath)) {
                    // 保存文件信息到数据库
                    $sql = "INSERT INTO notice_files (notice_id, file_name, file_path, file_type, file_size) 
                           VALUES (?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('isssi', $notice_id, $fileName, $uniqueName, $fileType, $fileSize);
                    $stmt->execute();
                }
            }
        }
    }

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '通知发布成功'
    ]);

} catch (Exception $e) {
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 