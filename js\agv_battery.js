const table = document.querySelector('.data-table');
const tbody = table.querySelector('tbody');
const modal = document.getElementById('editModal');
const modifyButton = modal.querySelector('.btn-modify');
const returnButton = modal.querySelector('.btn-return');

let isEditing = false;
let currentItemData = null;


function renderTable(data) {
    // 清空现有内容
    table.querySelector('thead').innerHTML = '';
    tbody.innerHTML = '';

    // 自动生成表头
    if(data.length > 0) {
        // 创建表头行
        const headerRow = document.createElement('tr');
        
        // 遍历第一个数据项的键生成表头
        Object.keys(data[0]).forEach(key => {
            if (key !== 'id') {
                const th = document.createElement('th');
                th.textContent = key;
                headerRow.appendChild(th);
            }
        });

        // 添加管理列表头
        const manageTh = document.createElement('th');
        manageTh.textContent = '管理';
        headerRow.appendChild(manageTh);

        table.querySelector('thead').appendChild(headerRow);
    }

    // 动态创建数据行
    data.forEach(item => {
        const row = document.createElement('tr');
        
        // 遍历所有字段生成单元格
        Object.entries(item).forEach(([key, value]) => {
            if (key !== 'id') {
                const td = document.createElement('td');
                td.textContent = value;
                row.appendChild(td);
            }
        });

        // 添加管理按钮
        const manageTd = document.createElement('td');
        const editButton = document.createElement('button');
        editButton.textContent = '编辑';
        editButton.className = 'edit-btn';
        
        // 添加点击事件
        editButton.addEventListener('click', (e) => {
            // e.stopPropagation();
            showModal(item);
        });

        manageTd.appendChild(editButton);
        row.appendChild(manageTd);

        tbody.appendChild(row);
    });
}

function populateModalData(data) {
    const detailTables = modal.querySelectorAll('.detail-table');
    detailTables.forEach(detailTable => {
        const allRows = detailTable.querySelectorAll('tr');

        for (let i = 0; i < allRows.length; i += 2) {
            const headerRow = allRows[i];
            const dataRow = allRows[i + 1];

            if (headerRow && dataRow) {
                dataRow.innerHTML = '';
                const headers = headerRow.querySelectorAll('th');
                headers.forEach(header => {
                    const key = header.textContent.trim();
                    const value = data[key] ?? '';
                    const td = document.createElement('td');
                    td.textContent = value;
                    if (key === 'id') {
                        td.style.display = 'none';
                    }
                    dataRow.appendChild(td);
                });
            }
        }
    });
}

function setEditingState(editing) {
    isEditing = editing;
    const detailTables = modal.querySelectorAll('.detail-table');

    modifyButton.textContent = editing ? '保存' : '修改';
    returnButton.textContent = editing ? '取消' : '返回';

    detailTables.forEach(detailTable => {
        if (editing) {
            const dataCells = detailTable.querySelectorAll('td');
            dataCells.forEach(td => {
                const headerCell = td.closest('tr').previousElementSibling.children[td.cellIndex];
                const key = headerCell.textContent.trim();

                if (key === 'id') {
                    td.style.display = 'none';
                    return;
                }

                td.classList.add('editable-cell');
                const currentValue = td.textContent;
                td.innerHTML = `<input type="text" value="${currentValue}" class="edit-input">`;
            });
        } else {
            const editableCells = detailTable.querySelectorAll('.editable-cell');
            editableCells.forEach(td => {
                td.classList.remove('editable-cell');
                const input = td.querySelector('input.edit-input');
                if (input) {
                    td.textContent = input.value;
                }
            });
        }
    });
}

function showModal(data) {
    currentItemData = { ...data };
    populateModalData(data);
    setEditingState(false);
    modal.style.display = 'block';
}

modifyButton.addEventListener('click', () => {
    if (!isEditing) {
        setEditingState(true);
    } else {
        const updatedData = {};
        const detailTables = modal.querySelectorAll('.detail-table');

        detailTables.forEach(detailTable => {
            detailTable.querySelectorAll('td').forEach(td => {
                const headerCell = td.closest('tr').previousElementSibling.children[td.cellIndex];
                const key = headerCell.textContent.trim();
                const input = td.querySelector('input.edit-input');
                updatedData[key] = input ? input.value : td.textContent;
            });
        });
        
        updatedData.id = currentItemData.id;

        fetch('php/update_battery.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updatedData)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('更新成功!');
                currentItemData = updatedData;
                setEditingState(false);
                populateModalData(currentItemData);
                fetchAndRenderTable();
            } else {
                alert('更新失败: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Update Error:', error);
            alert('更新操作失败，请查看控制台获取详情。');
        });
    }
});

returnButton.addEventListener('click', () => {
    if (isEditing) {
        populateModalData(currentItemData);
        setEditingState(false);
    } else {
        modal.style.display = 'none';
    }
});

// 关闭模态框的事件绑定
document.querySelector('.close-button').addEventListener('click', function() {
    modal.style.display = 'none';
});

// 点击外部区域关闭
window.addEventListener('click', function(event) {
    if (event.target === modal) {
        modal.style.display = 'none';
    }
});

function fetchAndRenderTable() {
    fetch('php/get_battery.php')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                renderTable(result.data);
                table.classList.add('auto-generated-table');
            } else {
                throw new Error(result.message || '未知错误');
            }
        })
        .catch(error => {
            console.error('错误详情:', error);
            alert('操作失败: ' + error.message);
            
            table.querySelector('thead').innerHTML = '';
            table.querySelector('tbody').innerHTML = `
                <tr><td colspan="100%" style="color:red;text-align:center">${error.message}</td></tr>
            `;
        });
}

fetchAndRenderTable();

// 电池更换履历表单管理类
class BatteryChangeManager {
    constructor() {
        this.eventsInitialized = false;
        this.init();
    }

    init() {
        const changeinTab = document.getElementById('changeinTab');
        if (changeinTab) {
            this.initializeBatteryChangeTable();
        }
    }

    // 初始化电池更换履历表格
    async initializeBatteryChangeTable() {
        const tbody = document.getElementById('batteryChangeTableBody');
        if (!tbody) return;
        tbody.innerHTML = '';

        // 默认添加1行
        await this.addNewBatteryChangeRow(tbody);

        if (!this.eventsInitialized) {
            this.bindEvents();
            this.eventsInitialized = true;
        }
    }

    bindEvents() {
        // 绑定添加按钮事件
        const addRowsBtn = document.querySelector('#changeinTab .btn-add-rows');
        if (addRowsBtn) {
            addRowsBtn.addEventListener('click', async () => {
                const tbody = document.getElementById('batteryChangeTableBody');
                await this.addNewBatteryChangeRow(tbody);
            });
        }

        // 绑定表单提交事件
        const form = document.querySelector('.battery-change-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitBatteryChangeForm(form);
            });
        }
    }

    // 动态加载AGV选项
    async loadAgvOptions(row, rowIndex) {
        try {
            const response = await fetch('php/get_battery.php');
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            
            const result = await response.json();
            if (result.success) {
                const uniqueAgvNumbers = [...new Set(result.data.map(item => item.唯一编号))]
                    .filter(Boolean) // 过滤掉空值
                    .sort((a, b) => a.localeCompare(b, undefined, {numeric: true}));
                
                const select = row.querySelector(`select[name="agv_info_${rowIndex}"]`);
                if (select) {
                    select.innerHTML += uniqueAgvNumbers.map(agvInfo => 
                        `<option value="${agvInfo}">${agvInfo}</option>`
                    ).join('');
                }
            } else {
                throw new Error(result.message || '获取AGV信息失败');
            }
        } catch (error) {
            console.error('加载AGV选项失败:', error);
            alert('加载车辆选项失败: ' + error.message);
            throw error;
        }
    }

    // 添加新的电池更换履历行
    async addNewBatteryChangeRow(tbody) {
        const row = document.createElement('tr');
        const rowIndex = tbody.children.length;

        row.innerHTML = `
            <td>
                <select name="agv_info_${rowIndex}" required>
                    <option value="">请选择车辆</option>
                </select>
            </td>
            <td>
                <input type="text" name="old_battery_${rowIndex}" placeholder="更换前电池信息" required>
            </td>
            <td>
                <input type="text" name="new_battery_${rowIndex}" placeholder="更换后电池信息" required>
            </td>
            <td>
                <input type="date" name="change_date_${rowIndex}" required>
            </td>
            <td>
                <input type="date" name="move_out_date_${rowIndex}" required>
            </td>
            <td>
                <input type="date" name="return_date_${rowIndex}" required>
            </td>
            <td>
                <div class="file-upload-container">
                    <div class="file-list" id="batteryFileList-${rowIndex}"></div>
                    <button type="button" class="btn-add-file" onclick="batteryChangeManager.addFile(${rowIndex})">添加文件</button>
                    <input type="file" id="batteryFileInput-${rowIndex}" name="battery_files[]" style="display: none;">
                </div>
            </td>
            <td>
                <button type="button" class="btn-delete" onclick="batteryChangeManager.deleteBatteryChangeRow(this)">删除</button>
            </td>
        `;

        tbody.appendChild(row);

        // 绑定文件输入事件
        const fileInput = document.getElementById(`batteryFileInput-${rowIndex}`);
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileSelect(e, rowIndex);
            });
        }

        // 动态加载AGV选项
        await this.loadAgvOptions(row, rowIndex);
    }

    // 添加文件
    addFile(rowIndex) {
        const fileInput = document.getElementById(`batteryFileInput-${rowIndex}`);
        if (fileInput) {
            fileInput.click();
        }
    }

    // 处理文件选择
    handleFileSelect(event, rowIndex) {
        const files = Array.from(event.target.files);
        const fileListDiv = document.getElementById(`batteryFileList-${rowIndex}`);
        const fileInput = event.target;

        if (!fileListDiv) return;

        if (!fileInput.selectedFiles) {
            fileInput.selectedFiles = [];
        }

        // 添加新文件到已选文件列表
        files.forEach(file => {
            fileInput.selectedFiles.push(file);

            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.setAttribute('data-file-name', file.name);
            fileItem.innerHTML = `
                <span class="file-name">${file.name}</span>
                <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
                <button type="button" class="btn-delete-file"
                        onclick="batteryChangeManager.removeFile(${rowIndex}, '${file.name}', this)">×</button>
            `;
            fileListDiv.appendChild(fileItem);
        });

        // 清空文件输入，允许重复选择同一文件
        event.target.value = '';
    }

    // 移除文件
    removeFile(rowIndex, fileName, button) {
        const fileInput = document.getElementById(`batteryFileInput-${rowIndex}`);
        const fileListDiv = document.getElementById(`batteryFileList-${rowIndex}`);
        const fileItem = button.closest('.file-item');

        if (fileInput.selectedFiles) {
            fileInput.selectedFiles = fileInput.selectedFiles.filter(file => file.name !== fileName);
        }

        fileItem.remove();

        if (fileListDiv.children.length === 0) {
            fileInput.value = '';
            fileInput.selectedFiles = [];
        }
    }

    // 删除电池更换履历行
    deleteBatteryChangeRow(button) {
        const row = button.closest('tr');
        const tbody = row.parentElement;

        if (tbody.children.length > 1) {
            row.remove();
        } else {
            // 如果只剩一行，清空内容但不删除行
            const inputs = row.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.type === 'file') {
                    input.value = '';
                    input.selectedFiles = [];
                } else {
                    input.value = '';
                }
            });

            // 清空文件列表
            const fileList = row.querySelector('.file-list');
            if (fileList) {
                fileList.innerHTML = '';
            }
        }
    }

    // 提交电池更换履历表单
    async submitBatteryChangeForm(form) {
        try {
            const tbody = document.getElementById('batteryChangeTableBody');
            const rows = tbody.querySelectorAll('tr');
            let validRowCount = 0;

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];

                // 获取表单数据
                const agvInfo = row.querySelector(`select[name="agv_info_${i}"]`).value;
                const oldBattery = row.querySelector(`input[name="old_battery_${i}"]`).value;
                const newBattery = row.querySelector(`input[name="new_battery_${i}"]`).value;
                const changeDate = row.querySelector(`input[name="change_date_${i}"]`).value;
                const moveOutDate = row.querySelector(`input[name="move_out_date_${i}"]`).value;
                const returnDate = row.querySelector(`input[name="return_date_${i}"]`).value;

                // 检查必填字段
                if (!agvInfo || !oldBattery || !newBattery || !changeDate || !moveOutDate || !returnDate) {
                    continue; // 跳过空行
                }

                const formData = new FormData();

                // 添加表单数据
                formData.append('agv_info', agvInfo);
                formData.append('old_battery', oldBattery);
                formData.append('new_battery', newBattery);
                formData.append('change_date', changeDate);
                formData.append('move_out_date', moveOutDate);
                formData.append('return_date', returnDate);

                // 添加文件
                const fileInput = row.querySelector('input[name="battery_files[]"]');
                if (fileInput && fileInput.selectedFiles && fileInput.selectedFiles.length > 0) {
                    fileInput.selectedFiles.forEach(file => {
                        formData.append('files[]', file);
                    });
                }

                try {
                    const response = await fetch('php/submit_battery.php', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();
                    if (result.success) {
                        validRowCount++;
                    } else {
                        throw new Error(`第${i+1}行: ${result.message}`);
                    }
                } catch (error) {
                    console.error('提交失败:', error);
                    throw error;
                }
            }

            if (validRowCount > 0) {
                alert(`成功添加了 ${validRowCount} 条电池更换记录`);
                form.reset();
                this.initializeBatteryChangeTable();
            } else {
                alert('没有有效的电池更换数据被提交，请检查表单');
            }
        } catch (error) {
            console.error('提交失败:', error);
            alert('提交失败: ' + error.message);
        }
    }
}

// 电池更换履历查询管理类
class BatteryChangeListManager {
    constructor() {
        // 分页设置
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;

        // 搜索条件
        this.searchParams = {
            agvInfo: '',
            key: '',
            start_date: '',
            end_date: ''
        };

        this.init();
    }

        init() {
            this.bindSearchForm();
            this.initializePagination();
            this.loadBatteryChangeList();
            this.loadAgvOptions();
        }

        // 动态加载AGV选项到搜索表单
        async loadAgvOptions() {
            try {
                const response = await fetch('php/get_battery.php');
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                
                const result = await response.json();
                if (result.success) {
                    const uniqueAgvNumbers = [...new Set(result.data.map(item => item.唯一编号))]
                        .filter(Boolean)
                        .sort((a, b) => a.localeCompare(b, undefined, {numeric: true}));
                    
                    const select = document.getElementById('batterysearch-agvInfo');
                    if (select) {
                        select.innerHTML = '<option value="">全部</option>' + 
                            uniqueAgvNumbers.map(agvInfo => 
                                `<option value="${agvInfo}">${agvInfo}</option>`
                            ).join('');
                    }
                } else {
                    throw new Error(result.message || '获取AGV信息失败');
                }
            } catch (error) {
                console.error('加载AGV选项失败:', error);
                alert('加载车辆选项失败: ' + error.message);
            }
        }

    // 绑定搜索表单事件
    bindSearchForm() {
        const searchForm = document.getElementById('batterysearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();

                // 获取搜索条件
                this.searchParams.agvInfo = document.getElementById('batterysearch-agvInfo').value;
                this.searchParams.key = document.getElementById('batterysearch-key').value;
                this.searchParams.start_date = document.getElementById('batterysearch-start-date').value;
                this.searchParams.end_date = document.getElementById('batterysearch-end-date').value;

                // 重置页码并加载数据
                this.currentPage = 1;
                this.loadBatteryChangeList();
            });

            // 重置按钮事件
            const resetBtn = searchForm.querySelector('.sparepartsreset-btn');
            if (resetBtn) {
                resetBtn.addEventListener('click', (e) => {
                    e.preventDefault();

                    // 重置所有搜索条件
                    document.getElementById('batterysearch-agvInfo').value = '';
                    document.getElementById('batterysearch-key').value = '';
                    document.getElementById('batterysearch-start-date').value = '';
                    document.getElementById('batterysearch-end-date').value = '';

                    // 更新搜索参数
                    this.searchParams.agvInfo = '';
                    this.searchParams.key = '';
                    this.searchParams.start_date = '';
                    this.searchParams.end_date = '';

                    // 重置页码并加载数据
                    this.currentPage = 1;
                    this.loadBatteryChangeList();
                });
            }

            // 导出按钮事件
            const downloadBtn = searchForm.querySelector('.sparepartsdownload-btn');
            if (downloadBtn) {
                downloadBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.exportBatteryChangeData();
                });
            }
        }
    }

    // 加载电池更换履历列表
    async loadBatteryChangeList() {
        try {
            const queryParams = new URLSearchParams({
                page: this.currentPage,
                page_size: this.pageSize,
                agvInfo: this.searchParams.agvInfo,
                key: this.searchParams.key,
                start_date: this.searchParams.start_date,
                end_date: this.searchParams.end_date
            });

            const response = await fetch(`php/get_battery_change.php?${queryParams.toString()}`);
            const result = await response.json();

            if (result.success) {
                this.updateBatteryChangeTable(result.data);
                this.updatePagination(result);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载电池更换履历失败:', error);
            alert('加载失败：' + error.message);
        }
    }

    // 更新电池更换履历表格
    updateBatteryChangeTable(batteryChanges) {
        const tbody = document.getElementById('batteryListTableBody');
        if (!tbody) return;

        if (!batteryChanges || batteryChanges.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无电池更换记录</td></tr>';
            return;
        }

        tbody.innerHTML = batteryChanges.map(change => `
            <tr>
                <td>${change.agv_info}</td>
                <td>${change.old_battery}</td>
                <td>${change.new_battery}</td>
                <td>${change.change_date}</td>
                <td>${change.move_out_date}</td>
                <td>${change.return_date}</td>
                <td>
                    ${change.files && change.files.length > 0 ?
                        change.files.map(file =>
                            `<a href="php/download_battery_file.php?file=${encodeURIComponent(file.file_path)}"
                               style="display: block; margin: 2px 0; color: #007bff; text-decoration: none; cursor: pointer;"
                               title="点击下载 ${file.file_name}">
                               ${file.file_name}
                             </a>`
                        ).join('') :
                        '无文件'
                    }
                </td>
            </tr>
        `).join('');
    }

    // 导出电池更换履历数据
    async exportBatteryChangeData() {
        try {
            const queryParams = new URLSearchParams({
                page: 1,
                page_size: 0,
                agvInfo: this.searchParams.agvInfo,
                key: this.searchParams.key,
                start_date: this.searchParams.start_date,
                end_date: this.searchParams.end_date
            });

            const response = await fetch(`php/get_battery_change.php?${queryParams.toString()}`);
            const result = await response.json();

            if (!result.success) throw new Error(result.message);

            const ws = XLSX.utils.json_to_sheet(result.data.map(item => ({
                "车辆信息": item.agv_info,
                "更换前电池信息": item.old_battery,
                "更换后电池信息": item.new_battery,
                "电池更换时间": item.change_date,
                "车辆搬出时间": item.move_out_date,
                "车辆返回时间": item.return_date,
                "文件数量": item.files ? item.files.length : 0
            })));

            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "电池更换履历");
            XLSX.writeFile(wb, `电池更换履历_${new Date().toLocaleDateString()}.xlsx`);
        } catch (error) {
            console.error('导出失败:', error);
            alert('导出失败: ' + error.message);
        }
    }

    // 初始化分页控件
    initializePagination() {
        // 页码大小变化
        const pageSizeSelect = document.querySelector('#changelistTab .page-size');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.pageSize = parseInt(e.target.value);
                this.currentPage = 1;
                this.loadBatteryChangeList();
            });
        }

        // 页码导航按钮
        document.querySelector('#changelistTab .btn-prev-page')?.addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.loadBatteryChangeList();
            }
        });

        document.querySelector('#changelistTab .btn-next-page')?.addEventListener('click', () => {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
                this.loadBatteryChangeList();
            }
        });

        // 首页和末页
        document.querySelector('#changelistTab .btn-first-page')?.addEventListener('click', () => {
            this.currentPage = 1;
            this.loadBatteryChangeList();
        });

        document.querySelector('#changelistTab .btn-last-page')?.addEventListener('click', () => {
            this.currentPage = this.totalPages;
            this.loadBatteryChangeList();
        });

        // 页码输入框
        const pageInput = document.querySelector('#changelistTab .current-page');
        if (pageInput) {
            pageInput.addEventListener('change', (e) => {
                const page = parseInt(e.target.value);
                if (page >= 1 && page <= this.totalPages) {
                    this.currentPage = page;
                    this.loadBatteryChangeList();
                } else {
                    e.target.value = this.currentPage;
                }
            });
        }
    }

    // 更新分页信息
    updatePagination(result) {
        this.totalPages = result.totalPages;

        const pagination = document.querySelector('#changelistTab .pagination');
        if (pagination) {
            pagination.querySelector('.total-count').textContent = result.total;
            pagination.querySelector('.total-pages').textContent = result.totalPages;
            pagination.querySelector('.current-page').value = result.currentPage;
        }
    }
}

// 创建电池更换履历管理器实例
let batteryChangeManager;
let batteryChangeListManager;

document.addEventListener('DOMContentLoaded', function() {
    const changeinTab = document.getElementById('changeinTab');
    if (changeinTab && !batteryChangeManager) {
        batteryChangeManager = new BatteryChangeManager();
    }

    const changelistTab = document.getElementById('changelistTab');
    if (changelistTab && !batteryChangeListManager) {
        batteryChangeListManager = new BatteryChangeListManager();
    }
});