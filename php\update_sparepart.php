<?php
require_once 'db_config.php';
header('Content-Type: application/json');
session_start();

try {
    // 检查必填字段
    $required_fields = ['name', 'model', 'location', 'code', 'use','state','quantity'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("缺少必填字段: $field");
        }
    }

    // 获取并验证数据
    $id = intval($_POST['id']);
    $name = trim($_POST['name']);
    $model = trim($_POST['model']);
    $location = trim($_POST['location']);
    $code = trim($_POST['code']);
    $use = trim($_POST['use']);
    $state = trim($_POST['state']);
    $section = trim($_POST['section']);
    $quantity = trim($_POST['quantity']);

    // 准备SQL语句
    $sql = "UPDATE sparepartlist 
            SET name = ?, 
                model = ?, 
                location = ?, 
                code = ?, 
                `use` = ?,
                section = ?,
                state = ?,
                quantity = ?
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("准备语句失败: " . $conn->error);
    }

    // 绑定参数并执行
    $stmt->bind_param('sssssssii', $name, $model, $location, $code, $use, $section, $state, $quantity, $id);
    
    if (!$stmt->execute()) {
        throw new Exception("执行失败: " . $stmt->error);
    }

    // 检查影响行数
    if ($stmt->affected_rows === 0) {
        throw new Exception("没有修改任何数据或记录不存在");
    }

    echo json_encode([
        'success' => true,
        'message' => '修改成功'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>