﻿document.addEventListener('DOMContentLoaded', function() {
    // 加载业绩管理表格数据
    loadKPIData();
    this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
    if (this.userInfo.account === 'admin') {
    document.getElementById('updatebtn').style.display = 'block';
    } else {
    document.getElementById('updatebtn').style.display = 'none';
    }
    // 设置查询表单提交事件
    document.getElementById('kpisearchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        searchKPI();
    });
    
    // 设置业绩管理选项卡按钮事件
    setupKPIManagementButtons();
    
    // 初始化分页
    initPagination();
});


// 当前表格数据
let kpiTableData = [];
// 当前页码和每页数量
let currentPage = 1;
let pageSize = 10;
// 总记录数
let totalRecords = 0;
// 领班列表
let leaderList = [];
// 编辑模式
let editMode = false;

// 加载KPI数据
function loadKPIData() {
    fetch('php/get_kpi.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                kpiTableData = data.data;
                leaderList = data.leaders;
                totalRecords = kpiTableData.length;
                
                // 更新领班下拉框
                updateLeaderSelect(leaderList);
                
                // 渲染表格
                renderKPITable();
                
                // 更新分页信息
                updatePagination();
                
                // 加载图表
                loadKPICharts();
            } else {
                alert('加载数据失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('加载数据出错:', error);
            alert('加载数据出错');
        });
}

// 更新领班下拉框
function updateLeaderSelect(leaders) {
    const leaderSelect = document.getElementById('kpisearch-leader');
    leaderSelect.innerHTML = '<option value="">全部</option>';
    
    leaders.forEach(leader => {
        const option = document.createElement('option');
        option.value = leader;
        option.textContent = leader;
        leaderSelect.appendChild(option);
    });
}

// 渲染KPI表格
function renderKPITable() {
    const table = document.getElementById('kpiTable');
    const tbody = table.querySelector('tbody') || document.createElement('tbody');
    
    if (!table.querySelector('tbody')) {
        table.appendChild(tbody);
    } else {
        tbody.innerHTML = '';
    }
    
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, kpiTableData.length);
    
    for (let i = startIndex; i < endIndex; i++) {
        const row = kpiTableData[i];
        const tr = document.createElement('tr');
        
        // 添加所有数据列
        tr.innerHTML = `
            <td>${row.date || ''}</td>
            <td>${row.class || ''}</td>
            <td>${row.shift || ''}</td>
            <td>${row.floor || ''}</td>
            <td>${row.leader || ''}</td>
            <td>${row.kpi !== null ? row.kpi.toFixed(2) : ''}</td>
            <td class="editable" data-field="FP">${row.FP !== null ? row.FP.toFixed(2) : ''}</td>
            <td class="editable" data-field="QSM">${row.QSM !== null ? row.QSM.toFixed(2) : ''}</td>
            <td class="editable" data-field="quality">${row.quality !== null ? row.quality.toFixed(2) : ''}</td>
            <td class="editable" data-field="5S3D">${row['5S3D'] !== null ? row['5S3D'].toFixed(2) : ''}</td>
            <td class="editable" data-field="env">${row.env !== null ? row.env.toFixed(2) : ''}</td>
            <td class="editable" data-field="info">${row.info !== null ? row.info.toFixed(2) : ''}</td>
            <td class="action-cell">
                <button class="btn-edit" data-index="${i}">编辑</button>
            </td>
        `;
        
        tbody.appendChild(tr);
    }
    
    // 设置表格行编辑事件
    setupRowEditEvents();
}

// 设置表格行编辑事件
function setupRowEditEvents() {
    const editButtons = document.querySelectorAll('#kpiTable .btn-edit');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const index = parseInt(this.getAttribute('data-index'));
            toggleRowEditMode(index);
        });
    });
}

// 切换行编辑模式
function toggleRowEditMode(index) {
    const row = document.querySelectorAll('#kpiTable tbody tr')[index - (currentPage - 1) * pageSize];
    if (!row) return;

    const editButton = row.querySelector('.btn-edit');
    const isEditing = row.classList.contains('editing');
    const editableCells = row.querySelectorAll('.editable');
    const rowIndex = (currentPage - 1) * pageSize + parseInt(editButton.getAttribute('data-index'));

    if (isEditing) {
        // 保存数据
        const updateData = {};
        editableCells.forEach(cell => {
            cell.contentEditable = false;
            cell.style.backgroundColor = '';
            cell.style.border = '';
            
            const field = cell.getAttribute('data-field');
            const value = parseFloat(cell.textContent.trim()) || 0;
            kpiTableData[rowIndex][field] = value;
            updateData[field] = value;
        });

        // 提交数据
        fetch('php/submit_kpi.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ...kpiTableData[rowIndex],
                ...updateData,
                kpi: calculateKPI(rowIndex)
            })
        })
        .then(response => response.json())
        .then(result => {
            if (!result.success) {
                console.error('保存失败:', result.message);
                alert('保存失败: ' + result.message);
                loadKPIData(); // 失败时恢复数据
            } else {
                // 更新本地KPI显示
                row.querySelector('td:nth-child(6)').textContent = calculateKPI(rowIndex) ?? '';
            }
        })
        .catch(error => {
            console.error('提交出错:', error);
            alert('提交出错');
            loadKPIData();
        });

        row.classList.remove('editing');
        editButton.textContent = '编辑';

    } else {
        // 进入编辑模式
        editableCells.forEach(cell => {
            cell.contentEditable = true;
            cell.style.backgroundColor = '#f8f8f8';
            cell.style.border = '2px solid #4c6fff';
        });
        row.classList.add('editing');
        editButton.textContent = '保存';
    }
}

// 计算KPI值
function calculateKPI(index) {
    const row = kpiTableData[index];
    if (row.FP === null) return null;
    
    return row.FP - (row.QSM || 0) - (row.quality || 0) - (row['5S3D'] || 0) - (row.env || 0) - (row.info || 0);
}

// 设置业绩管理选项卡按钮事件
function setupKPIManagementButtons() {
    const editBtn = document.querySelector('#kpiloadTab .button-group button:nth-child(1)');
    const submitBtn = document.querySelector('#kpiloadTab .button-group button:nth-child(2)');
    const cancelBtn = document.querySelector('#kpiloadTab .button-group button:nth-child(3)');
    const updateBtn = document.querySelector('#kpiloadTab .button-group button:nth-child(5)');
    
    // 默认隐藏提交和取消按钮
    submitBtn.style.display = 'none';
    cancelBtn.style.display = 'none';
    
    // 编辑按钮事件
    editBtn.addEventListener('click', function() {
        editMode = true;
        toggleTableEditMode(true);
        showSubmitCancelButtons();
    });
    
    // 提交按钮事件
    submitBtn.addEventListener('click', function() {
        submitKPIData();
        editMode = false;
        toggleTableEditMode(false);
        hideSubmitCancelButtons();
    });
    
    // 取消按钮事件
    cancelBtn.addEventListener('click', function() {
        editMode = false;
        toggleTableEditMode(false);
        hideSubmitCancelButtons();
        loadKPIData(); // 重新加载数据
    });

    // 更新按钮事件
    updateBtn.addEventListener('click', function() {
        updateKPIData(); // 更新数据
    });
}

// 更新数据功能
async function updateKPIData() {
    try {
        const response = await fetch('php/update_kpi.php', {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('数据更新成功！');
            location.reload(); 
        } else {
            alert(`更新失败：${result.error || '未知错误'}`);
        }
    } catch (error) {
        console.error('更新请求失败:', error);
        alert('网络请求失败，请检查控制台');
    }
}

// 切换表格编辑模式
function toggleTableEditMode(editable) {
    const editableCells = document.querySelectorAll('#kpiTable .editable');
    
    editableCells.forEach(cell => {
        if (editable) {
            const currentValue = cell.textContent.trim();
            if (!cell.querySelector('input')) {
                const input = document.createElement('input');
                input.type = 'number';
                input.step = '0.01';
                input.value = currentValue;
                input.style.width = '100%';
                
                cell.textContent = '';
                cell.appendChild(input);
            }
        } else {
            if (cell.querySelector('input')) {
                const input = cell.querySelector('input');
                cell.textContent = input.value;
            }
        }
    });
}

// 显示提交和取消按钮
function showSubmitCancelButtons() {
    const submitBtn = document.querySelector('#kpiloadTab .button-group button:nth-child(2)');
    const cancelBtn = document.querySelector('#kpiloadTab .button-group button:nth-child(3)');
    
    submitBtn.style.display = 'inline-block';
    cancelBtn.style.display = 'inline-block';
}

// 隐藏提交和取消按钮
function hideSubmitCancelButtons() {
    const submitBtn = document.querySelector('#kpiloadTab .button-group button:nth-child(2)');
    const cancelBtn = document.querySelector('#kpiloadTab .button-group button:nth-child(3)');
    
    submitBtn.style.display = 'none';
    cancelBtn.style.display = 'none';
}

// 提交KPI数据
function submitKPIData() {
    const rows = document.querySelectorAll('#kpiTable tbody tr');
    const updatedData = [];
    
    rows.forEach((row, index) => {
        const rowIndex = (currentPage - 1) * pageSize + index;
        if (rowIndex < kpiTableData.length) {
            const data = kpiTableData[rowIndex];
            const cells = row.querySelectorAll('.editable');
            
            // 从输入框获取更新的值
            cells.forEach(cell => {
                if (cell.querySelector('input')) {
                    const field = cell.getAttribute('data-field');
                    const value = parseFloat(cell.querySelector('input').value) || 0;
                    data[field] = value;
                }
            });
            
            updatedData.push({
                date: data.date,
                leader: data.leader,
                FP: data.FP,
                QSM: data.QSM,
                quality: data.quality,
                '5S3D': data['5S3D'],
                env: data.env,
                info: data.info
            });
        }
    });
    
    // 提交更新的数据
    updatedData.forEach(data => {
        fetch('php/submit_kpi.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (!result.success) {
                console.error('保存数据失败:', result.message);
            }
        })
        .catch(error => {
            console.error('提交数据出错:', error);
        });
    });
    
    // 重新加载数据
    setTimeout(loadKPIData, 500);
}

// 初始化分页
function initPagination() {
    const pageSizeSelect = document.querySelector('.page-size');
    const btnFirstPage = document.querySelector('.btn-first-page');
    const btnPrevPage = document.querySelector('.btn-prev-page');
    const btnNextPage = document.querySelector('.btn-next-page');
    const btnLastPage = document.querySelector('.btn-last-page');
    const currentPageInput = document.querySelector('.current-page');
    
    // 设置每页显示数量
    pageSizeSelect.addEventListener('change', function() {
        pageSize = parseInt(this.value);
        currentPage = 1;
        renderKPITable();
        updatePagination();
    });
    
    // 首页按钮
    btnFirstPage.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage = 1;
            renderKPITable();
            updatePagination();
        }
    });
    
    // 上一页按钮
    btnPrevPage.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            renderKPITable();
            updatePagination();
        }
    });
    
    // 下一页按钮
    btnNextPage.addEventListener('click', function() {
        const totalPages = Math.ceil(totalRecords / pageSize);
        if (currentPage < totalPages) {
            currentPage++;
            renderKPITable();
            updatePagination();
        }
    });
    
    // 末页按钮
    btnLastPage.addEventListener('click', function() {
        const totalPages = Math.ceil(totalRecords / pageSize);
        if (currentPage < totalPages) {
            currentPage = totalPages;
            renderKPITable();
            updatePagination();
        }
    });
    
    // 页码输入框
    currentPageInput.addEventListener('change', function() {
        const totalPages = Math.ceil(totalRecords / pageSize);
        let newPage = parseInt(this.value);
        
        if (isNaN(newPage) || newPage < 1) {
            newPage = 1;
        } else if (newPage > totalPages) {
            newPage = totalPages;
        }
        
        if (newPage !== currentPage) {
            currentPage = newPage;
            renderKPITable();
            updatePagination();
        }
        
        this.value = currentPage;
    });
}

// 更新分页信息
function updatePagination() {
    const totalCountElem = document.querySelector('.total-count');
    const totalPagesElem = document.querySelector('.total-pages');
    const currentPageInput = document.querySelector('.current-page');
    
    const totalPages = Math.ceil(totalRecords / pageSize);
    
    totalCountElem.textContent = totalRecords;
    totalPagesElem.textContent = totalPages;
    currentPageInput.value = currentPage;
    
    // 禁用/启用分页按钮
    document.querySelector('.btn-first-page').disabled = currentPage === 1;
    document.querySelector('.btn-prev-page').disabled = currentPage === 1;
    document.querySelector('.btn-next-page').disabled = currentPage === totalPages;
    document.querySelector('.btn-last-page').disabled = currentPage === totalPages;
}

// 业绩查询
function searchKPI() {
    const leader = document.getElementById('kpisearch-leader').value;
    const keyword = document.getElementById('kpisearch-key').value;
    const date = document.getElementById('kpisearch-date').value;
    
    let filteredData = [...kpiTableData];
    
    // 按领班筛选
    if (leader) {
        filteredData = filteredData.filter(item => item.leader === leader);
    }
    
    // 按日期筛选
    if (date) {
        filteredData = filteredData.filter(item => item.date === date);
    }
    
    // 按关键词筛选
    if (keyword) {
        filteredData = filteredData.filter(item => 
            (item.leader && item.leader.includes(keyword)) ||
            (item.class && item.class.includes(keyword)) ||
            (item.shift && item.shift.includes(keyword)) ||
            (item.floor && item.floor.includes(keyword))
        );
    }
    
    // 更新表格数据
    kpiTableData = filteredData;
    totalRecords = filteredData.length;
    currentPage = 1;
    
    renderKPITable();
    updatePagination();
}

// 加载KPI图表
function loadKPICharts() {
    // 准备图表数据
    const chartData = prepareChartData();
    
    // 渲染月别排名横向条形图
    renderMonthlyRankingChart(chartData.monthlyRanking);
    
    // 渲染周别趋势折线图
    renderWeeklyTrendChart(chartData.weeklyTrend);
    
    // 渲染近期业绩表格
    renderRecentPerformanceTable(chartData.recentWeeks, chartData.recentDays);
}

// 准备图表数据
function prepareChartData() {
    // 获取当前月份和周别
    const now = new Date();
    const currentMonth = now.getMonth() + 1;
    
    // 按领班分组数据
    const leaderData = {};
    leaderList.forEach(leader => {
        leaderData[leader] = {
            monthlyScores: [],
            weeklyScores: {},
            dailyScores: {}
        };
    });
    
    // 处理数据
    kpiTableData.forEach(item => {
        if (!item.leader || item.kpi === null) return;
        
        const date = new Date(item.date);
        const month = date.getMonth() + 1;
        const weekNumber = getWeekNumber(date);
        
        // 添加月度分数
        if (month === currentMonth) {
            leaderData[item.leader].monthlyScores.push(item.kpi);
        }
        
        // 添加周别分数
        if (!leaderData[item.leader].weeklyScores[weekNumber]) {
            leaderData[item.leader].weeklyScores[weekNumber] = [];
        }
        leaderData[item.leader].weeklyScores[weekNumber].push(item.kpi);
        
        // 添加日期分数
        leaderData[item.leader].dailyScores[item.date] = item.kpi;
    });
    
    // 计算月度平均分
    const monthlyRanking = [];
    for (const leader in leaderData) {
        const scores = leaderData[leader].monthlyScores;
        if (scores.length > 0) {
            const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
            monthlyRanking.push({
                leader: leader,
                score: avgScore
            });
        }
    }
    
    // 排序月度排名
    monthlyRanking.sort((a, b) => b.score - a.score);
    
    // 准备周别趋势数据
    const weeklyTrend = {};
    const weekNumbers = getRecentWeeks(4);
    
    leaderList.forEach(leader => {
        weeklyTrend[leader] = [];
        
        weekNumbers.forEach(week => {
            const scores = leaderData[leader].weeklyScores[week] || [];
            const avgScore = scores.length > 0 ? 
                scores.reduce((sum, score) => sum + score, 0) / scores.length : null;
            
            weeklyTrend[leader].push({
                week: week,
                score: avgScore
            });
        });
    });
    
    // 准备最近四周数据
    const recentWeeks = {};
    leaderList.forEach(leader => {
        recentWeeks[leader] = [];
        
        weekNumbers.forEach(week => {
            const scores = leaderData[leader].weeklyScores[week] || [];
            const avgScore = scores.length > 0 ? 
                scores.reduce((sum, score) => sum + score, 0) / scores.length : null;
            
            recentWeeks[leader].push(avgScore);
        });
    });
    
    // 准备最近七日数据
    const recentDays = {};
    const recentDates = getRecentDates(7);
    
    leaderList.forEach(leader => {
        recentDays[leader] = [];
        
        recentDates.forEach(date => {
            recentDays[leader].push(leaderData[leader].dailyScores[date] || null);
        });
    });
    
    return {
        monthlyRanking: monthlyRanking,
        weeklyTrend: weeklyTrend,
        recentWeeks: recentWeeks,
        recentDays: recentDays
    };
}

// 获取日期所在的周数
function getWeekNumber(date) {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

// 获取最近n周的周数
function getRecentWeeks(n) {
    const now = new Date();
    const currentWeek = getWeekNumber(now);
    const weeks = [];
    
    for (let i = 0; i < n; i++) {
        weeks.push(currentWeek - i);
    }
    
    return weeks.reverse();
}

// 获取最近n天的日期
function getRecentDates(n) {
    const dates = [];
    const now = new Date();
    
    for (let i = 0; i < n; i++) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const dateString = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        dates.push(dateString);
    }
    
    return dates.reverse();
}

// 渲染月别排名横向条形图
function renderMonthlyRankingChart(data) {
    // 清除现有图表
    const chartContainer = document.createElement('div');
    chartContainer.style.width = '48%';
    chartContainer.style.height = '400px';
    chartContainer.style.float = 'left';
    chartContainer.style.marginBottom = '20px';
    chartContainer.style.border = '1px solid #ddd';
    chartContainer.style.padding = '10px';
    chartContainer.style.borderRadius = '5px';
    chartContainer.id = 'monthlyRankingChart';
    
    const existingChart = document.getElementById('monthlyRankingChart');
    if (existingChart) {
        existingChart.parentNode.removeChild(existingChart);
    }
    

    
    // 添加到业绩查询选项卡
    const tabContent = document.getElementById('kpisearchTab');
    const searchForm = document.querySelector('.kpisearch');
    
    tabContent.insertBefore(chartContainer, searchForm.nextSibling);
    
    // 准备图表数据
    const leaders = data.map(item => item.leader).reverse();
    const scores = data.map(item => item.score).reverse();
    
    // 创建图表
    const chart = echarts.init(chartContainer);
    
    const option = {
        title: {
            text: '各领班月度业绩排名',
            left: 'center', 
            textStyle: {
              color: '#333',
              fontSize: 18
            }
          },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            name: '得分'
        },
        yAxis: {
            type: 'category',
            data: leaders,
            axisLabel: {
                interval: 0,
                rotate: 30
            }
        },
        series: [
            {
                name: '月度平均分',
                type: 'bar',
                data: scores.map(score => score.toFixed(2)),
                itemStyle: {
                    color: function(params) {
                        // 根据排名设置不同颜色
                        const colors = ['#f44336', '#ff9800', '#ffc107', '#4caf50', '#2196f3', '#9c27b0'];
                        return colors[params.dataIndex % colors.length];
                    }
                },
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{c}'
                }
            }
        ]
    };
    
    chart.setOption(option);
    
    // 窗口大小改变时重绘图表
    window.addEventListener('resize', function() {
        chart.resize();
    });
}

// 渲染周别趋势折线图
function renderWeeklyTrendChart(data) {
    // 清除现有图表
    const chartContainer = document.createElement('div');
    chartContainer.style.width = '48%';
    chartContainer.style.height = '400px';
    chartContainer.style.float = 'right';
    chartContainer.style.marginBottom = '20px';
    chartContainer.style.border = '1px solid #ddd';
    chartContainer.style.padding = '10px';
    chartContainer.style.borderRadius = '5px';
    chartContainer.id = 'weeklyTrendChart';
    
    const existingChart = document.getElementById('weeklyTrendChart');
    if (existingChart) {
        existingChart.parentNode.removeChild(existingChart);
    }
    

    
    // 添加到业绩查询选项卡
    const tabContent = document.getElementById('kpisearchTab');
    const searchForm = document.querySelector('.kpisearch');
    
    tabContent.insertBefore(chartContainer, searchForm.nextSibling);

    
    // 准备图表数据
    const weeks = data[leaderList[0]].map(item => `第${item.week}周`);
    const series = [];
    
    leaderList.forEach(leader => {
        series.push({
            name: leader,
            type: 'line',
            data: data[leader].map(item => item.score !== null ? item.score.toFixed(2) : '-'),
            smooth: true
        });
    });
    
    // 创建图表
    const chart = echarts.init(chartContainer);
    
    const option = {
        title:{
            text:'各领班周度业绩趋势',
            left: 'center', 
            textStyle: {
              color: '#333',
              fontSize: 18
            }
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: leaderList,
            orient: 'horizontal',
            bottom: 0
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: weeks
        },
        yAxis: {
            type: 'value',
            name: '得分'
        },
        series: series
    };
    
    chart.setOption(option);
    
    // 窗口大小改变时重绘图表
    window.addEventListener('resize', function() {
        chart.resize();
    });
}

// 渲染近期业绩表格
function renderRecentPerformanceTable(weekData, dayData) {
    // 清除现有表格
    const existingTable = document.getElementById('recentPerformanceTable');
    if (existingTable) {
        existingTable.parentNode.removeChild(existingTable);
    }
    
    // 创建表格容器
    const tableContainer = document.createElement('div');
    tableContainer.style.clear = 'both';
    tableContainer.style.width = '100%';
    tableContainer.id = 'recentPerformanceTable';
    
    // 创建左右容器
    const leftContainer = document.createElement('div');
    leftContainer.style.width = '49%';
    leftContainer.style.float = 'left';
    
    const rightContainer = document.createElement('div');
    rightContainer.style.width = '49%';
    rightContainer.style.float = 'right';
    
    // 添加到业绩查询选项卡
    const tabContent = document.getElementById('kpisearchTab');
    tabContent.appendChild(tableContainer);
    
    // 创建最近四周表格
    const weekTable = document.createElement('table');
    weekTable.className = 'display';
    weekTable.style.width = '100%';
    weekTable.style.marginBottom = '20px';
    
    // 创建表头
    const weekThead = document.createElement('thead');
    const weekHeaderRow = document.createElement('tr');
    weekHeaderRow.innerHTML = '<th>领班</th><th>近四周平均</th><th>第一周</th><th>第二周</th><th>第三周</th><th>第四周</th>';
    weekThead.appendChild(weekHeaderRow);
    weekTable.appendChild(weekThead);
    
    // 创建表体
    const weekTbody = document.createElement('tbody');
    
    leaderList.forEach(leader => {
        const weekRow = document.createElement('tr');
        const scores = weekData[leader];
        
        // 计算平均分
        const validScores = scores.filter(score => score !== null);
        const avgScore = validScores.length > 0 ? 
            validScores.reduce((sum, score) => sum + score, 0) / validScores.length : null;
        
        weekRow.innerHTML = `
            <td>${leader}</td>
            <td>${avgScore !== null ? avgScore.toFixed(2) : '-'}</td>
            <td>${scores[0] !== null ? scores[0].toFixed(2) : '-'}</td>
            <td>${scores[1] !== null ? scores[1].toFixed(2) : '-'}</td>
            <td>${scores[2] !== null ? scores[2].toFixed(2) : '-'}</td>
            <td>${scores[3] !== null ? scores[3].toFixed(2) : '-'}</td>
        `;
        
        weekTbody.appendChild(weekRow);
    });
    
    weekTable.appendChild(weekTbody);
    
    // 创建最近七日表格
    const dayTable = document.createElement('table');
    dayTable.className = 'display';
    dayTable.style.width = '100%';
    
    // 获取最近七日日期
    const recentDates = getRecentDates(7);
    
    // 创建表头
    const dayThead = document.createElement('thead');
    const dayHeaderRow = document.createElement('tr');
    dayHeaderRow.innerHTML = '<th>领班</th>';
    
    recentDates.forEach(date => {
        const dateObj = new Date(date);
        const formattedDate = `${dateObj.getMonth() + 1}/${dateObj.getDate()}`;
        dayHeaderRow.innerHTML += `<th>${formattedDate}</th>`;
    });
    
    dayThead.appendChild(dayHeaderRow);
    dayTable.appendChild(dayThead);
    
    // 创建表体
    const dayTbody = document.createElement('tbody');
    
    leaderList.forEach(leader => {
        const dayRow = document.createElement('tr');
        const scores = dayData[leader];
        
        dayRow.innerHTML = `<td>${leader}</td>`;
        
        scores.forEach(score => {
            dayRow.innerHTML += `<td>${score !== null ? score.toFixed(2) : '-'}</td>`;
        });
        
        dayTbody.appendChild(dayRow);
    });
    
    dayTable.appendChild(dayTbody);
    
    // 添加内容到左右容器
    leftContainer.appendChild(document.createElement('h4')).textContent = '最近四周业绩';
    leftContainer.appendChild(weekTable);
    
    rightContainer.appendChild(document.createElement('h4')).textContent = '最近七日业绩';
    rightContainer.appendChild(dayTable);
    
    // 将左右容器添加到主容器
    tableContainer.appendChild(leftContainer);
    tableContainer.appendChild(rightContainer);
    
    // 添加表格样式
    const style = document.createElement('style');
    style.textContent = `
        .display {
            border: 1px solid #ddd;
            border-collapse: collapse;
        }
        .display th, .display td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .display th {
            background-color: #f5f5f5;
        }
        .display tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .display tr:hover {
            background-color: #f0f0f0;
        }
    `;
    document.head.appendChild(style);
}
