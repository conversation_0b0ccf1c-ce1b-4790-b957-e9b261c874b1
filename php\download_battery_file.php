<?php
require_once 'db_config.php';

// 获取文件路径参数
$file_path = isset($_GET['file']) ? $_GET['file'] : '';

if (empty($file_path)) {
    http_response_code(400);
    echo "缺少文件参数";
    exit;
}

try {
    // 验证文件是否存在于数据库中
    $sql = "SELECT file_name, file_path FROM agv_battery_files WHERE file_path = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('s', $file_path);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        http_response_code(404);
        echo "文件不存在";
        exit;
    }
    
    $file_info = $result->fetch_assoc();
    $original_name = $file_info['file_name'];
    
    // 构建完整的文件路径
    $full_path = '../uploads/battery/' . $file_path;
    
    // 检查文件是否实际存在
    if (!file_exists($full_path)) {
        http_response_code(404);
        echo "文件不存在于服务器上";
        exit;
    }
    
    // 获取文件信息
    $file_size = filesize($full_path);
    $file_type = mime_content_type($full_path);
    
    // 设置下载头
    header('Content-Type: ' . $file_type);
    header('Content-Disposition: attachment; filename="' . $original_name . '"');
    header('Content-Length: ' . $file_size);
    header('Cache-Control: no-cache, must-revalidate');
    header('Pragma: no-cache');
    
    // 输出文件内容
    readfile($full_path);
    
} catch (Exception $e) {
    http_response_code(500);
    echo "下载失败: " . $e->getMessage();
}

$conn->close();
?>
