<?php
require_once 'db_config.php';
header('Content-Type: application/json');
session_start();

try {
    // 检查是否有文件上传
    if (empty($_FILES['files'])) {
        throw new Exception("没有选择文件");
    }

    // 获取备品ID
    $id = intval($_POST['id']);
    if ($id <= 0) {
        throw new Exception("无效的备品ID");
    }

    // 检查上传目录是否存在，不存在则创建
    $upload_dir = '../uploads/spareparts/';
    if (!file_exists($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }

    $uploaded_files = [];
    $files = $_FILES['files'];

    // 处理多个文件
    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] === UPLOAD_ERR_OK) {
            $tmp_name = $files['tmp_name'][$i];
            $name = $files['name'][$i];
            $type = $files['type'][$i];
            $size = $files['size'][$i];

            // 验证文件类型
            if (!in_array($type, ['image/jpeg', 'image/png', 'image/gif'])) {
                continue; // 跳过不支持的文件类型
            }

            // 生成唯一文件名
            $extension = pathinfo($name, PATHINFO_EXTENSION);
            $unique_filename = uniqid() . '_' . time() . '.' . $extension;
            $file_path = $upload_dir . $unique_filename;

            // 移动文件
            if (move_uploaded_file($tmp_name, $file_path)) {
                // 记录文件信息到数据库
                $sql = "INSERT INTO sparepart_files (sparepart_id, file_name, file_path, file_type, file_size) 
                        VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                
                if (!$stmt) {
                    throw new Exception("准备语句失败: " . $conn->error);
                }

                $relative_path = $unique_filename;
                $stmt->bind_param('isssi', $id, $name, $relative_path, $type, $size);
                
                if (!$stmt->execute()) {
                    // 如果数据库插入失败，删除已上传的文件
                    unlink($file_path);
                    throw new Exception("保存文件信息失败: " . $stmt->error);
                }

                $uploaded_files[] = [
                    'name' => $name,
                    'path' => $relative_path
                ];
            }
        }
    }

    if (empty($uploaded_files)) {
        throw new Exception("没有文件上传成功");
    }

    echo json_encode([
        'success' => true,
        'message' => '文件上传成功',
        'files' => $uploaded_files
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 