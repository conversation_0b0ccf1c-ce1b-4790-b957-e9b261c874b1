<?php
require_once 'sqlsrv_config.php';

header('Content-Type: application/json;charset=utf-8');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");


// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // 开始事务
    sqlsrv_begin_transaction($conn);

    // 获取表单数据数组
    // 为防止出错，表单提交多少数据，获取多少数据，没提交的，不要获取，会出错
    $sections = $_POST['section'] ?? [];
    // $projects = $_POST['project'] ?? [];
    $units = $_POST['unit'] ?? [];
    $lines = $_POST['line'] ?? [];
    $partnames = $_POST['partname'] ?? [];
    // $parttypes = $_POST['parttype'] ?? [];
    $partbrands = $_POST['partbrand'] ?? [];
    $partmodels = $_POST['partmodel'] ?? [];
    $stockcounts = $_POST['stockcount'] ?? [];
    $usepositions = $_POST['useposition'] ?? [];
    $partlevels = $_POST['partlevel'] ?? [];
    $saftystocks = $_POST['saftystock'] ?? [];
    $damagedhistorys = $_POST['damagedhistory'] ?? [];
    $recorder = $_POST['recorder'] ?? '';

    // 处理每一行数据
    $successCount = 0;
    $errors = [];

    for ($i = 0; $i < count($units); $i++) {
        // 跳过空行
        if (empty($lines[$i]) || empty($units[$i]) || empty($partnames[$i])) continue;
        // 简单的输入验证 
        if (!is_numeric($stockcounts[$i])) { 
            throw new Exception("第 ". ($i + 1). " 行的库存数量不是有效的数字"); 
        }

        $sql = "INSERT INTO parts_bom (section, unit, line, partname, partbrand, partmodel,
            stockcount, useposition, partlevel, saftystock, damagedhistory, recorder
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = array($sections[$i], $units[$i], $lines[$i], $partnames[$i],
            $partbrands[$i], $partmodels[$i], $stockcounts[$i], $usepositions[$i],
            $partlevels[$i], $saftystocks[$i], $damagedhistorys[$i], $recorder);
            
        $stmt = sqlsrv_query($conn, $sql, $params);
        if (!$stmt) {
            throw new Exception(print_r(sqlsrv_errors(),true));
        }

        $successCount++;
    }

    // 提交事务
    sqlsrv_commit($conn);

    echo json_encode([
        'success' => true,
        'message' => "成功提交 {$successCount} 条记录",
        'count' => $successCount
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 回滚事务
    if (isset($conn) && sqlsrv_ping($conn)) {
        sqlsrv_rollback($conn);
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}finally{
    // 关闭连接
    if (isset($stmt) && is_resource($stmt)) {
        sqlsrv_free_stmt($stmt);
    }
    if ($conn !== null && $conn !== false) {
        sqlsrv_close($conn);
    }
}


?> 