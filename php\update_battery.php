<?php
require_once 'db_config.php';

header('Content-Type: application/json');

// 获取从前端发送的JSON数据
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// 检查数据是否存在且'id'不为空
if (!$data || empty($data['id'])) {
    echo json_encode([
        'success' => false,
        'message' => '无效的请求数据或缺少ID'
    ]);
    exit;
}

try {
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }

    $conn->set_charset("utf8mb4");

    // 准备更新语句
    $sql = "UPDATE `battery` SET 
        `栋别` = ?, `产线内/外` = ?, `位置区域` = ?, `对应充电桩` = ?, `归类` = ?, `物品名称` = ?, `使用状态` = ?, 
        `电池型号` = ?, `储能类型` = ?, `电池厂家` = ?, `导入日期` = ?, `充电类型` = ?, `电池认证` = ?, 
        `电池容量` = ?, `寿命(年)` = ?, `电池规格` = ?, `电池数量` = ?, `点检周期` = ?, `管理部门` = ?, 
        `部门长` = ?, `科室` = ?, `科长` = ?, `担当` = ?, `唯一编号` = ?
    WHERE `id` = ?";

    $stmt = $conn->prepare($sql);

    if (!$stmt) {
        throw new Exception("SQL语句准备失败: " . $conn->error);
    }

    // 绑定参数
    $stmt->bind_param(
        "sssssssssssssidsissssssss",
        $data['栋别'],
        $data['产线内/外'],
        $data['位置区域'],
        $data['对应充电桩'],
        $data['归类'],
        $data['物品名称'],
        $data['使用状态'],
        $data['电池型号'],
        $data['储能类型'],
        $data['电池厂家'],
        $data['导入日期'],
        $data['充电类型'],
        $data['电池认证'],
        $data['电池容量'],
        $data['寿命(年)'],
        $data['电池规格'],
        $data['电池数量'],
        $data['点检周期'],
        $data['管理部门'],
        $data['部门长'],
        $data['科室'],
        $data['科长'],
        $data['担当'],
        $data['唯一编号'],
        $data['id']
    );

    // 执行更新
    if ($stmt->execute()) {
        echo json_encode(['success' => true]);
    } else {
        throw new Exception("数据库更新失败: " . $stmt->error);
    }

    $stmt->close();
    
} catch (Exception $e) {
    // 错误处理
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 