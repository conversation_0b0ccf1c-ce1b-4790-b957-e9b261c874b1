<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }

    $sql = "SELECT * FROM battery";
    $result = $conn->query($sql);

    if (!$result) {
        throw new Exception("查询失败: " . $conn->error);
    }
    
    $batteries = $result->fetch_all(MYSQLI_ASSOC);

    echo json_encode([
        'success' => true,
        'data' => $batteries
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>