<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"> 
    <meta http-equiv="Pragma" content="no-cache"> <meta http-equiv="Expires" content="0"> 
    <title>通知发布 - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/tabs.js" defer></script>
    <script src="js/notice.js" defer></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'publish':
                        switchTab('publish');
                        break;
                    case 'history':
                        switchTab('history');
                        break;
                    default:
                        switchTab('publish');
                }
            }
        });
    </script>
</head>
<body style="margin: 0; padding: 10px;">

    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <div class="tabs" style="display: none;">
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('publish')">发布通知</button>
                <button class="tab-button" onclick="switchTab('history')">历史记录</button>
            </div>
        </div>
            
        <!-- 主要内容区域 -->
        <div class="notice-content">
            <!-- 发布通知选项卡 -->
            <div id="publishTab" class="tab-content active">
                <div class="notice-editor">
                    <form id="noticeForm" class="notice-form">
                            <div class="form-group">
                                <label for="notice-related">关联事项</label>
                                <select id="notice-related" name="related" required>
                                    <option value="">请选择</option>
                                    <option value="故障">故障</option>
                                    <option value="切机">切机</option>
                                    <option value="备品">备品</option>
                                    <option value="改善">改善</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="notice-days">展示天数</label>
                                <select id="notice-days" name="days" required>
                                    <option value="">请选择</option>
                                    <option value="1">1天</option>
                                    <option value="3">3天</option>
                                    <option value="7">7天</option>
                                    <option value="14">14天</option>
                                    <option value="30">30天</option>
                                    <option value="0">永久</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="notice-publisher">发布人</label>
                                <input type="text" id="notice-publisher" name="publisher" readonly>
                            </div>

                            <div class="form-group">
                                <label for="notice-project">工程</label>
                                <select id="notice-project" name="project">
                                </select>
                            </div>


                        <div class="form-group">
                            <label for="notice-content">通知内容</label>
                            <textarea id="notice-content" name="content" rows="10" required 
                                placeholder="请输入通知内容..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="notice-file">选择附件</label>
                            <input type="file" id="fileUpload" name="fileUpload">
                            <div id="fileList" class="file-list"></div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="submit-btn">发布通知</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 历史记录选项卡 -->
            <div id="historyTab" class="tab-content">
                <div class="notice-list">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>关联事项</th>
                                <th>通知内容</th>
                                <th>附件</th>
                                <th>发布人</th>
                                <th>状态</th>
                                <th>发布时间</th>
                                <th>结束时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过 JavaScript 动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 