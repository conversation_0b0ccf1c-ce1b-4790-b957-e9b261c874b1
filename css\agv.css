.batterylist .data-table th,
.batterylist .data-table td{
    text-align: center;
    white-space: nowrap;  /* 不允许文本换行 */
}

.batterylist .data-table th:nth-child(1),
.batterylist .data-table td:nth-child(1){
    position: sticky;
    left: 0px; 
    z-index: 2;
    min-width: 50px;
    background-color: #f5f7fa;
}

.batterylist .data-table th:nth-child(2),
.batterylist .data-table td:nth-child(2) {
    position: sticky;
    left: 60px; 
    z-index: 2;
    background-color: #f5f7fa;
}

.batterylist .data-table th:last-child,
.batterylist .data-table td:last-child {
    position: sticky;
    right: 0px;
    z-index: 2;
    background-color: #f5f7fa;
}

.batterylist .data-table th {
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #f5f7fa;
}

/* 处理固定行列交叉处的单元格 */
.batterylist .data-table th:nth-child(1),
.batterylist .data-table th:nth-child(2),
.batterylist .data-table th:last-child {
    z-index: 3;  /* 确保交叉点的表头单元格在最上层 */
    border-bottom: 1px solid #ddd;
}

/* 添加模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    width: 80%;
    position: relative;
    border-radius: 5px;
}

.modal-body h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
    border-bottom: 2px solid #4c6fff;
    padding-bottom: 5px;
    font-weight: 600;
}

.modal-body h4:first-child {
    margin-top: 0;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
}

.detail-table th,
.detail-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    vertical-align: middle;
}

.detail-table th {
    background-color: #f2f2f2;
}

td.editable-cell {
    background-color: #f8f8f8 !important;
    border: 1px solid #4c6fff !important;
    padding: 0;
}

.edit-input {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border: none;
    background-color: transparent;
    padding: 8px;
    outline: none;
    font-size: inherit;
    font-family: inherit;
}

.close-button {
    position: absolute;
    right: 15px;
    top: 10px;
    font-size: 24px;
    cursor: pointer;
}

.close-button:hover {
    color: #666;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 电池更换履历表单样式 */
.battery-change-editor {
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
}

.battery-change-form {
    width: 100%;
}

/* 履历录入表格样式 */
#changeinTab .splist-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    table-layout: fixed;
}

#changeinTab .splist-table th,
#changeinTab .splist-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
    vertical-align: middle;
    word-wrap: break-word;
    min-height: 40px;
}

#changeinTab .splist-table th {
    background-color: #f5f5f5;
    white-space: nowrap;
    font-weight: bold;
}

#changeinTab .splist-table select,
#changeinTab .splist-table input[type="text"],
#changeinTab .splist-table input[type="date"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    min-height: 40px;
}

#changeinTab .splist-table th:nth-child(1), /* 车辆信息 */
#changeinTab .splist-table td:nth-child(1) {
    width: 10%;
}

#changeinTab .splist-table th:nth-child(2), /* 更换前电池信息 */
#changeinTab .splist-table td:nth-child(2) {
    width: 16%;
}

#changeinTab .splist-table th:nth-child(3), /* 更换后电池信息 */
#changeinTab .splist-table td:nth-child(3) {
    width: 16%;
}

#changeinTab .splist-table th:nth-child(4), /* 电池更换时间 */
#changeinTab .splist-table td:nth-child(4) {
    width: 12%;
}

#changeinTab .splist-table th:nth-child(5), /* 车辆搬出时间 */
#changeinTab .splist-table td:nth-child(5) {
    width: 12%;
}

#changeinTab .splist-table th:nth-child(6), /* 车辆返回时间 */
#changeinTab .splist-table td:nth-child(6) {
    width: 12%;
}

#changeinTab .splist-table th:nth-child(7), /* 更换评估报告 */
#changeinTab .splist-table td:nth-child(7) {
    width: 16%;
}

#changeinTab .splist-table th:nth-child(8), /* 操作 */
#changeinTab .splist-table td:nth-child(8) {
    width: 6%;
}

/* 文件上传容器样式 */
.file-upload-container {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-height: 60px;
    width: 100%;
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin-bottom: 5px;
    max-height: 120px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3px 6px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    font-size: 11px;
}

.file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 5px;
}

.btn-add-file {
    padding: 4px 12px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
    display: block;
    margin: 0 auto;
}

.btn-add-file:hover {
    background-color: #218838;
}

.btn-delete-file {
    padding: 2px 6px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 2px;
    cursor: pointer;
    font-size: 10px;
    white-space: nowrap;
    min-width: 40px;
}

.btn-delete-file:hover {
    background-color: #c82333;
}

/* 删除按钮样式 */
#changeinTab .btn-delete {
    padding: 4px 12px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
}

#changeinTab .btn-delete:hover {
    background-color: #c82333;
}

/* 表单操作按钮区域 */
#changeinTab .form-actions {
    margin-top: 20px;
    padding: 15px 0;
    border-top: 1px solid #ddd;
}

#changeinTab .btn-add-rows,
#changeinTab .btn-submit {
    margin-right: 10px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

#changeinTab .btn-add-rows {
    background-color: #007bff;
    color: white;
}

#changeinTab .btn-add-rows:hover {
    background-color: #0056b3;
}

#changeinTab .btn-submit {
    background-color: #007bff;
    color: white;
}

#changeinTab .btn-submit:hover {
    background-color: #0056b3;
}


/* 履历查询表格样式 */
#battery-list .splist-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    table-layout: fixed;
}

#battery-list .splist-table th,
#battery-list .splist-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
    vertical-align: middle;
    word-wrap: break-word;
    min-height: 40px;
}

#battery-list .splist-table th {
    background-color: #f5f5f5;
    white-space: nowrap;
    font-weight: bold;
}

#battery-list .splist-table th:nth-child(1), /* 车辆信息 */
#battery-list .splist-table td:nth-child(1) {
    width: 8%;
}

#battery-list .splist-table th:nth-child(2), /* 更换前电池信息 */
#battery-list .splist-table td:nth-child(2),
#battery-list .splist-table th:nth-child(3), /* 更换后电池信息 */
#battery-list .splist-table td:nth-child(3) {
    width: 16%;
}

#battery-list .splist-table th:nth-child(4), /* 车辆搬出时间 */
#battery-list .splist-table td:nth-child(4),
#battery-list .splist-table th:nth-child(5), /* 车辆返回时间 */
#battery-list .splist-table td:nth-child(5),
#battery-list .splist-table th:nth-child(6), /* 电池更换时间 */
#battery-list .splist-table td:nth-child(6) {
    width: 8%;
}

#battery-list .splist-table th:nth-child(7), /* 更换评估报告 */
#battery-list .splist-table td:nth-child(7) {
    width: 20%;
    white-space: nowrap; /* 不换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
}