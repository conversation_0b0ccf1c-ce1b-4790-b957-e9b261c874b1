<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    if (!isset($data['id'])) {
        throw new Exception('缺少通知ID');
    }

    $id = $data['id'];

    // 首先检查通知是否存在且是否是当前用户发布的
    $check_sql = "SELECT publisher FROM notice WHERE id = ?";
    $check_stmt = $conn->prepare($check_sql);
    if (!$check_stmt) {
        throw new Exception("准备检查语句失败: " . $conn->error);
    }

    $check_stmt->bind_param('i', $id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();
    $notice = $result->fetch_assoc();

    if (!$notice) {
        throw new Exception("通知不存在");
    }

    // 删除通知
    $delete_sql = "DELETE FROM notice WHERE id = ?";
    $delete_stmt = $conn->prepare($delete_sql);
    if (!$delete_stmt) {
        throw new Exception("准备删除语句失败: " . $conn->error);
    }

    $delete_stmt->bind_param('i', $id);
    if (!$delete_stmt->execute()) {
        throw new Exception("删除失败: " . $delete_stmt->error);
    }

    echo json_encode([
        'success' => true,
        'message' => '删除成功'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 