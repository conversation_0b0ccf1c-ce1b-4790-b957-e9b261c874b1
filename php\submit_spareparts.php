<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 检查必填字段
    $required_fields = ['name', 'model', 'location', 'code', 'use', 'quantity', 'recorder'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("缺少必填字段: $field");
        }
    }

    // 获取表单数据
    $name = $_POST['name'];
    $model = $_POST['model'];
    $location = $_POST['location'];
    $code = $_POST['code'];
    $use = $_POST['use'];
    $quantity = intval($_POST['quantity']);
    $reason = $_POST['reason'];
    $recorder = $_POST['recorder'];
    $section = $_POST['section'];
    $state = $_POST['state'];
    
    

    
    // 准备SQL语句
    $sql = "INSERT INTO sparepartlist (name, model, location, code, `use`, quantity, reason , recorder, section, state) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("准备语句失败: " . $conn->error);
    }

    // 绑定参数
    $stmt->bind_param('sssssissss', 
        $name,
        $model,
        $location,
        $code,
        $use,
        $quantity,
        $reason,
        $recorder,
        $section,
        $state
    );

    // 执行插入
    if (!$stmt->execute()) {
        throw new Exception("执行失败: " . $stmt->error);
    }

    $sparepart_id = $stmt->insert_id;

    // 处理文件上传
    if (!empty($_FILES['files'])) {
        $uploadDir = '../uploads/spareparts/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        foreach ($_FILES['files']['tmp_name'] as $key => $tmp_name) {
            if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
                $fileName = $_FILES['files']['name'][$key];
                $fileType = $_FILES['files']['type'][$key];
                $fileSize = $_FILES['files']['size'][$key];
                
                // 生成唯一文件名
                $uniqueName = uniqid() . '_' . $fileName;
                $filePath = $uploadDir . $uniqueName;
                
                if (move_uploaded_file($tmp_name, $filePath)) {
                    // 保存文件信息到数据库
                    $sql = "INSERT INTO sparepart_files (sparepart_id, file_name, file_path, file_type, file_size) 
                           VALUES (?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('isssi', $sparepart_id, $fileName, $uniqueName, $fileType, $fileSize);
                    $stmt->execute();
                }
            }
        }
    }

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '备品信息添加成功'
    ]);

} catch (Exception $e) {
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 