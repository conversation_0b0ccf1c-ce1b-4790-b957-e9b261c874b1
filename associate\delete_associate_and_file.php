<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    $id = isset($data['id']) ? intval($data['id']) : 0;
    
    if ($id <= 0) {
        throw new Exception("无效的交接ID");
    }
    
    // 开始事务
    $conn->begin_transaction();
    
    // 查询备品附件
    $sql = "SELECT file_path FROM associate_files WHERE associate_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    // 删除文件记录
    $stmt = $conn->prepare("DELETE FROM associate_files WHERE associate_id = ?");
    $stmt->bind_param('i', $id);
    if (!$stmt->execute()) {
        throw new Exception("删除附件记录失败: " . $stmt->error);
    }
    
    // 删除主记录
    $stmt = $conn->prepare("DELETE FROM associatelist WHERE id = ?");
    $stmt->bind_param('i', $id);
    if (!$stmt->execute()) {
        throw new Exception("删除交接记录失败: " . $stmt->error);
    }
    
    // 提交事务
    $conn->commit();
    
    // 尝试删除物理文件
    while ($file = $result->fetch_assoc()) {
        $filePath = '../uploads/associates/' . $file['file_path'];
        if (file_exists($filePath)) {
            @unlink($filePath);
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => '交接记录已成功删除'
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if ($conn->ping()) {
        $conn->rollback();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 