<?php
require_once 'sqlsrv_config.php';

header('Content-Type: application/json;charset=utf-8');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

$sql = "SELECT TOP 10 * FROM dbo.TT_Main";
$stmt = sqlsrv_query($conn, $sql);

if ($stmt === false) {
    die(json_encode(array("error" => "查询失败: " . print_r(sqlsrv_errors(), true))));
}

$data = array();
while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
    // 处理日期格式（如果日期是DateTime对象）
    if ($row['DATE'] instanceof DateTime) {
        $row['DATE'] = $row['DATE']->format('Y-m-d');
    }
    $data[] = $row;
}

sqlsrv_free_stmt($stmt);
sqlsrv_close($conn);

echo json_encode($data);
?>