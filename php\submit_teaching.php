<?php
// 禁用错误显示到输出
ini_set('display_errors', 0);
error_reporting(E_ALL);
// 设置错误日志
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';


try {
    // 获取表单数据
    $title = $_POST['fileTitle'] ?? '';
    $category = $_POST['fileCategory'] ?? '';
    $upload_user = $_POST['upload_user'] ?? '';
    $unit = $_POST['fileUnit'] ?? '';
    $section = $_POST['fileSection'] ?? '';

    // 验证必填字段
    if (empty($title) || empty($category) || empty($unit)) {
        throw new Exception('请填写必要信息');
    }

    // 处理文件上传
    if (!isset($_FILES['files'])) {
        throw new Exception('请选择要上传的文件');
    }

    $uploadDir = '../uploads/teaching/';
    if (!file_exists($uploadDir)) {
        // 递归创建目录，确保权限正确
        if (!mkdir($uploadDir, 0777, true)) {
            throw new Exception('创建上传目录失败');
        }
        // 确保目录权限正确设置
        chmod($uploadDir, 0777);
    }

    $uploadedFiles = [];
    
    // 处理多个文件
    foreach ($_FILES['files']['tmp_name'] as $key => $tmp_name) {
        if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
            $fileName = $_FILES['files']['name'][$key];
            $fileSize = $_FILES['files']['size'][$key];
            
            // 生成唯一文件名
            $safeFileName = preg_replace('/[#<>:"\/\\|?*]/', '_', $fileName);
            $uniqueName = uniqid() . '_' . $safeFileName;
            $targetPath = $uploadDir . $uniqueName;
            
            if (move_uploaded_file($tmp_name, $targetPath)) {
                $uploadedFiles[] = [
                    'original_name' => $fileName,
                    'saved_name' => $uniqueName,
                    'size' => $fileSize
                ];
            }
        }
    }

    if (empty($uploadedFiles)) {
        throw new Exception('文件上传失败');
    }

    // 开始数据库事务
    $conn->begin_transaction();

    try {
        // 插入主记录
        $sql = "INSERT INTO teaching_materials (
            title, category, upload_user, upload_time, unit, section
        ) VALUES (?, ?, ?, NOW(), ?, ?)";

        $stmt = $conn->prepare($sql);
        $stmt->bind_param('sssss',
            $title,
            $category,
            $upload_user,
            $unit,
            $section
        );

        if (!$stmt->execute()) {
            throw new Exception('保存记录失败: ' . $stmt->error);
        }

        $materialId = $stmt->insert_id;

        // 插入文件记录
        $fileSql = "INSERT INTO teaching_files (
            material_id, file_name, file_path, file_size
        ) VALUES (?, ?, ?, ?)";
        
        $fileStmt = $conn->prepare($fileSql);
        
        foreach ($uploadedFiles as $file) {
            $fileStmt->bind_param('issi',
                $materialId,
                $file['original_name'],
                $file['saved_name'],
                $file['size']
            );
            
            if (!$fileStmt->execute()) {
                throw new Exception('保存文件记录失败: ' . $fileStmt->error);
            }
        }

        // 提交事务
        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => '资料上传成功'
        ]);

    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        
        // 删除已上传的文件
        foreach ($uploadedFiles as $file) {
            $filePath = $uploadDir . $file['saved_name'];
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
        
        throw $e;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 