<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 检查必填字段
    $required_fields = ['sparepart_id', 'action', 'quantity_change', 'operator', 'date'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception("缺少必填字段: $field");
        }
    }

    // 获取表单数据
    $sparepartId = intval($_POST['sparepart_id']);
    $action = $_POST['action'];
    $quantityChange = intval($_POST['quantity_change']);
    $useLocation = $_POST['usage_location'];
    $operator = $_POST['operator'];
    $date = $_POST['date'];
    
    // 验证数据
    if ($sparepartId <= 0) {
        throw new Exception("无效的备品ID");
    }
    
    if ($action !== '入库' && $action !== '领用') {
        throw new Exception("无效的变更动作");
    }
    
    // 如果是领用操作，确保数量为负数
    if ($action === '领用' && $quantityChange > 0) {
        $quantityChange = -$quantityChange;
    }
    
    // 获取当前备品信息
    $sql = "SELECT quantity,state FROM sparepartlist WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $sparepartId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("未找到指定备品");
    }
    
    $sparepart = $result->fetch_assoc();
    $currentQuantity = $sparepart['quantity'];
    $currentState = $sparepart['state'];
    
    // 计算新库存数量
    $newQuantity = $currentQuantity + $quantityChange;
    
    // 验证新库存数量不为负数
    if ($newQuantity < 0 || $currentState === 'open') {
        throw new Exception("库存数量不能为负数");
    }
    
    // 开始事务
    $conn->begin_transaction();
    
    try {
        // 插入备品履历记录
        $sql = "INSERT INTO sparepart_history (sparepart_id, action, quantity_change, stock_quantity, use_location, operator, change_date) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('isiisss', $sparepartId, $action, $quantityChange, $newQuantity, $useLocation, $operator, $date);
        $stmt->execute();
        
        // 更新备品库存数量
        $sql = "UPDATE sparepartlist SET quantity = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('ii', $newQuantity, $sparepartId);
        $stmt->execute();
        
        // 提交事务
        $conn->commit();
        
        // 返回成功响应
        echo json_encode([
            'success' => true,
            'message' => '备品履历添加成功',
            'data' => [
                'action' => $action,
                'quantity_change' => $quantityChange,
                'stock_quantity' => $newQuantity,
                'operator' => $operator,
                'date' => $date
            ]
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 