# 设备管理系统技术文档

## 1. 项目概述

### 项目名称
**设备综合管理系统 (Equipment Management System)**

### 项目目的
一个基于Web的企业级设备综合管理平台，用于管理工厂设备的交接、故障处理、备品管理、参数监控等全生命周期业务流程。

### 主要功能模块
- **交接管理**：设备交接查询、登录、分析
- **故障管理**：大故障查询、登录处理
- **现场管理**：现场管理、TactTime监控、领班业绩
- **备品管理**：备品列表、请购、录入、履历
- **部件BOM**：BOM查询、登录管理
- **切机管理**：金型信息、切机管理
- **PM管理**：PM查询、登录
- **电池管理**：AGV电池信息、更换履历
- **资料手册**：技术资料管理、上传
- **通知发布**：通知发布、历史记录
- **参数监控**：实时监控、基准管理
- **AI小助理**：智能问答系统

### 项目架构类型
**前后端分离的单体Web应用**，采用传统的LAMP架构模式，通过Ajax进行前后端数据交互。

## 2. 前端技术栈

### 核心框架/库
- **HTML5 + CSS3 + JavaScript (ES6+)**：现代Web标准
- **jQuery 1.11.3**：DOM操作和Ajax请求
- **Layui 2.2.3**：企业级UI组件库，提供导航、表格、表单等组件

### UI组件库和样式框架
- **Layui UI框架**：
  - 导航组件 (layui-nav)
  - 标签页组件 (layui-tab)
  - 表格组件 (layui-table)
  - 表单组件 (layui-form)
  - 弹层组件 (layui-layer)
- **自定义CSS样式**：
  - 响应式布局设计
  - 扁平化UI风格
  - 企业级配色方案

### 数据可视化
- **ECharts 5.x**：用于参数监控模块的图表展示
  - 实时数据曲线图
  - 多维度数据分析
  - 交互式图表操作

### 状态管理
- **localStorage**：用户信息和权限管理
- **sessionStorage**：临时数据存储
- **JavaScript对象**：页面级状态管理

### 构建工具和打包器
- **无构建工具**：采用传统的文件引用方式
- **直接引用CDN/本地文件**：所有依赖库通过script标签引入

### 开发工具和依赖包
```javascript
// 主要JavaScript库
- jquery-1.11.3.min.js     // DOM操作和Ajax
- layui.js                 // UI组件库
- echarts.js              // 图表库
- xlsx.js                 // Excel文件处理
- html2canvas.js          // 页面截图功能
- datatables.js           // 数据表格增强
```

### 前端特性
- **响应式设计**：支持桌面端和移动端
- **模块化架构**：每个功能模块独立的JS文件
- **组件化开发**：可复用的UI组件
- **权限控制**：基于用户级别的功能权限管理

## 3. 后端技术栈

### 编程语言和版本
- **PHP 7.4+**：服务端脚本语言
- **支持PHP 8.0+**：向上兼容

### Web框架
- **原生PHP**：无框架，直接使用PHP原生功能
- **面向过程编程**：简单直接的开发模式
- **模块化设计**：按功能模块组织PHP文件

### 数据库系统

#### 主数据库 (MySQL)
```php
// MySQL配置 (db_config.php)
$servername = "localhost";
$username = "root"; 
$password = "";
$dbname = "equipment_management";
```
- **MySQL 5.7+ / MariaDB 10.4+**
- **字符集**：utf8mb4 (支持完整Unicode)
- **存储引擎**：InnoDB (支持事务)

#### 辅助数据库 (SQL Server)
```php
// SQL Server配置 (sqlsrv_config.php)
$serverName = "************";
$database = "Tact_Time";
$username = "eqplink";
```
- **Microsoft SQL Server**：用于TactTime等特定模块
- **双数据库架构**：MySQL主库 + SQL Server辅库

### 服务器技术
- **Apache 2.4+ / Nginx 1.18+**：Web服务器
- **XAMPP开发环境**：本地开发栈
- **Linux/Windows服务器**：生产环境支持

### API设计模式
- **RESTful API风格**：标准HTTP方法
- **JSON数据格式**：统一的数据交换格式
- **统一响应格式**：
```php
// 标准API响应格式
{
    "success": true/false,
    "data": {...},
    "message": "操作结果信息"
}
```

### 认证和授权机制
- **Session-based认证**：PHP Session管理
- **多级权限控制**：
  - level: 用户级别 (21=OC, 22=LCM, 23=LOG)
  - splevel: 备品管理权限级别
- **基于角色的访问控制 (RBAC)**：
```php
// 权限验证示例
if ($userInfo->splevel === 1) {
    // 备品管理员权限
}
```

## 4. 开发环境和工具

### 包管理器
- **无包管理器**：手动管理依赖
- **直接文件引用**：所有库文件本地化存储

### 版本控制
- **Git**：源代码版本控制
- **文件结构化管理**：按功能模块组织代码

### 测试框架
- **手动测试**：无自动化测试框架
- **浏览器调试**：Chrome DevTools
- **PHP错误日志**：php_errors.log

### 代码质量工具
- **PHP内置错误报告**：error_reporting(E_ALL)
- **浏览器控制台**：JavaScript调试
- **手动代码审查**：团队协作审查

### 开发工具链
```
开发环境：
├── XAMPP (Apache + MySQL + PHP)
├── VS Code / PhpStorm (IDE)
├── Chrome DevTools (调试)
├── phpMyAdmin (数据库管理)
└── Git (版本控制)
```

## 5. 部署和运维

### 部署方式
- **传统Web部署**：文件直接上传到Web服务器
- **LAMP/WAMP栈**：Linux/Windows + Apache + MySQL + PHP
- **手动部署**：FTP/SFTP文件传输

### 服务器环境
```
生产环境要求：
├── PHP 7.4+ (推荐 8.0+)
├── MySQL 5.7+ 或 MariaDB 10.3+
├── Apache 2.4+ 或 Nginx 1.18+
├── SSL证书 (HTTPS支持)
└── 文件上传权限配置
```

### 容器化技术
- **暂无容器化**：传统部署方式
- **可扩展支持**：可迁移至Docker容器

### 运维特性
- **日志管理**：
  - PHP错误日志：`php/php_errors.log`
  - 聊天记录：`logs/chat_logs.txt`
  - 应用日志：`paramdata/logs/app.log`
- **错误处理**：自定义错误页面 (error目录)
- **性能监控**：基础的PHP性能日志
- **数据备份**：数据库定期备份策略

### 安全特性
- **SQL注入防护**：使用预处理语句
- **XSS防护**：输入数据过滤
- **CORS配置**：跨域请求控制
- **文件上传安全**：文件类型和大小限制
- **Session安全**：安全的会话管理

## 6. 数据库设计

### 主要数据表
```sql
-- 用户登录表
loginlist (account, password, name, department, section, level, splevel)

-- 设备参数监控表  
eqp_data (device_mark, model_mark, param_name, param_value, param_spec, trigger_time_new)

-- 交接管理表
associates (line, unit, phenomenon, cause, treatment, fault_class, fault_part, fault_code, status)

-- 备品管理表
spareparts (part_name, part_number, quantity, location, supplier)

-- 通知管理表
notices (title, content, publisher, status, publish_time, end_time)
```

### 数据库特性
- **多数据库支持**：MySQL + SQL Server
- **字符集**：utf8mb4 (完整Unicode支持)
- **索引优化**：关键字段建立索引
- **外键约束**：保证数据完整性

---

**文档版本**：v1.0  
**最后更新**：2025-08-18  
**维护团队**：设备管理系统开发组
