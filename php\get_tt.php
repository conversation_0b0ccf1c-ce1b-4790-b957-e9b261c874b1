<?php
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// 数据库配置信息
$serverName = "109.120.2.35";
$connectionOptions = array(
    "Database" => "Tact_Time",
    "Uid" => "eqplink",
    "PWD" => "eqplink",
    "CharacterSet" => "UTF-8"
);

try {
    // 建立连接
    $conn = sqlsrv_connect($serverName, $connectionOptions);
    if ($conn === false) {
        throw new Exception("Connection failed: " . print_r(sqlsrv_errors(), true));
    }

    // 获取查询参数
    $date = isset($_GET['date']) ? trim($_GET['date']) : '';
    $line = isset($_GET['line']) ? trim($_GET['line']) : '';
    $unit = isset($_GET['unit']) ? trim($_GET['unit']) : '';
    $period = isset($_GET['period']) ? trim($_GET['period']) : '';
    $module = isset($_GET['module']) ? trim($_GET['module']) : '';
    
    // 构建查询条件
    $whereConditions = ["HH IS NOT NULL"];
    $params = array();

    // 构建参数数组
    if (!empty($date)) {
        $whereConditions[] = "DATE = ?";
        $params[] = array($date, SQLSRV_PARAM_IN);
    }

    if (!empty($line)) {
        $whereConditions[] = "LINE = ?";
        $params[] = array($line, SQLSRV_PARAM_IN);
    }
    
    if (!empty($unit)) {
        $whereConditions[] = "UNITS = ?";
        $params[] = array($unit, SQLSRV_PARAM_IN);
    }

    if (!empty($period)) {
        $whereConditions[] = "PERIOD = ?";
        $params[] = array($period, SQLSRV_PARAM_IN);
    }

    if (!empty($module)) {
        $whereConditions[] = "MODULE = ?";
        $params[] = array($module, SQLSRV_PARAM_IN);
    }
    
    // 组合WHERE子句
    $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

    $sql = "SELECT 
            LINE,
            UNITS,
            UNIT,
            PRODUCT,
            CONVERT(varchar(10), DATE, 120) as DATE,
            HH,
            ROUND(TT, 1) as TT,
            PERIOD,
            MODULE
            FROM dbo.TT_Main
            $whereClause 
            ORDER BY DATE, CAST(HH AS INT)";


    $stmt = sqlsrv_query($conn, $sql, $params);
    
    if ($stmt === false) {
        $errors = sqlsrv_errors();
        $errorMessages = [];
        foreach ($errors as $error) {
            $errorMessages[] = "Code: {$error['code']}, Message: {$error['message']}";
        }
        throw new Exception("Query failed:\n" . implode("\n", $errorMessages));
    }

    // 处理结果集
    $data = array();
    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        // 处理日期对象转换
        if (isset($row['DATE']) && $row['DATE'] instanceof DateTime) {
            $row['DATE'] = $row['DATE']->format('Y-m-d');
        }
        $data[] = $row;
    }

    echo json_encode([
        'success' => true,
        'data' => $data
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}


if (isset($stmt) && is_resource($stmt)) {
    sqlsrv_free_stmt($stmt);
}
if ($conn !== null && $conn !== false) {
    sqlsrv_close($conn);
}
?>