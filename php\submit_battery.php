<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 检查必填字段
    $required_fields = ['agv_info', 'old_battery', 'new_battery', 'change_date', 'move_out_date', 'return_date'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("缺少必填字段: $field");
        }
    }

    // 获取表单数据
    $agv_info = $_POST['agv_info'];
    $old_battery = $_POST['old_battery'];
    $new_battery = $_POST['new_battery'];
    $change_date = $_POST['change_date'];
    $move_out_date = $_POST['move_out_date'];
    $return_date = $_POST['return_date'];
    
    // 准备SQL语句
    $sql = "INSERT INTO agv_battery (agv_info, old_battery, new_battery, change_date, move_out_date, return_date) 
            VALUES (?, ?, ?, ?, ?, ?)";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("准备语句失败: " . $conn->error);
    }

    // 绑定参数
    $stmt->bind_param('ssssss', 
        $agv_info,
        $old_battery,
        $new_battery,
        $change_date,
        $move_out_date,
        $return_date
    );

    // 执行插入
    if (!$stmt->execute()) {
        throw new Exception("执行失败: " . $stmt->error);
    }

    $battery_id = $stmt->insert_id;

    // 处理文件上传
    if (!empty($_FILES['files'])) {
        $uploadDir = '../uploads/battery/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        foreach ($_FILES['files']['tmp_name'] as $key => $tmp_name) {
            if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
                $fileName = $_FILES['files']['name'][$key];
                
                // 生成唯一文件名
                $safeFileName = preg_replace('/[#<>:"\/\\|?*]/', '_', $fileName);
                $uniqueName = uniqid() . '_' . $safeFileName;
                $filePath = $uploadDir . $uniqueName;
                
                if (move_uploaded_file($tmp_name, $filePath)) {
                    // 保存文件信息到数据库
                    $sql = "INSERT INTO agv_battery_files (battery_id, file_name, file_path)
                           VALUES (?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("准备文件插入语句失败: " . $conn->error);
                    }
                    $stmt->bind_param('iss', $battery_id, $fileName, $uniqueName);
                    if (!$stmt->execute()) {
                        throw new Exception("保存文件信息失败: " . $stmt->error);
                    }
                } else {
                    throw new Exception("文件上传失败: " . $fileName);
                }
            }
        }
    }

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => 'AGV电池更换记录添加成功'
    ]);

} catch (Exception $e) {
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
