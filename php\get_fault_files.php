<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';

try {
    if (!isset($_GET['fault_id'])) {
        throw new Exception('Missing fault_id parameter');
    }

    $fault_id = intval($_GET['fault_id']);
    
    $sql = "SELECT id, file_name, file_path, file_type, file_size 
            FROM fault_files 
            WHERE fault_id = ?";
            
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $fault_id);
    $stmt->execute();
    
    $result = $stmt->get_result();
    $files = [];
    
    while ($row = $result->fetch_assoc()) {
        $files[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $files
    ]);

} catch (Exception $e) {
    error_log("Error fetching fault files: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

if (isset($stmt)) {
    $stmt->close();
}
$conn->close();
?> 