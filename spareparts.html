﻿<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"> 
    <meta http-equiv="Pragma" content="no-cache"> <meta http-equiv="Expires" content="0"> 
    <title>备品管理 - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/tabs.js" defer></script>
    <script src="js/xlsx.js"></script>
    <!-- <script src="js/datatables.min.js"></script> -->
    <script src="js/spareparts.js" defer></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'splist':
                        switchTab('splist');
                        break;
                    case 'spneed':
                        switchTab('spneed');
                        break;
                    case 'spload':
                        switchTab('spload');
                        break;
                    case 'sphistory':
                        switchTab('sphistory');
                        break;
                    default:
                        switchTab('splist');
                }
            }
        });
    </script>
</head>
<body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <div class="tabs" style="display: none;">
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('splist')">备品列表</button>
                <button class="tab-button" onclick="switchTab('spneed')">备品请购</button>
                <button id="spload" class="tab-button" onclick="switchTab('spload')" style="display: none;">备品录入</button>
                <button id="sphistory" class="tab-button" onclick="switchTab('sphistory')" style="display: none;">备品履历</button>
            </div>
        </div>
            
        <!-- 主要内容区域 -->
        <div class="spareparts-content">
            <!-- 备品列表选项卡 -->
            <div id="splistTab" class="tab-content active">
                <div class="sparepartssearch">
                    <form id="sparepartssearchForm" action="#" method="get">
                        <div class="sparepartssearch-row">

                            <label for="sparepartssearch-state" style="display: none;">状态</label>
                            <select id="sparepartssearch-state" name="state" style="display: none;">
                                <option value="close">库存</option>
                                <option value="">全部</option>
                                <option value="open">请购</option>
                            </select>

                            <label for="sparepartssearch-section">科室</label>
                            <select id="sparepartssearch-section" name="section">
                                <option value="">全部</option>
                            </select>

                            <label for="sparepartssearch-key">关键词</label>
                            <input type="text" id="sparepartssearch-key" name="key" placeholder="请输入关键词查询">

                            <label for="sparepartssearch-location">库位</label>
                            <input type="text" id="sparepartssearch-location" name="location" placeholder="请输入库位查询">
                            
                            <label for="sparepartssearch-code">料号</label>
                            <input type="text" id="sparepartssearch-code" name="code" placeholder="请输入查询">
                            
                            <div class="button-group">
                                <button type="submit" class="sparepartssearch-btn">查询</button>
                                <button type="button" class="sparepartsreset-btn">重置</button>
                                <button type="button" class="sparepartsdownload-btn">导出</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div id="splist-list" class="splist-list">
                    <table id="mytable" class="data-table">
                        <thead>
                            <tr>
                                <th>备品名称</th>
                                <th>型号</th>
                                <th>库存位置</th>
                                <th>料号</th>
                                <th>使用位置</th>
                                <th>现有数量</th>
                                <th>科室</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过 JavaScript 动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 在表格后添加分页控件 -->
                <div class="pagination">
                    <div class="pagination-info">
                        共 <span class="total-count">0</span> 条记录，
                        每页 <select class="page-size">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select> 条
                    </div>
                    <div class="pagination-controls">
                        <button class="btn-first-page">首页</button>
                        <button class="btn-prev-page">上一页</button>
                        <span class="page-info">
                            第 <input type="number" class="current-page" min="1"> 页，
                            共 <span class="total-pages">0</span> 页
                        </span>
                        <button class="btn-next-page">下一页</button>
                        <button class="btn-last-page">末页</button>
                    </div>
                </div>                
            </div>

            <!-- 备品录入选项卡 -->
            <div id="sploadTab" class="tab-content">
                <div class="spload-editor">
                    <form action="#" method="post" class="spload-form">
                        <!-- 表头行 -->
                        <table class="splist-table">
                            <tr>
                                <th>备品名称</th>
                                <th>型号</th>
                                <th>库存位置</th>
                                <th>料号</th>
                                <th>使用位置</th>
                                <th>到货数量</th>
                                <th>使用科室</th>
                                <th>添加附件</th>
                                <th>操作</th>
                            </tr>
                            <!-- 初始10行空表格 -->
                            <tbody id="splistTableBody">
                                <!-- 这里将通过JavaScript动态生成10行空表格 -->
                            </tbody>
                        </table>
      
                        <div class="form-actions">
                            <button type="button" class="btn-add-rows">添加更多行</button>
                            <button type="submit" class="btn-submit">提交</button>
                            <button type="reset" class="btn-reset">重置</button>
                            <p style="color: red;">*请删除多余空行后提交！</p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 备品请购选项卡 -->
            <div id="spneedTab" class="tab-content">
                <div class="spneed-editor">
                    <form action="#" method="post" class="spneed-form">
                        <!-- 表头行 -->
                        <table class="splist-table">
                            <tr>
                                <th>备品名称</th>
                                <th>型号</th>
                                <th>料号</th>
                                <th>使用位置</th>
                                <th>请购数量</th>
                                <th>原因</th>
                                <th>添加附件</th>
                                <th>操作</th>
                            </tr>
                            <!-- 初始10行空表格 -->
                            <tbody id="spneedTableBody">
                                <!-- 这里将通过JavaScript动态生成10行空表格 -->
                            </tbody>
                        </table>
      
                        <div class="form-actions">
                            <button type="button" class="btn-add-rows">添加更多行</button>
                            <button type="submit" class="btn-submit">提交</button>
                            <button type="reset" class="btn-reset">重置</button>
                            <p style="color: red;">*请删除多余空行后提交！</p>
                        </div>
                    </form>
                </div>
                <div><br><br><br></div>

                <h3>请购List</h3>
                <div class="line"></div>
                <div class="sparepartssearch">
                    <form id="needsearchForm" action="#" method="get">
                        <div class="sparepartssearch-row">
                            
                            <label for="needsearch-key">模糊搜索</label>
                            <input type="text" id="needsearch-key" name="key" placeholder="请输入关键词">
                            
                            <label for="needsearch-code">料号</label>
                            <input type="text" id="needsearch-code" name="code" placeholder="请输入料号">

                            <label for="needsearch-section">科室</label>
                            <select id="needsearch-section" name="section">
                                <option value="">全部</option>
                            </select>
                            
                            <label for="needsearch-state">状态</label>
                            <select id="needsearch-state" name="state">
                                <option value="">全部</option>
                                <option value="open">请购</option>
                                <option value="run">请购中</option>
                                <option value="arrive">已到货</option>
                            </select>

                            <label id="needsearch-count"></label>
                            
                            <div class="button-group">
                                <button id="copy" type="button" class="sparepartshistorydownload-btn">复制到剪切板</button>
                                <button type="submit" class="sparepartssearch-btn">查询</button>
                                <button type="button" class="sparepartsreset-btn">重置</button>
                                <button type="button" class="sparepartsdownload-btn">导出</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div id="spneedlist-list" class="spneedlist-list">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>备品名称</th>
                                <th>型号</th>
                                <th>库存位置</th>
                                <th>料号</th>
                                <th>使用位置</th>
                                <th>请购数</th>
                                <th>原因</th>
                                <th>科室</th>
                                <th>申请人</th>
                                <th>请购日期</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过 JavaScript 动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 在表格后添加分页控件 -->
                <div class="pagination">
                    <div class="pagination-info">
                        共 <span class="total-count">0</span> 条记录，
                        每页 <select class="page-size">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select> 条
                    </div>
                    <div class="pagination-controls">
                        <button class="btn-first-page">首页</button>
                        <button class="btn-prev-page">上一页</button>
                        <span class="page-info">
                            第 <input type="number" class="current-page" min="1"> 页，
                            共 <span class="total-pages">0</span> 页
                        </span>
                        <button class="btn-next-page">下一页</button>
                        <button class="btn-last-page">末页</button>
                    </div>
                </div>    
                
            </div>

            <!-- 备品履历选项卡 -->
            <div id="sphistoryTab" class="tab-content">
                <div class="sparepartshistory">
                    <form id="sparepartshistoryForm" action="#" method="get">
                        <div class="sparepartshistory-row">

                            <!-- <label for="sparepartssearch-state" style="display: none;">状态</label>
                            <select id="sparepartssearch-state" name="state" style="display: none;">
                                <option value="">全部</option>
                                <option value="open">请购</option>
                                <option value="close">库存</option>
                            </select>

                            <label for="sparepartssearch-section">科室</label>
                            <select id="sparepartssearch-section" name="section">
                                <option value="">全部</option>
                            </select>

                            <label for="sparepartssearch-key">关键词</label>
                            <input type="text" id="sparepartssearch-key" name="key" placeholder="请输入关键词查询">

                            <label for="sparepartssearch-location">库位</label>
                            <input type="text" id="sparepartssearch-location" name="location" placeholder="请输入库位查询">
                            
                            <label for="sparepartssearch-code">料号</label>
                            <input type="text" id="sparepartssearch-code" name="code" placeholder="请输入查询"> -->
                            
                            <div class="button-group">
                                <!-- <button type="submit" class="sparepartshistorysearch-btn">查询</button>
                                <button type="button" class="sparepartshistoryreset-btn">重置</button> -->
                                <button id="sphiscopy" type="button" class="sparepartshistorydownload-btn">复制到剪切板</button>
                                <button id="sphisdown" type="button" class="sparepartshistorydownload-btn">导出Excel</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div id="sphistory-list" class="sphistory-list">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>型号</th>
                                <th>动作</th>
                                <th>数量</th>
                                <th>变更后库存</th>
                                <th>使用位置</th>
                                <th>操作人</th>
                                <th>日期</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过 JavaScript 动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
</body>
</html> 