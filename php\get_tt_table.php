<?php
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// 数据库配置信息
$serverName = "109.120.2.35";
$connectionOptions = array(
    "Database" => "Tact_Time",
    "Uid" => "eqplink",
    "PWD" => "eqplink",
    "CharacterSet" => "UTF-8"
);

const UNIT_ORDER = [
    'LD01' => 1, 'CU01' => 2, 'CL01' => 3, 'PA01' => 4, 'LC01' => 5, 'AC01' => 6,
    'LB01' => 7, 'SS01' => 8, 'PC01' => 9, 'AC01' => 10, 'AC02' => 11 ,'PT01' => 12,
    'HP01' =>13, 'PT02' => 14, 'HP02' =>15, 'FB01' => 16, 'BP01' =>17, 'FB02' => 18,
    'BP02' => 19, 'AG01' => 20, 'AK01' => 21, 'SD01' => 22, 'PB01' => 23, 'PB02' => 24,
    'PB03' => 25, 'UD01' => 26
];

try {
    $conn = sqlsrv_connect($serverName, $connectionOptions);
    
    if ($conn === false) {
        throw new Exception("Connection failed: " . print_r(sqlsrv_errors(), true));
    }

    $query = "
        SELECT 
        LINE, 
        UNITS, 
        ROUND(TT, 2) AS TT 
        FROM (
        SELECT 
            *,
            ROW_NUMBER() OVER (
                PARTITION BY LINE, UNITS 
                ORDER BY DATE DESC
            ) AS rn
        FROM TT_Main
        WHERE 
            DATE = CONVERT(VARCHAR(10), GETDATE(), 120)
            AND PERIOD = 'DAY' AND [MODULE]='Module_OFF'
    ) AS ranked
    WHERE rn = 1 ORDER BY LINE
    ";
    
    $result = sqlsrv_query($conn, $query);
    
    if ($result === false) {
        throw new Exception("Query failed: " . print_r(sqlsrv_errors(), true));
    }

    $dynamicData = [];
    $currentLine = null;
    $unitsData = [];
    
    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        if ($currentLine !== $row['LINE']) {
            if ($currentLine !== null) {
                // 对当前行的units进行排序
                usort($unitsData, function($a, $b) {
                    $aOrder = UNIT_ORDER[$a['units']] ?? PHP_INT_MAX;
                    $bOrder = UNIT_ORDER[$b['units']] ?? PHP_INT_MAX;
                    
                    if ($aOrder === $bOrder) {
                        return strnatcmp($a['units'], $b['units']);
                    }
                    return $aOrder - $bOrder;
                });
                $dynamicData[] = [
                    'line' => $currentLine,
                    'units' => $unitsData
                ];
            }
            $currentLine = $row['LINE'];
            $unitsData = [];
        }
        $unitsData[] = [
            'units' => $row['UNITS'],
            'tt' => $row['TT']
        ];
    }
    if ($currentLine !== null) {
        // 处理最后一行数据
        usort($unitsData, function($a, $b) {
            $aOrder = UNIT_ORDER[$a['units']] ?? PHP_INT_MAX;
            $bOrder = UNIT_ORDER[$b['units']] ?? PHP_INT_MAX;
            
            if ($aOrder === $bOrder) {
                return strnatcmp($a['units'], $b['units']);
            }
            return $aOrder - $bOrder;
        });
        $dynamicData[] = [
            'line' => $currentLine,
            'units' => $unitsData
        ];
    }
    
    sqlsrv_free_stmt($result);

    echo json_encode([
        'success' => true,
        'data' => $dynamicData
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

if ($conn) {
    sqlsrv_close($conn);
}
?>