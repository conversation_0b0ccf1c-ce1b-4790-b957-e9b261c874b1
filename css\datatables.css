/*
 * This combined file was created by the DataTables downloader builder:
 *   https://datatables.net/download
 *
 * To rebuild or modify this file with the latest versions of the included
 * software please visit:
 *   https://datatables.net/download/#dt/jq-3.7.0/jszip-3.10.1/pdfmake-0.2.7/dt-2.2.2/b-3.2.2/b-colvis-3.2.2/b-html5-3.2.2/b-print-3.2.2
 *
 * Included libraries:
 *   jQuery 3.7.0, JSZip 3.10.1, pdfmake 0.2.7, DataTables 2.2.2, Buttons 3.2.2, Column visibility 3.2.2, HTML5 export 3.2.2, Print view 3.2.2
 */

:root {
  --dt-row-selected: 13, 110, 253;
  --dt-row-selected-text: 255, 255, 255;
  --dt-row-selected-link: 9, 10, 11;
  --dt-row-stripe: 0, 0, 0;
  --dt-row-hover: 0, 0, 0;
  --dt-column-ordering: 0, 0, 0;
  --dt-html-background: white;
}
:root.dark {
  --dt-html-background: rgb(33, 37, 41);
}

table.dataTable td.dt-control {
  text-align: center;
  cursor: pointer;
}
table.dataTable td.dt-control:before {
  display: inline-block;
  box-sizing: border-box;
  content: "";
  border-top: 5px solid transparent;
  border-left: 10px solid rgba(0, 0, 0, 0.5);
  border-bottom: 5px solid transparent;
  border-right: 0px solid transparent;
}
table.dataTable tr.dt-hasChild td.dt-control:before {
  border-top: 10px solid rgba(0, 0, 0, 0.5);
  border-left: 5px solid transparent;
  border-bottom: 0px solid transparent;
  border-right: 5px solid transparent;
}
table.dataTable tfoot:empty {
  display: none;
}

html.dark table.dataTable td.dt-control:before,
:root[data-bs-theme=dark] table.dataTable td.dt-control:before,
:root[data-theme=dark] table.dataTable td.dt-control:before {
  border-left-color: rgba(255, 255, 255, 0.5);
}
html.dark table.dataTable tr.dt-hasChild td.dt-control:before,
:root[data-bs-theme=dark] table.dataTable tr.dt-hasChild td.dt-control:before,
:root[data-theme=dark] table.dataTable tr.dt-hasChild td.dt-control:before {
  border-top-color: rgba(255, 255, 255, 0.5);
  border-left-color: transparent;
}

div.dt-scroll {
  width: 100%;
}

div.dt-scroll-body thead tr,
div.dt-scroll-body tfoot tr {
  height: 0;
}
div.dt-scroll-body thead tr th, div.dt-scroll-body thead tr td,
div.dt-scroll-body tfoot tr th,
div.dt-scroll-body tfoot tr td {
  height: 0 !important;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  border-top-width: 0px !important;
  border-bottom-width: 0px !important;
}
div.dt-scroll-body thead tr th div.dt-scroll-sizing, div.dt-scroll-body thead tr td div.dt-scroll-sizing,
div.dt-scroll-body tfoot tr th div.dt-scroll-sizing,
div.dt-scroll-body tfoot tr td div.dt-scroll-sizing {
  height: 0 !important;
  overflow: hidden !important;
}

table.dataTable thead > tr > th:active,
table.dataTable thead > tr > td:active {
  outline: none;
}
table.dataTable thead > tr > th.dt-orderable-asc span.dt-column-order:before, table.dataTable thead > tr > th.dt-ordering-asc span.dt-column-order:before,
table.dataTable thead > tr > td.dt-orderable-asc span.dt-column-order:before,
table.dataTable thead > tr > td.dt-ordering-asc span.dt-column-order:before {
  position: absolute;
  display: block;
  bottom: 50%;
  content: "\25B2";
  content: "\25B2"/"";
}
table.dataTable thead > tr > th.dt-orderable-desc span.dt-column-order:after, table.dataTable thead > tr > th.dt-ordering-desc span.dt-column-order:after,
table.dataTable thead > tr > td.dt-orderable-desc span.dt-column-order:after,
table.dataTable thead > tr > td.dt-ordering-desc span.dt-column-order:after {
  position: absolute;
  display: block;
  top: 50%;
  content: "\25BC";
  content: "\25BC"/"";
}
table.dataTable thead > tr > th.dt-orderable-asc, table.dataTable thead > tr > th.dt-orderable-desc, table.dataTable thead > tr > th.dt-ordering-asc, table.dataTable thead > tr > th.dt-ordering-desc,
table.dataTable thead > tr > td.dt-orderable-asc,
table.dataTable thead > tr > td.dt-orderable-desc,
table.dataTable thead > tr > td.dt-ordering-asc,
table.dataTable thead > tr > td.dt-ordering-desc {
  position: relative;
  padding-right: 30px;
}
table.dataTable thead > tr > th.dt-orderable-asc span.dt-column-order, table.dataTable thead > tr > th.dt-orderable-desc span.dt-column-order, table.dataTable thead > tr > th.dt-ordering-asc span.dt-column-order, table.dataTable thead > tr > th.dt-ordering-desc span.dt-column-order,
table.dataTable thead > tr > td.dt-orderable-asc span.dt-column-order,
table.dataTable thead > tr > td.dt-orderable-desc span.dt-column-order,
table.dataTable thead > tr > td.dt-ordering-asc span.dt-column-order,
table.dataTable thead > tr > td.dt-ordering-desc span.dt-column-order {
  position: absolute;
  right: 12px;
  top: 0;
  bottom: 0;
  width: 12px;
}
table.dataTable thead > tr > th.dt-orderable-asc span.dt-column-order:before, table.dataTable thead > tr > th.dt-orderable-asc span.dt-column-order:after, table.dataTable thead > tr > th.dt-orderable-desc span.dt-column-order:before, table.dataTable thead > tr > th.dt-orderable-desc span.dt-column-order:after, table.dataTable thead > tr > th.dt-ordering-asc span.dt-column-order:before, table.dataTable thead > tr > th.dt-ordering-asc span.dt-column-order:after, table.dataTable thead > tr > th.dt-ordering-desc span.dt-column-order:before, table.dataTable thead > tr > th.dt-ordering-desc span.dt-column-order:after,
table.dataTable thead > tr > td.dt-orderable-asc span.dt-column-order:before,
table.dataTable thead > tr > td.dt-orderable-asc span.dt-column-order:after,
table.dataTable thead > tr > td.dt-orderable-desc span.dt-column-order:before,
table.dataTable thead > tr > td.dt-orderable-desc span.dt-column-order:after,
table.dataTable thead > tr > td.dt-ordering-asc span.dt-column-order:before,
table.dataTable thead > tr > td.dt-ordering-asc span.dt-column-order:after,
table.dataTable thead > tr > td.dt-ordering-desc span.dt-column-order:before,
table.dataTable thead > tr > td.dt-ordering-desc span.dt-column-order:after {
  left: 0;
  opacity: 0.125;
  line-height: 9px;
  font-size: 0.8em;
}
table.dataTable thead > tr > th.dt-orderable-asc, table.dataTable thead > tr > th.dt-orderable-desc,
table.dataTable thead > tr > td.dt-orderable-asc,
table.dataTable thead > tr > td.dt-orderable-desc {
  cursor: pointer;
}
table.dataTable thead > tr > th.dt-orderable-asc:hover, table.dataTable thead > tr > th.dt-orderable-desc:hover,
table.dataTable thead > tr > td.dt-orderable-asc:hover,
table.dataTable thead > tr > td.dt-orderable-desc:hover {
  outline: 2px solid rgba(0, 0, 0, 0.05);
  outline-offset: -2px;
}
table.dataTable thead > tr > th.dt-ordering-asc span.dt-column-order:before, table.dataTable thead > tr > th.dt-ordering-desc span.dt-column-order:after,
table.dataTable thead > tr > td.dt-ordering-asc span.dt-column-order:before,
table.dataTable thead > tr > td.dt-ordering-desc span.dt-column-order:after {
  opacity: 0.6;
}
table.dataTable thead > tr > th.sorting_desc_disabled span.dt-column-order:after, table.dataTable thead > tr > th.sorting_asc_disabled span.dt-column-order:before,
table.dataTable thead > tr > td.sorting_desc_disabled span.dt-column-order:after,
table.dataTable thead > tr > td.sorting_asc_disabled span.dt-column-order:before {
  display: none;
}
table.dataTable thead > tr > th:active,
table.dataTable thead > tr > td:active {
  outline: none;
}

div.dt-scroll-body > table.dataTable > thead > tr > th,
div.dt-scroll-body > table.dataTable > thead > tr > td {
  overflow: hidden;
}

:root.dark table.dataTable thead > tr > th.dt-orderable-asc:hover, :root.dark table.dataTable thead > tr > th.dt-orderable-desc:hover,
:root.dark table.dataTable thead > tr > td.dt-orderable-asc:hover,
:root.dark table.dataTable thead > tr > td.dt-orderable-desc:hover,
:root[data-bs-theme=dark] table.dataTable thead > tr > th.dt-orderable-asc:hover,
:root[data-bs-theme=dark] table.dataTable thead > tr > th.dt-orderable-desc:hover,
:root[data-bs-theme=dark] table.dataTable thead > tr > td.dt-orderable-asc:hover,
:root[data-bs-theme=dark] table.dataTable thead > tr > td.dt-orderable-desc:hover {
  outline: 2px solid rgba(255, 255, 255, 0.05);
}

div.dt-processing {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  margin-left: -100px;
  margin-top: -22px;
  text-align: center;
  padding: 2px;
  z-index: 10;
}
div.dt-processing > div:last-child {
  position: relative;
  width: 80px;
  height: 15px;
  margin: 1em auto;
}
div.dt-processing > div:last-child > div {
  position: absolute;
  top: 0;
  width: 13px;
  height: 13px;
  border-radius: 50%;
  background: rgb(13, 110, 253);
  background: rgb(var(--dt-row-selected));
  animation-timing-function: cubic-bezier(0, 1, 1, 0);
}
div.dt-processing > div:last-child > div:nth-child(1) {
  left: 8px;
  animation: datatables-loader-1 0.6s infinite;
}
div.dt-processing > div:last-child > div:nth-child(2) {
  left: 8px;
  animation: datatables-loader-2 0.6s infinite;
}
div.dt-processing > div:last-child > div:nth-child(3) {
  left: 32px;
  animation: datatables-loader-2 0.6s infinite;
}
div.dt-processing > div:last-child > div:nth-child(4) {
  left: 56px;
  animation: datatables-loader-3 0.6s infinite;
}

@keyframes datatables-loader-1 {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes datatables-loader-3 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0);
  }
}
@keyframes datatables-loader-2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(24px, 0);
  }
}
table.dataTable.nowrap th, table.dataTable.nowrap td {
  white-space: nowrap;
}
table.dataTable th,
table.dataTable td {
  box-sizing: border-box;
}
table.dataTable th.dt-type-numeric, table.dataTable th.dt-type-date,
table.dataTable td.dt-type-numeric,
table.dataTable td.dt-type-date {
  text-align: right;
}
table.dataTable th.dt-left,
table.dataTable td.dt-left {
  text-align: left;
}
table.dataTable th.dt-center,
table.dataTable td.dt-center {
  text-align: center;
}
table.dataTable th.dt-right,
table.dataTable td.dt-right {
  text-align: right;
}
table.dataTable th.dt-justify,
table.dataTable td.dt-justify {
  text-align: justify;
}
table.dataTable th.dt-nowrap,
table.dataTable td.dt-nowrap {
  white-space: nowrap;
}
table.dataTable th.dt-empty,
table.dataTable td.dt-empty {
  text-align: center;
  vertical-align: top;
}
table.dataTable thead th,
table.dataTable thead td,
table.dataTable tfoot th,
table.dataTable tfoot td {
  text-align: left;
}
table.dataTable thead th.dt-head-left,
table.dataTable thead td.dt-head-left,
table.dataTable tfoot th.dt-head-left,
table.dataTable tfoot td.dt-head-left {
  text-align: left;
}
table.dataTable thead th.dt-head-center,
table.dataTable thead td.dt-head-center,
table.dataTable tfoot th.dt-head-center,
table.dataTable tfoot td.dt-head-center {
  text-align: center;
}
table.dataTable thead th.dt-head-right,
table.dataTable thead td.dt-head-right,
table.dataTable tfoot th.dt-head-right,
table.dataTable tfoot td.dt-head-right {
  text-align: right;
}
table.dataTable thead th.dt-head-justify,
table.dataTable thead td.dt-head-justify,
table.dataTable tfoot th.dt-head-justify,
table.dataTable tfoot td.dt-head-justify {
  text-align: justify;
}
table.dataTable thead th.dt-head-nowrap,
table.dataTable thead td.dt-head-nowrap,
table.dataTable tfoot th.dt-head-nowrap,
table.dataTable tfoot td.dt-head-nowrap {
  white-space: nowrap;
}
table.dataTable tbody th.dt-body-left,
table.dataTable tbody td.dt-body-left {
  text-align: left;
}
table.dataTable tbody th.dt-body-center,
table.dataTable tbody td.dt-body-center {
  text-align: center;
}
table.dataTable tbody th.dt-body-right,
table.dataTable tbody td.dt-body-right {
  text-align: right;
}
table.dataTable tbody th.dt-body-justify,
table.dataTable tbody td.dt-body-justify {
  text-align: justify;
}
table.dataTable tbody th.dt-body-nowrap,
table.dataTable tbody td.dt-body-nowrap {
  white-space: nowrap;
}

:root {
  --dt-row-hover-alpha: 0.035;
  --dt-row-stripe-alpha: 0.023;
  --dt-column-ordering-alpha: 0.019;
  --dt-row-selected-stripe-alpha: 0.923;
  --dt-row-selected-column-ordering-alpha: 0.919;
}

/*
 * Table styles
 */
table.dataTable {
  width: 100%;
  margin: 0 auto;
  border-spacing: 0;
  /*
   * Header and footer styles
   */
  /*
   * Body styles
   */
}
table.dataTable thead th,
table.dataTable tfoot th {
  font-weight: bold;
}
table.dataTable > thead > tr > th,
table.dataTable > thead > tr > td {
  padding: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.3);
}
table.dataTable > thead > tr > th:active,
table.dataTable > thead > tr > td:active {
  outline: none;
}
table.dataTable > tfoot > tr > th,
table.dataTable > tfoot > tr > td {
  border-top: 1px solid rgba(0, 0, 0, 0.3);
  padding: 10px 10px 6px 10px;
}
table.dataTable > tbody > tr {
  background-color: transparent;
}
table.dataTable > tbody > tr:first-child > * {
  border-top: none;
}
table.dataTable > tbody > tr:last-child > * {
  border-bottom: none;
}
table.dataTable > tbody > tr.selected > * {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.9);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), 0.9);
  color: rgb(255, 255, 255);
  color: rgb(var(--dt-row-selected-text));
}
table.dataTable > tbody > tr.selected a {
  color: rgb(9, 10, 11);
  color: rgb(var(--dt-row-selected-link));
}
table.dataTable > tbody > tr > th,
table.dataTable > tbody > tr > td {
  padding: 8px 10px;
}
table.dataTable.row-border > tbody > tr > *, table.dataTable.display > tbody > tr > * {
  border-top: 1px solid rgba(0, 0, 0, 0.15);
}
table.dataTable.row-border > tbody > tr:first-child > *, table.dataTable.display > tbody > tr:first-child > * {
  border-top: none;
}
table.dataTable.row-border > tbody > tr.selected + tr.selected > td, table.dataTable.display > tbody > tr.selected + tr.selected > td {
  border-top-color: rgba(13, 110, 253, 0.65);
  border-top-color: rgba(var(--dt-row-selected), 0.65);
}
table.dataTable.cell-border > tbody > tr > * {
  border-top: 1px solid rgba(0, 0, 0, 0.15);
  border-right: 1px solid rgba(0, 0, 0, 0.15);
}
table.dataTable.cell-border > tbody > tr > *:first-child {
  border-left: 1px solid rgba(0, 0, 0, 0.15);
}
table.dataTable.cell-border > tbody > tr:first-child > * {
  border-top: 1px solid rgba(0, 0, 0, 0.3);
}
table.dataTable.stripe > tbody > tr:nth-child(odd) > *, table.dataTable.display > tbody > tr:nth-child(odd) > * {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.023);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-stripe), var(--dt-row-stripe-alpha));
}
table.dataTable.stripe > tbody > tr:nth-child(odd).selected > *, table.dataTable.display > tbody > tr:nth-child(odd).selected > * {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.923);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), var(--dt-row-selected-stripe-alpha));
}
table.dataTable.hover > tbody > tr:hover > *, table.dataTable.display > tbody > tr:hover > * {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.035);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-hover), var(--dt-row-hover-alpha));
}
table.dataTable.hover > tbody > tr.selected:hover > *, table.dataTable.display > tbody > tr.selected:hover > * {
  box-shadow: inset 0 0 0 9999px #0d6efd !important;
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), 1) !important;
}
table.dataTable.order-column > tbody tr > .sorting_1,
table.dataTable.order-column > tbody tr > .sorting_2,
table.dataTable.order-column > tbody tr > .sorting_3, table.dataTable.display > tbody tr > .sorting_1,
table.dataTable.display > tbody tr > .sorting_2,
table.dataTable.display > tbody tr > .sorting_3 {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.019);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-column-ordering), var(--dt-column-ordering-alpha));
}
table.dataTable.order-column > tbody tr.selected > .sorting_1,
table.dataTable.order-column > tbody tr.selected > .sorting_2,
table.dataTable.order-column > tbody tr.selected > .sorting_3, table.dataTable.display > tbody tr.selected > .sorting_1,
table.dataTable.display > tbody tr.selected > .sorting_2,
table.dataTable.display > tbody tr.selected > .sorting_3 {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.919);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), var(--dt-row-selected-column-ordering-alpha));
}
table.dataTable.display > tbody > tr:nth-child(odd) > .sorting_1, table.dataTable.order-column.stripe > tbody > tr:nth-child(odd) > .sorting_1 {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.054);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-column-ordering), calc(var(--dt-row-stripe-alpha) + var(--dt-column-ordering-alpha)));
}
table.dataTable.display > tbody > tr:nth-child(odd) > .sorting_2, table.dataTable.order-column.stripe > tbody > tr:nth-child(odd) > .sorting_2 {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.047);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-column-ordering), calc(var(--dt-row-stripe-alpha) + var(--dt-column-ordering-alpha) - 0.007));
}
table.dataTable.display > tbody > tr:nth-child(odd) > .sorting_3, table.dataTable.order-column.stripe > tbody > tr:nth-child(odd) > .sorting_3 {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.039);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-column-ordering), calc(var(--dt-row-stripe-alpha) + var(--dt-column-ordering-alpha) - 0.015));
}
table.dataTable.display > tbody > tr:nth-child(odd).selected > .sorting_1, table.dataTable.order-column.stripe > tbody > tr:nth-child(odd).selected > .sorting_1 {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.954);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), calc(var(--dt-row-selected-stripe-alpha) + var(--dt-column-ordering-alpha)));
}
table.dataTable.display > tbody > tr:nth-child(odd).selected > .sorting_2, table.dataTable.order-column.stripe > tbody > tr:nth-child(odd).selected > .sorting_2 {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.947);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), calc(var(--dt-row-selected-stripe-alpha) + var(--dt-column-ordering-alpha) - 0.007));
}
table.dataTable.display > tbody > tr:nth-child(odd).selected > .sorting_3, table.dataTable.order-column.stripe > tbody > tr:nth-child(odd).selected > .sorting_3 {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.939);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), calc(var(--dt-row-selected-stripe-alpha) + var(--dt-column-ordering-alpha) - 0.015));
}
table.dataTable.display tbody tr:hover > .sorting_1, table.dataTable.order-column.hover tbody tr:hover > .sorting_1 {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.082);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-hover), calc(var(--dt-row-stripe-alpha) + var(--dt-column-ordering-alpha) + var(--dt-row-hover-alpha)));
}
table.dataTable.display tbody tr:hover > .sorting_2, table.dataTable.order-column.hover tbody tr:hover > .sorting_2 {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.074);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-hover), calc(var(--dt-row-stripe-alpha) + var(--dt-column-ordering-alpha) + var(--dt-row-hover-alpha) - 0.007));
}
table.dataTable.display tbody tr:hover > .sorting_3, table.dataTable.order-column.hover tbody tr:hover > .sorting_3 {
  box-shadow: inset 0 0 0 9999px rgba(0, 0, 0, 0.062);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-hover), calc(var(--dt-row-stripe-alpha) + var(--dt-column-ordering-alpha) + var(--dt-row-hover-alpha) - 0.015));
}
table.dataTable.display tbody tr:hover.selected > .sorting_1, table.dataTable.order-column.hover tbody tr:hover.selected > .sorting_1 {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.982);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), calc(var(--dt-row-selected-stripe-alpha) + var(--dt-column-ordering-alpha)));
}
table.dataTable.display tbody tr:hover.selected > .sorting_2, table.dataTable.order-column.hover tbody tr:hover.selected > .sorting_2 {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.974);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), calc(var(--dt-row-selected-stripe-alpha) + var(--dt-column-ordering-alpha) + var(--dt-row-hover-alpha) - 0.007));
}
table.dataTable.display tbody tr:hover.selected > .sorting_3, table.dataTable.order-column.hover tbody tr:hover.selected > .sorting_3 {
  box-shadow: inset 0 0 0 9999px rgba(13, 110, 253, 0.962);
  box-shadow: inset 0 0 0 9999px rgba(var(--dt-row-selected), calc(var(--dt-row-selected-stripe-alpha) + var(--dt-column-ordering-alpha) + var(--dt-row-hover-alpha) - 0.015));
}
table.dataTable.compact thead th,
table.dataTable.compact thead td,
table.dataTable.compact tfoot th,
table.dataTable.compact tfoot td,
table.dataTable.compact tbody th,
table.dataTable.compact tbody td {
  padding: 4px;
}

div.dt-container div.dt-layout-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0.75em 0;
}
div.dt-container div.dt-layout-row div.dt-layout-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
div.dt-container div.dt-layout-row div.dt-layout-cell.dt-layout-start {
  justify-content: flex-start;
  margin-right: auto;
}
div.dt-container div.dt-layout-row div.dt-layout-cell.dt-layout-end {
  justify-content: flex-end;
  margin-left: auto;
}
div.dt-container div.dt-layout-row div.dt-layout-cell:empty {
  display: none;
}

@media screen and (max-width: 767px) {
  div.dt-container div.dt-layout-row:not(.dt-layout-table) {
    display: block;
  }
  div.dt-container div.dt-layout-row:not(.dt-layout-table) div.dt-layout-cell {
    display: block;
    text-align: center;
  }
  div.dt-container div.dt-layout-row:not(.dt-layout-table) div.dt-layout-cell > * {
    margin: 0.5em 0;
  }
  div.dt-container div.dt-layout-row:not(.dt-layout-table) div.dt-layout-cell.dt-layout-start {
    margin-right: 0;
  }
  div.dt-container div.dt-layout-row:not(.dt-layout-table) div.dt-layout-cell.dt-layout-end {
    margin-left: 0;
  }
}
div.dt-container div.dt-layout-start > *:not(:last-child) {
  margin-right: 1em;
}
div.dt-container div.dt-layout-end > *:not(:first-child) {
  margin-left: 1em;
}
div.dt-container div.dt-layout-full {
  width: 100%;
}
div.dt-container div.dt-layout-full > *:only-child {
  margin-left: auto;
  margin-right: auto;
}
div.dt-container div.dt-layout-table > div {
  display: block !important;
}

@media screen and (max-width: 767px) {
  div.dt-container div.dt-layout-start > *:not(:last-child) {
    margin-right: 0;
  }
  div.dt-container div.dt-layout-end > *:not(:first-child) {
    margin-left: 0;
  }
}
/*
 * Control feature layout
 */
div.dt-container {
  position: relative;
  clear: both;
}
div.dt-container .dt-search input {
  border: 1px solid #aaa;
  border-radius: 3px;
  padding: 5px;
  background-color: transparent;
  color: inherit;
  margin-left: 3px;
}
div.dt-container .dt-input {
  border: 1px solid #aaa;
  border-radius: 3px;
  padding: 5px;
  background-color: transparent;
  color: inherit;
}
div.dt-container select.dt-input {
  padding: 4px;
}
div.dt-container .dt-paging .dt-paging-button {
  box-sizing: border-box;
  display: inline-block;
  min-width: 1.5em;
  padding: 0.5em 1em;
  margin-left: 2px;
  text-align: center;
  text-decoration: none !important;
  cursor: pointer;
  color: inherit !important;
  border: 1px solid transparent;
  border-radius: 2px;
  background: transparent;
}
div.dt-container .dt-paging .dt-paging-button.current, div.dt-container .dt-paging .dt-paging-button.current:hover {
  color: inherit !important;
  border: 1px solid rgba(0, 0, 0, 0.3);
  background-color: rgba(0, 0, 0, 0.05);
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(230, 230, 230, 0.05)), color-stop(100%, rgba(0, 0, 0, 0.05))); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top, rgba(230, 230, 230, 0.05) 0%, rgba(0, 0, 0, 0.05) 100%); /* Chrome10+,Safari5.1+ */
  background: -moz-linear-gradient(top, rgba(230, 230, 230, 0.05) 0%, rgba(0, 0, 0, 0.05) 100%); /* FF3.6+ */
  background: -ms-linear-gradient(top, rgba(230, 230, 230, 0.05) 0%, rgba(0, 0, 0, 0.05) 100%); /* IE10+ */
  background: -o-linear-gradient(top, rgba(230, 230, 230, 0.05) 0%, rgba(0, 0, 0, 0.05) 100%); /* Opera 11.10+ */
  background: linear-gradient(to bottom, rgba(230, 230, 230, 0.05) 0%, rgba(0, 0, 0, 0.05) 100%); /* W3C */
}
div.dt-container .dt-paging .dt-paging-button.disabled, div.dt-container .dt-paging .dt-paging-button.disabled:hover, div.dt-container .dt-paging .dt-paging-button.disabled:active {
  cursor: default;
  color: rgba(0, 0, 0, 0.5) !important;
  border: 1px solid transparent;
  background: transparent;
  box-shadow: none;
}
div.dt-container .dt-paging .dt-paging-button:hover {
  color: white !important;
  border: 1px solid #111;
  background-color: #111;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #585858), color-stop(100%, #111)); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top, #585858 0%, #111 100%); /* Chrome10+,Safari5.1+ */
  background: -moz-linear-gradient(top, #585858 0%, #111 100%); /* FF3.6+ */
  background: -ms-linear-gradient(top, #585858 0%, #111 100%); /* IE10+ */
  background: -o-linear-gradient(top, #585858 0%, #111 100%); /* Opera 11.10+ */
  background: linear-gradient(to bottom, #585858 0%, #111 100%); /* W3C */
}
div.dt-container .dt-paging .dt-paging-button:active {
  outline: none;
  background-color: #0c0c0c;
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #2b2b2b), color-stop(100%, #0c0c0c)); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top, #2b2b2b 0%, #0c0c0c 100%); /* Chrome10+,Safari5.1+ */
  background: -moz-linear-gradient(top, #2b2b2b 0%, #0c0c0c 100%); /* FF3.6+ */
  background: -ms-linear-gradient(top, #2b2b2b 0%, #0c0c0c 100%); /* IE10+ */
  background: -o-linear-gradient(top, #2b2b2b 0%, #0c0c0c 100%); /* Opera 11.10+ */
  background: linear-gradient(to bottom, #2b2b2b 0%, #0c0c0c 100%); /* W3C */
  box-shadow: inset 0 0 3px #111;
}
div.dt-container .dt-paging .ellipsis {
  padding: 0 1em;
}
div.dt-container .dt-length,
div.dt-container .dt-search,
div.dt-container .dt-info,
div.dt-container .dt-processing,
div.dt-container .dt-paging {
  color: inherit;
}
div.dt-container .dataTables_scroll {
  clear: both;
}
div.dt-container .dataTables_scroll div.dt-scroll-body {
  -webkit-overflow-scrolling: touch;
}
div.dt-container .dataTables_scroll div.dt-scroll-body > table > thead > tr > th, div.dt-container .dataTables_scroll div.dt-scroll-body > table > thead > tr > td, div.dt-container .dataTables_scroll div.dt-scroll-body > table > tbody > tr > th, div.dt-container .dataTables_scroll div.dt-scroll-body > table > tbody > tr > td {
  vertical-align: middle;
}
div.dt-container .dataTables_scroll div.dt-scroll-body > table > thead > tr > th > div.dataTables_sizing,
div.dt-container .dataTables_scroll div.dt-scroll-body > table > thead > tr > td > div.dataTables_sizing, div.dt-container .dataTables_scroll div.dt-scroll-body > table > tbody > tr > th > div.dataTables_sizing,
div.dt-container .dataTables_scroll div.dt-scroll-body > table > tbody > tr > td > div.dataTables_sizing {
  height: 0;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
}
div.dt-container.dt-empty-footer tbody > tr:last-child > * {
  border-bottom: 1px solid rgba(0, 0, 0, 0.3);
}
div.dt-container.dt-empty-footer .dt-scroll-body {
  border-bottom: 1px solid rgba(0, 0, 0, 0.3);
}
div.dt-container.dt-empty-footer .dt-scroll-body tbody > tr:last-child > * {
  border-bottom: none;
}

html.dark {
  --dt-row-hover: 255, 255, 255;
  --dt-row-stripe: 255, 255, 255;
  --dt-column-ordering: 255, 255, 255;
}
html.dark table.dataTable > thead > tr > th,
html.dark table.dataTable > thead > tr > td {
  border-bottom: 1px solid rgb(89, 91, 94);
}
html.dark table.dataTable > thead > tr > th:active,
html.dark table.dataTable > thead > tr > td:active {
  outline: none;
}
html.dark table.dataTable > tfoot > tr > th,
html.dark table.dataTable > tfoot > tr > td {
  border-top: 1px solid rgb(89, 91, 94);
}
html.dark table.dataTable.row-border > tbody > tr > *, html.dark table.dataTable.display > tbody > tr > * {
  border-top: 1px solid rgb(64, 67, 70);
}
html.dark table.dataTable.row-border > tbody > tr:first-child > *, html.dark table.dataTable.display > tbody > tr:first-child > * {
  border-top: none;
}
html.dark table.dataTable.row-border > tbody > tr.selected + tr.selected > td, html.dark table.dataTable.display > tbody > tr.selected + tr.selected > td {
  border-top-color: rgba(13, 110, 253, 0.65);
  border-top-color: rgba(var(--dt-row-selected), 0.65);
}
html.dark table.dataTable.cell-border > tbody > tr > th,
html.dark table.dataTable.cell-border > tbody > tr > td {
  border-top: 1px solid rgb(64, 67, 70);
  border-right: 1px solid rgb(64, 67, 70);
}
html.dark table.dataTable.cell-border > tbody > tr > th:first-child,
html.dark table.dataTable.cell-border > tbody > tr > td:first-child {
  border-left: 1px solid rgb(64, 67, 70);
}
html.dark .dt-container.dt-empty-footer table.dataTable {
  border-bottom: 1px solid rgb(89, 91, 94);
}
html.dark .dt-container .dt-search input,
html.dark .dt-container .dt-length select {
  border: 1px solid rgba(255, 255, 255, 0.2);
  background-color: var(--dt-html-background);
}
html.dark .dt-container .dt-paging .dt-paging-button.current, html.dark .dt-container .dt-paging .dt-paging-button.current:hover {
  border: 1px solid rgb(89, 91, 94);
  background: rgba(255, 255, 255, 0.15);
}
html.dark .dt-container .dt-paging .dt-paging-button.disabled, html.dark .dt-container .dt-paging .dt-paging-button.disabled:hover, html.dark .dt-container .dt-paging .dt-paging-button.disabled:active {
  color: #666 !important;
}
html.dark .dt-container .dt-paging .dt-paging-button:hover {
  border: 1px solid rgb(53, 53, 53);
  background: rgb(53, 53, 53);
}
html.dark .dt-container .dt-paging .dt-paging-button:active {
  background: #3a3a3a;
}

/*
 * Overrides for RTL support
 */
*[dir=rtl] table.dataTable thead th,
*[dir=rtl] table.dataTable thead td,
*[dir=rtl] table.dataTable tfoot th,
*[dir=rtl] table.dataTable tfoot td {
  text-align: right;
}
*[dir=rtl] table.dataTable th.dt-type-numeric, *[dir=rtl] table.dataTable th.dt-type-date,
*[dir=rtl] table.dataTable td.dt-type-numeric,
*[dir=rtl] table.dataTable td.dt-type-date {
  text-align: left;
}
*[dir=rtl] div.dt-container div.dt-layout-cell.dt-start {
  text-align: right;
}
*[dir=rtl] div.dt-container div.dt-layout-cell.dt-end {
  text-align: left;
}
*[dir=rtl] div.dt-container div.dt-search input {
  margin: 0 3px 0 0;
}


@keyframes dtb-spinner {
  100% {
    transform: rotate(360deg);
  }
}
@-o-keyframes dtb-spinner {
  100% {
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-ms-keyframes dtb-spinner {
  100% {
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes dtb-spinner {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes dtb-spinner {
  100% {
    -moz-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
div.dataTables_wrapper {
  position: relative;
}

div.dt-buttons {
  position: initial;
}
div.dt-buttons .dt-button {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

div.dt-button-info {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 400px;
  margin-top: -100px;
  margin-left: -200px;
  background-color: white;
  border-radius: 0.75em;
  box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.8);
  text-align: center;
  z-index: 2003;
  overflow: hidden;
}
div.dt-button-info h2 {
  padding: 2rem 2rem 1rem 2rem;
  margin: 0;
  font-weight: normal;
}
div.dt-button-info > div {
  padding: 1em 2em 2em 2em;
}

div.dtb-popover-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 22px;
  height: 22px;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 2003;
}

button.dtb-hide-drop {
  display: none !important;
}

div.dt-button-collection-title {
  text-align: center;
  padding: 0.3em 0.5em 0.5em;
  margin-left: 0.5em;
  margin-right: 0.5em;
  font-size: 0.9em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

div.dt-button-collection-title:empty {
  display: none;
}

span.dt-button-spacer {
  display: inline-block;
  margin: 0.5em;
  white-space: nowrap;
}
span.dt-button-spacer.bar {
  border-left: 1px solid rgba(0, 0, 0, 0.3);
  vertical-align: middle;
  padding-left: 0.5em;
}
span.dt-button-spacer.bar:empty {
  height: 1em;
  width: 1px;
  padding-left: 0;
}

div.dt-button-collection .dt-button-active {
  padding-right: 3em;
}
div.dt-button-collection .dt-button-active:after {
  position: absolute;
  top: 50%;
  margin-top: -10px;
  right: 1em;
  display: inline-block;
  content: "✓";
  color: inherit;
}
div.dt-button-collection .dt-button-active.dt-button-split {
  padding-right: 0;
}
div.dt-button-collection .dt-button-active.dt-button-split:after {
  display: none;
}
div.dt-button-collection .dt-button-active.dt-button-split > *:first-child {
  padding-right: 3em;
}
div.dt-button-collection .dt-button-active.dt-button-split > *:first-child:after {
  position: absolute;
  top: 50%;
  margin-top: -10px;
  right: 1em;
  display: inline-block;
  content: "✓";
  color: inherit;
}
div.dt-button-collection .dt-button-active-a a {
  padding-right: 3em;
}
div.dt-button-collection .dt-button-active-a a:after {
  position: absolute;
  right: 1em;
  display: inline-block;
  content: "✓";
  color: inherit;
}
div.dt-button-collection span.dt-button-spacer {
  width: 100%;
  font-size: 0.9em;
  text-align: center;
  margin: 0.5em 0;
}
div.dt-button-collection span.dt-button-spacer:empty {
  height: 0;
  width: 100%;
}
div.dt-button-collection span.dt-button-spacer.bar {
  border-left: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-left: 0;
}

@media print {
  table.dataTable tr > * {
    box-shadow: none !important;
  }
}
div.dt-buttons > .dt-button,
div.dt-buttons > div.dt-button-split .dt-button {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  margin-left: 0.167em;
  margin-right: 0.167em;
  margin-bottom: 0.333em;
  padding: 0.5em 1em;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 2px;
  cursor: pointer;
  font-size: 0.88em;
  line-height: 1.6em;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.1); /* Fallback */
  background: linear-gradient(to bottom, rgba(230, 230, 230, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,StartColorStr="rgba(230, 230, 230, 0.1)", EndColorStr="rgba(0, 0, 0, 0.1)");
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  text-decoration: none;
  outline: none;
  text-overflow: ellipsis;
}
div.dt-buttons > .dt-button:first-child,
div.dt-buttons > div.dt-button-split .dt-button:first-child {
  margin-left: 0;
}
div.dt-buttons > .dt-button.disabled,
div.dt-buttons > div.dt-button-split .dt-button.disabled {
  cursor: default;
  opacity: 0.4;
}
div.dt-buttons > .dt-button.dt-button-active:not(.disabled),
div.dt-buttons > div.dt-button-split .dt-button.dt-button-active:not(.disabled) {
  background-color: rgba(0, 0, 0, 0.1); /* Fallback */
  background: linear-gradient(to bottom, rgba(179, 179, 179, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,StartColorStr="rgba(179, 179, 179, 0.1)", EndColorStr="rgba(0, 0, 0, 0.1)");
  box-shadow: inset 1px 1px 3px #999999;
}
div.dt-buttons > .dt-button.dt-button-active:not(.disabled):hover:not(.disabled),
div.dt-buttons > div.dt-button-split .dt-button.dt-button-active:not(.disabled):hover:not(.disabled) {
  box-shadow: inset 1px 1px 3px #999999;
  background-color: rgba(0, 0, 0, 0.1); /* Fallback */
  background: linear-gradient(to bottom, rgba(128, 128, 128, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,StartColorStr="rgba(128, 128, 128, 0.1)", EndColorStr="rgba(0, 0, 0, 0.1)");
}
div.dt-buttons > .dt-button:hover,
div.dt-buttons > div.dt-button-split .dt-button:hover {
  text-decoration: none;
}
div.dt-buttons > .dt-button:hover:not(.disabled),
div.dt-buttons > div.dt-button-split .dt-button:hover:not(.disabled) {
  border: 1px solid #666;
  background-color: rgba(0, 0, 0, 0.1); /* Fallback */
  background: linear-gradient(to bottom, rgba(153, 153, 153, 0.1) 0%, rgba(0, 0, 0, 0.1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,StartColorStr="rgba(153, 153, 153, 0.1)", EndColorStr="rgba(0, 0, 0, 0.1)");
}
div.dt-buttons > .dt-button:focus:not(.disabled),
div.dt-buttons > div.dt-button-split .dt-button:focus:not(.disabled) {
  outline: 2px solid rgb(53, 132, 228);
}
div.dt-buttons > .dt-button embed,
div.dt-buttons > div.dt-button-split .dt-button embed {
  outline: none;
}
div.dt-buttons > div.dt-button-split .dt-button:first-child {
  border-right: 1px solid rgba(0, 0, 0, 0.15);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
div.dt-buttons > div.dt-button-split .dt-button:first-child:hover {
  border-right: 1px solid #666;
}
div.dt-buttons > div.dt-button-split .dt-button:last-child {
  border-left: 1px solid transparent;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
div.dt-buttons > div.dt-button-split .dt-button:last-child:hover {
  border-left: 1px solid #666;
}
div.dt-buttons span.dt-button-down-arrow {
  position: relative;
  top: -2px;
  font-size: 10px;
  padding-left: 10px;
  line-height: 1em;
  opacity: 0.6;
}
div.dt-buttons div.dt-button-split {
  display: inline-block;
}
div.dt-buttons div.dt-button-split .dt-button:first-child {
  margin-right: 0;
}
div.dt-buttons div.dt-button-split .dt-button:last-child {
  margin-left: -1px;
  padding-left: 0.75em;
  padding-right: 0.75em;
  z-index: 2;
}
div.dt-buttons div.dt-button-split .dt-button:last-child span {
  padding-left: 0;
}

div.dt-button-collection {
  position: absolute;
  top: 0;
  left: 0;
  width: 200px;
  margin-top: 3px;
  margin-bottom: 3px;
  padding: 0.75em 0;
  border: 1px solid rgba(0, 0, 0, 0.4);
  background-color: white;
  overflow: hidden;
  z-index: 2002;
  border-radius: 5px;
  box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
}
div.dt-button-collection .dt-button {
  position: relative;
  left: 0;
  right: 0;
  width: 100%;
  display: flex;
  float: none;
  justify-content: space-between;
  align-items: center;
  background: none;
  margin: 0;
  padding: 0.5em 1em;
  border: none;
  text-align: left;
  cursor: pointer;
  color: inherit;
}
div.dt-button-collection .dt-button.dt-button-active {
  background: none;
  box-shadow: none;
}
div.dt-button-collection .dt-button.disabled {
  cursor: default;
  opacity: 0.4;
}
div.dt-button-collection .dt-button:hover:not(.disabled) {
  border: none;
  background: rgba(153, 153, 153, 0.1);
  box-shadow: none;
}
div.dt-button-collection div.dt-button-split {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: stretch;
}
div.dt-button-collection div.dt-button-split button.dt-button {
  margin: 0;
  display: inline-block;
  width: 0;
  flex-grow: 1;
  flex-shrink: 0;
  flex-basis: 50px;
}
div.dt-button-collection div.dt-button-split button.dt-button-split-drop {
  min-width: 33px;
  flex: 0;
}
div.dt-button-collection.fixed .dt-button {
  border-radius: 0.25em;
  background: rgba(255, 255, 255, 0.1);
}
div.dt-button-collection.fixed {
  position: fixed;
  display: block;
  top: 50%;
  left: 50%;
  margin-left: -75px;
  border-radius: 5px;
  background-color: white;
  padding: 0.5em;
}
div.dt-button-collection.fixed.two-column {
  margin-left: -200px;
}
div.dt-button-collection.fixed.three-column {
  margin-left: -225px;
}
div.dt-button-collection.fixed.four-column {
  margin-left: -300px;
}
div.dt-button-collection.fixed.columns {
  margin-left: -409px;
}
@media screen and (max-width: 1024px) {
  div.dt-button-collection.fixed.columns {
    margin-left: -308px;
  }
}
@media screen and (max-width: 640px) {
  div.dt-button-collection.fixed.columns {
    margin-left: -203px;
  }
}
@media screen and (max-width: 460px) {
  div.dt-button-collection.fixed.columns {
    margin-left: -100px;
  }
}
div.dt-button-collection.fixed > :last-child {
  max-height: 100vh;
  overflow: auto;
}
div.dt-button-collection.two-column > :last-child, div.dt-button-collection.three-column > :last-child, div.dt-button-collection.four-column > :last-child {
  display: block !important;
  column-gap: 8px;
}
div.dt-button-collection.two-column > :last-child > *, div.dt-button-collection.three-column > :last-child > *, div.dt-button-collection.four-column > :last-child > * {
  -webkit-column-break-inside: avoid;
  break-inside: avoid;
}
div.dt-button-collection.two-column {
  width: 400px;
}
div.dt-button-collection.two-column > :last-child {
  padding-bottom: 1px;
  column-count: 2;
}
div.dt-button-collection.three-column {
  width: 450px;
}
div.dt-button-collection.three-column > :last-child {
  padding-bottom: 1px;
  column-count: 3;
}
div.dt-button-collection.four-column {
  width: 600px;
}
div.dt-button-collection.four-column > :last-child {
  padding-bottom: 1px;
  column-count: 4;
}
div.dt-button-collection .dt-button {
  border-radius: 0;
}
div.dt-button-collection.columns {
  width: auto;
}
div.dt-button-collection.columns > :last-child {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  gap: 6px;
  width: 818px;
  padding-bottom: 1px;
}
div.dt-button-collection.columns > :last-child .dt-button {
  min-width: 200px;
  flex: 0 1;
  margin: 0;
}
div.dt-button-collection.columns.dtb-b3 > :last-child, div.dt-button-collection.columns.dtb-b2 > :last-child, div.dt-button-collection.columns.dtb-b1 > :last-child {
  justify-content: space-between;
}
div.dt-button-collection.columns.dtb-b3 .dt-button {
  flex: 1 1 32%;
}
div.dt-button-collection.columns.dtb-b2 .dt-button {
  flex: 1 1 48%;
}
div.dt-button-collection.columns.dtb-b1 .dt-button {
  flex: 1 1 100%;
}
@media screen and (max-width: 1024px) {
  div.dt-button-collection.columns > :last-child {
    width: 612px;
  }
}
@media screen and (max-width: 640px) {
  div.dt-button-collection.columns > :last-child {
    width: 406px;
  }
  div.dt-button-collection.columns.dtb-b3 .dt-button {
    flex: 0 1 32%;
  }
}
@media screen and (max-width: 460px) {
  div.dt-button-collection.columns > :last-child {
    width: 200px;
  }
}

div.dt-button-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7); /* Fallback */
  background: radial-gradient(ellipse farthest-corner at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%); /* W3C Markup, IE10 Release Preview */
  z-index: 2001;
}

.dt-button.processing {
  color: rgba(0, 0, 0, 0.2);
}
.dt-button.processing:after {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  box-sizing: border-box;
  display: block;
  content: " ";
  border: 2px solid rgb(40, 40, 40);
  border-radius: 50%;
  border-left-color: transparent;
  border-right-color: transparent;
  animation: dtb-spinner 1500ms infinite linear;
  -o-animation: dtb-spinner 1500ms infinite linear;
  -ms-animation: dtb-spinner 1500ms infinite linear;
  -webkit-animation: dtb-spinner 1500ms infinite linear;
  -moz-animation: dtb-spinner 1500ms infinite linear;
}

@media screen and (max-width: 640px) {
  div.dt-buttons {
    float: none !important;
    text-align: center;
  }
}
html.dark div.dt-buttons > .dt-button,
html.dark div.dt-buttons > div.dt-button-split .dt-button {
  border: 1px solid rgb(89, 91, 94);
  background: rgba(255, 255, 255, 0.15);
}
html.dark div.dt-buttons > .dt-button.dt-button-active:not(.disabled),
html.dark div.dt-buttons > div.dt-button-split .dt-button.dt-button-active:not(.disabled) {
  background: rgba(179, 179, 179, 0.15);
  box-shadow: inset 1px 1px 2px black;
}
html.dark div.dt-buttons > .dt-button.dt-button-active:not(.disabled):hover:not(.disabled),
html.dark div.dt-buttons > div.dt-button-split .dt-button.dt-button-active:not(.disabled):hover:not(.disabled) {
  background: rgba(128, 128, 128, 0.15);
  box-shadow: inset 1px 1px 3px black;
}
html.dark div.dt-buttons > .dt-button:hover:not(.disabled),
html.dark div.dt-buttons > div.dt-button-split .dt-button:hover:not(.disabled) {
  background: rgba(179, 179, 179, 0.15);
}
html.dark div.dt-buttons > .dt-button:focus:not(.disabled),
html.dark div.dt-buttons > div.dt-button-split .dt-button:focus:not(.disabled) {
  outline: 2px solid rgb(110, 168, 254);
}
html.dark div.dt-buttons > div.dt-button-split .dt-button:first-child {
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}
html.dark div.dt-buttons > div.dt-button-split .dt-button:first-child:hover {
  border-right: 1px solid rgb(89, 91, 94);
}
html.dark div.dt-buttons > div.dt-button-split .dt-button:last-child:hover {
  border-left: 1px solid rgb(89, 91, 94);
}
html.dark div.dt-button-collection {
  border: 1px solid rgba(255, 255, 255, 0.15);
  background-color: rgb(33, 37, 41);
  box-shadow: 3px 4px 10px 1px rgba(0, 0, 0, 0.8);
}
html.dark div.dt-button-info {
  background-color: var(--dt-html-background);
  border: 1px solid rgba(255, 255, 255, 0.15);
}


