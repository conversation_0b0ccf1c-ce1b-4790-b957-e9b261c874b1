<?php
// 禁用错误显示到输出
ini_set('display_errors', 0);
error_reporting(E_ALL);
// 设置错误日志
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';

try {
    // 获取表单数据
    $section = $_POST['section'] ?? '';        
    $project = $_POST['project'] ?? '';
    $line = $_POST['line'] ?? '';
    $unit = $_POST['unit'] ?? '';
    $category = $_POST['category'] ?? '';
    $direction = $_POST['direction'] ?? '';
    $datetime = $_POST['datetime'] ?? '';
    $closetime = $_POST['closetime'] ?? '';
    $status = $_POST['status'] ?? '';
    $responsible = $_POST['responsible'] ?? '';
    $recorder = $_POST['recorder'] ?? '';
    $keyword = $_POST['keyword'] ?? '';
    $issue = $_POST['issue'] ?? '';
    $phenomenon = $_POST['phenomenon'] ?? '';
    $analysis = $_POST['analysis'] ?? '';
    $measures = $_POST['measures'] ?? '';
    $relatedParts = $_POST['relatedParts'] ?? '';
    $model = $_POST['model'] ?? '';

    // 准备SQL语句
    $sql = "INSERT INTO alarmlist (
        section, line, unit, category, direction, 
        datetime, closetime, status, responsible, recorder, keyword,
        issue, phenomenon, analysis, measures, related_parts, model
    ) VALUES (
        ?, ?, ?, ?, ?, 
        ?, ?, ?, ?, ?, ?,
        ?, ?, ?, ?, ?, ?
    )";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sssssssssssssssss',
        $section, $line, $unit, $category, $direction,
        $datetime, $closetime, $status, $responsible, $recorder, $keyword,
        $issue, $phenomenon, $analysis, $measures, $relatedParts, $model
    );

    // 执行插入
    if (!$stmt->execute()) {
        throw new Exception('保存故障记录失败: ' . $stmt->error);
    }

    $fault_id = $stmt->insert_id;

    // 处理文件上传
    if (!empty($_FILES['files'])) {
        $uploadDir = '../uploads/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        foreach ($_FILES['files']['tmp_name'] as $key => $tmp_name) {
            if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
                $fileName = $_FILES['files']['name'][$key];
                $fileType = $_FILES['files']['type'][$key];
                $fileSize = $_FILES['files']['size'][$key];
                
                // 生成唯一文件名
                $uniqueName = uniqid() . '_' . $fileName;
                $filePath = $uploadDir . $uniqueName;
                
                if (move_uploaded_file($tmp_name, $filePath)) {
                    // 保存文件信息到数据库
                    $sql = "INSERT INTO fault_files (fault_id, file_name, file_path, file_type, file_size) 
                           VALUES (?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('isssi', $fault_id, $fileName, $uniqueName, $fileType, $fileSize);
                    $stmt->execute();
                }
            }
        }
    }

    echo json_encode([
        'success' => true,
        'message' => '故障信息已成功添加'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 