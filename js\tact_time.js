document.addEventListener('DOMContentLoaded', function() {
    loadLineOptions();
    loadDynamicTable();

    // 添加事件监听器
    document.getElementById('lineSelect').addEventListener('change', handleLineChange);
    document.getElementById('unitSelect').addEventListener('change', loadData);
    // document.getElementById('dateSelect').addEventListener('change', loadData);

    const resetButton = document.getElementById('resetbtn');
    if (resetButton) {
        resetButton.addEventListener('click', resetFiltersAndData);
    }
});

// 刷新数据函数
function refreshData() {
    const container = document.getElementById('dynamicTableContainer');
    container.innerHTML = `
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); ">
            <h2>数据加载中...</h2>
        </div>
    `;
    // 重新加载动态表格数据
    loadDynamicTable();
    // const date = document.getElementById('dateSelect').value;
    // const line = document.getElementById('lineSelect').value;
    // const unit = document.getElementById('unitSelect').value;
    // if (date && line && unit) {
    //     loadData();
    // }
}

function handleLineChange() {
    const selectedLine = document.getElementById('lineSelect').value;
    const unitSelect = document.getElementById('unitSelect');

    unitSelect.innerHTML = '<option value="">请选择UNIT</option>';
    document.getElementById('tableBody').innerHTML = '';
    document.getElementById('chartContainer').innerHTML = '';

    if (selectedLine) {
        loadUnitOptions(selectedLine);
    } else {
        unitSelect.disabled = true;
    }
}

function loadLineOptions() {
    fetch('http://**************:7070/equipment_management/php/get_tt_options.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.lines) {
                const lineSelect = document.getElementById('lineSelect');
                lineSelect.innerHTML = '<option value="">请选择LINE</option>';

                // 填充LINE选项
                data.lines.forEach(line => {
                    const option = document.createElement('option');
                    option.value = line;
                    option.textContent = line;
                    lineSelect.appendChild(option);
                });

                // 初始状态下禁用UNIT选择框
                const unitSelect = document.getElementById('unitSelect');
                unitSelect.innerHTML = '<option value="">请先选择LINE</option>';
                // unitSelect.disabled = true;
            }
        })
        .catch(error => console.error('Error loading lines:', error));
}

function loadUnitOptions(selectedLine) {
    fetch(`http://**************:7070/equipment_management/php/get_tt_options.php?line=${selectedLine}`)
        .then(response => response.json())
        .then(data => {
            const unitSelect = document.getElementById('unitSelect');
            unitSelect.innerHTML = '<option value="">请选择UNIT</option>';

            if (data.success && data.units) {
                // 填充UNIT选项
                data.units.forEach(unit => {
                    const option = document.createElement('option');
                    option.value = unit;
                    option.textContent = unit;
                    unitSelect.appendChild(option);
                });
                unitSelect.disabled = false;
            } else {
                console.error('Error loading units or no units found:', data.message);
                unitSelect.disabled = true;
            }
        })
        .catch(error => {
            console.error('Error fetching units:', error);
            const unitSelect = document.getElementById('unitSelect');
            unitSelect.innerHTML = '<option value="">加载失败</option>';
            unitSelect.disabled = true;
        });
}

function loadDynamicTable() {
    fetch('http://**************:7070/equipment_management/php/get_tt_table.php')
        .then(response => response.json())
        .then(response => {
            if (response.success) {
                displayDynamicTable(response.data);
            }
        })
        .catch(error => console.error('Error:', error));
}

function loadData() {
    const date = document.getElementById('dateSelect').value;
    const line = document.getElementById('lineSelect').value;
    const unit = document.getElementById('unitSelect').value;
    
    const params = new URLSearchParams();
    if (date) params.append('date', date);
    if (line) params.append('line', line);
    if (unit) params.append('unit', unit);
    params.append('period', 'HH');
    params.append('module', 'Module_OFF');
    
    fetch('http://**************:7070/equipment_management/php/get_tt.php?' + params.toString())
        .then(response => response.json())
        .then(response => {
            if (response.success) {
                displayTableData(response.data);
                displayChart(response.data);
            }
        })
        .catch(error => console.error('Error:', error));
}

function displayTableData(data) {
    const tbody = document.getElementById('tableBody');
    tbody.innerHTML = '';
    
    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.LINE}</td>
            <td>${row.UNITS}</td>
            <td>${row.UNIT}</td>
            <td>${row.PRODUCT}</td>
            <td>${row.DATE}</td>
            <td>${row.HH}</td>
            <td>${row.TT}</td>
            <td>${row.PERIOD}</td>
            <td>${row.MODULE}</td>
        `;
        tbody.appendChild(tr);
    });
}

function displayChart(data) {
    const chartDom = document.getElementById('chartContainer');
    if (chartDom) {
        const existingChart = echarts.getInstanceByDom(chartDom);
        if (existingChart) {
            existingChart.dispose(); // 销毁现有实例
        }
    }     
    const myChart = echarts.init(chartDom);
    
    const hours = data.map(item => item.HH);
    const tt = data.map(item => parseFloat(item.TT));
    
    const option = {
        title: {
            text: 'TactTime趋势图'
        },
        tooltip: {
            trigger: 'axis'
        },
        xAxis: {
            type: 'category',
            data: hours,
            name: '小时'
        },
        yAxis: {
            type: 'value',
            name: 'TT'
        },
        series: [{
            data: tt,
            type: 'line',
            smooth: true
        }]
    };
    
    myChart.setOption(option);
}

function displayDynamicTable(data) {
    const container = document.getElementById('dynamicTableContainer');
    container.innerHTML = ''; 
    const groupedData = {};
    const specialLines = ["1S05", "1S09", "1S10", "1S23", "1S29"]; // 定义特殊线路

    data.forEach(lineData => {
        const formattedLine = lineData.line.substring(0, 2) + lineData.line.substring(lineData.line.length - 2);
        const section = lineData.line.substring(2, 4);
        
        if (!groupedData[formattedLine]) {
            groupedData[formattedLine] = {
                CP: { units: [] },
                OL: { units: [] }
            };
        }
        
        if (section === 'CP' || section === 'OL') {
            groupedData[formattedLine][section].units = lineData.units;
        }
    });

    Object.entries(groupedData).forEach(([formattedLine, sections]) => {
        const tableContainer = document.createElement('div');
        tableContainer.className = 'line-table';
        const table = document.createElement('table');
        table.className = 'tt-table';

        const isSpecialLine = specialLines.includes(formattedLine);
        const normalThreshold = 19;
        const specialThreshold = 24;

        let tableHTML = `
            <tr>
                <th>${formattedLine}</th>
                <th>UNIT</th>
                <th>TT</th>
            </tr>`;

        const generateTTCell = (ttValue) => {
            const numericValue = parseFloat(ttValue);
            const threshold = isSpecialLine ? specialThreshold : normalThreshold;
            const isRed = !isNaN(numericValue) && numericValue > threshold;
            return `<td${isRed ? ' style="color: red;"' : ''}>${ttValue ?? ''}</td>`;
        };

        // CP 部分 (固定5行)
        const cpUnits = sections.CP.units.slice(0, 5); // 限制最多5行
        const cpMaxRows = 5;
        if (cpUnits.length > 0 || cpMaxRows > 0) {
            const maxCP = Math.max(...cpUnits.map(unit => unit.tt));
            const cpThreshold = isSpecialLine ? specialThreshold : normalThreshold;
            const cpStyle = (maxCP > cpThreshold) ? 'style="background-color: pink;"' : '';

            tableHTML += `
                <tr>
                    <th rowspan="${cpMaxRows}" ${cpStyle}>CP<br>${maxCP}</th>
                    <td>${cpUnits[0]?.units ?? '-'}</td>
                    ${generateTTCell(cpUnits[0]?.tt)}
                </tr>`;

            for (let i = 1; i < cpMaxRows; i++) {
                const unit = cpUnits[i];
                tableHTML += `
                    <tr>
                        <td>${unit?.units ?? '-'}</td>
                        ${generateTTCell(unit?.tt)}
                    </tr>`;
            }
        }

        // OL 部分 (固定20行)
        const olUnits = sections.OL.units.slice(0, 20); // 限制最多20行
        const olMaxRows = 20;
        if (olUnits.length > 0 || olMaxRows > 0) {
            const maxOL = Math.max(...olUnits.map(unit => unit.tt));
            const olThreshold = isSpecialLine ? specialThreshold : normalThreshold;
            const olStyle = (maxOL > olThreshold) ? 'style="background-color: pink;"' : '';

            tableHTML += `
                <tr>
                    <th rowspan="${olMaxRows}" ${olStyle}>OL<br>${maxOL}</th>
                    <td>${olUnits[0]?.units ?? '-'}</td>
                    ${generateTTCell(olUnits[0]?.tt)}
                </tr>`;

            for (let i = 1; i < olMaxRows; i++) {
                const unit = olUnits[i];
                tableHTML += `
                    <tr>
                        <td>${unit?.units ?? '-'}</td>
                        ${generateTTCell(unit?.tt)}
                    </tr>`;
            }
        }

        table.innerHTML = tableHTML;
        tableContainer.appendChild(table);
        container.appendChild(tableContainer);
    });
}

// 根据屏幕宽度自动调整
function adjustTableWidth() {
    const containerWidth = document.getElementById('dynamicTableContainer').offsetWidth;
    const tables = document.querySelectorAll('.line-table');
    const tableWidth = Math.min(150, containerWidth * 0.8);
    
    tables.forEach(table => {
      table.style.width = `${tableWidth}px`;
    });
  }
  
  window.addEventListener('resize', adjustTableWidth);

// 重置筛选器和数据区域的函数
function resetFiltersAndData() {
    const dateSelect = document.getElementById('dateSelect');
    const lineSelect = document.getElementById('lineSelect');
    const unitSelect = document.getElementById('unitSelect');
    const tableBody = document.getElementById('tableBody');
    const chartContainer = document.getElementById('chartContainer');    

    dateSelect.value = '';
    lineSelect.value = '';
    unitSelect.innerHTML = '<option value="">请先选择LINE</option>';
    tableBody.innerHTML = '';
    chartContainer.innerHTML = '';
}