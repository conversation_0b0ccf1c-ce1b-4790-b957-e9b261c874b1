<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取section参数
    $section = isset($_GET['section']) ? $_GET['section'] : '';
    // 获取project参数
    $project = isset($_GET['project']) ? $_GET['project'] : '';
    
    // // 准备SQL语句，根据section筛选，并统计附件数量
    // if (!empty($section)) {
    //     $sql = "SELECT n.*, COUNT(nf.id) as file_count 
    //             FROM notice n 
    //             LEFT JOIN notice_files nf ON n.id = nf.notice_id 
    //             WHERE n.section = ? 
    //             GROUP BY n.id 
    //             ORDER BY n.upload_time DESC";
    //     $stmt = $conn->prepare($sql);
    //     if (!$stmt) {
    //         throw new Exception("准备语句失败: " . $conn->error);
    //     }
    //     $stmt->bind_param('s', $section);
    //     $stmt->execute();
    //     $result = $stmt->get_result();
    // } else {
    //     // 如果没有section参数，返回所有通知
    //     $sql = "SELECT n.*, COUNT(nf.id) as file_count 
    //             FROM notice n 
    //             LEFT JOIN notice_files nf ON n.id = nf.notice_id 
    //             GROUP BY n.id 
    //             ORDER BY n.upload_time DESC";
    //     $result = $conn->query($sql);
    // }

        $sql = "SELECT n.*, COUNT(nf.id) as file_count 
                FROM notice n 
                LEFT JOIN notice_files nf ON n.id = nf.notice_id 
            WHERE (n.end_time IS NULL OR n.end_time IS NOT NULL)";

    // 初始化参数数组和类型字符串
    $params = [];
    $types = '';
    
    // 添加section条件
    if (!empty($section)) {
        $sql .= " AND n.section = ?";
        $params[] = $section;
        $types .= 's';
    }
    
    // 添加project条件
    if (!empty($project)) {
        $sql .= " AND n.project = ?";
        $params[] = $project;
        $types .= 's';
    }
    
    // 添加分组和排序
    $sql .= " GROUP BY n.id ORDER BY n.upload_time DESC";
    
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("准备语句失败: " . $conn->error);
        }
    
    // 绑定参数
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
        $stmt->execute();
        $result = $stmt->get_result();
    
    if (!$result) {
        throw new Exception("查询失败: " . $conn->error);
    }
    
    $notices = [];
    while ($row = $result->fetch_assoc()) {
        // 确保 file_count 是数字类型
        $row['file_count'] = intval($row['file_count']);
        $notices[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $notices
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 