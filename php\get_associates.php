<?php
header('Content-Type: application/json');
require_once 'db_config.php';

try {
    // 获取查询参数
    $section = $_GET['section'] ?? '';
    $classes = $_GET['classes'] ?? '';
    $line = $_GET['line'] ?? '';
    $project = $_GET['project'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $problemtype = $_GET['problemtype'] ?? '';
    $needfollow = $_GET['needfollow'] ?? '';
    $status = $_GET['status'] ?? '';
    $keyword = $_GET['problemKeyword'] ?? '';
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10;

    // 构建WHERE子句
    $where = [];
    $params = [];
    $types = '';

    if (!empty($section)) {
        $where[] = "section = ?";
        $params[] = $section;
        $types .= 's';
    }

    if (!empty($classes)) {
        $where[] = "classes = ?";
        $params[] = $classes;
        $types .= 's';
    }

    if (!empty($line)) {
        $where[] = "line = ?";
        $params[] = $line;
        $types .= 's';
    }

    if (!empty($project)) {
        $where[] = "project = ?";
        $params[] = $project;
        $types .= 's';
    }

    if (!empty($unit)) {
        $where[] = "unit = ?";
        $params[] = $unit;
        $types .= 's';
    }

    if (!empty($problemtype)) {
        $where[] = "problemtype = ?";
        $params[] = $problemtype;
        $types .= 's';
    }

    if (!empty($needfollow)) {
        $where[] = "needfollow = ?";
        $params[] = $needfollow;
        $types .= 's';
    }

    if (!empty($status)) {
        $where[] = "status = ?";
        $params[] = $status;
        $types .= 's';
    }

    if (!empty($keyword)) {
        $where[] = "(phenomenon LIKE ? OR analysis LIKE ? OR measure LIKE ?)";
        $params[] = "%$keyword%";
        $params[] = "%$keyword%";
        $params[] = "%$keyword%";
        $types .= 'sss';
    }

    if (!empty($startDate)) {
        $where[] = "DATE(created_at) >= ?";
        $params[] = $startDate;
        $types .= 's';
    }

    if (!empty($endDate)) {
        $where[] = "DATE(created_at) <= ?";
        $params[] = $endDate;
        $types .= 's';
    }

    // 计算总记录数
    //$countSql = "SELECT COUNT(*) as total FROM associatelist";
    $countSql = "SELECT COUNT(*) as total FROM (select * from associatelist t1 
    JOIN ( SELECT MAX(step) AS step1, section as section1, classes as classes1, line as line1, project as project1, unit as unit1, phenomenon as phenomenon1, created_at as  created_at1
    FROM associatelist 
    GROUP BY section, classes, line, project, unit, problemtype, phenomenon, problempart, problemcode, created_at ) t2 
    ON t1.step = t2.step1 
    AND t1.section = t2.section1
    AND t1.classes = t2.classes1 
    AND t1.line = t2.line1 
    AND t1.project = t2.project1
    AND t1.unit = t2.unit1 
    AND t1.phenomenon = t2.phenomenon1 
    AND t1.created_at = t2.created_at1) t3";
    if (!empty($where)) {
        $countSql .= " WHERE " . implode(" AND ", $where);
    }

    $countStmt = $conn->prepare($countSql);
    if (!empty($params)) {
        $countStmt->bind_param($types, ...$params);
    }
    $countStmt->execute();
    $totalResult = $countStmt->get_result()->fetch_assoc();
    $total = $totalResult['total'];

    // 计算总页数
    $totalPages = ceil($total / $pageSize);
    $offset = ($page - 1) * $pageSize;

    // 获取数据
    // $sql = "SELECT a.*, 
    //         (SELECT COUNT(*) FROM associate_files WHERE associate_id = a.id) as image_count 
    //         FROM associatelist a";
    $sql = "select id,section,classes,line,project,unit,problemtype,phenomenon,analysis,measure,problempart,
    problempart,problemcode,needfollow,status,step,recorder,created_at,updated_at 
    from (select * from associatelist t1 
    JOIN ( SELECT MAX(step) AS step1, section as section1, classes as classes1, line as line1, project as project1, unit as unit1, phenomenon as phenomenon1, created_at as  created_at1
    FROM associatelist 
    GROUP BY section, classes, line, project, unit, problemtype, phenomenon, problempart, problemcode, created_at ) t2 
    ON t1.step = t2.step1 
    AND t1.section = t2.section1
    AND t1.classes = t2.classes1
    AND t1.line = t2.line1
    AND t1.project = t2.project1  
    AND t1.unit = t2.unit1 
    AND t1.phenomenon = t2.phenomenon1 
    AND t1.created_at = t2.created_at1) t3";
    if (!empty($where)) {
        $sql .= " WHERE " . implode(" AND ", $where);
    }
    $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
    //$sql .= " group by section,line,unit ORDER BY created_at DESC LIMIT ? OFFSET ?";

    // 添加分页参数
    $params[] = $pageSize;
    $params[] = $offset;
    $types .= 'ii';

    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
        'total' => $total,
        'totalPages' => $totalPages,
        'currentPage' => $page
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 