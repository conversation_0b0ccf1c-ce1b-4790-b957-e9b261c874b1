<?php
/**
 * 图表重复加载修复验证脚本
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>图表重复加载修复验证</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
    .success { background: #d4edda; border-color: #c3e6cb; }
    .error { background: #f8d7da; border-color: #f5c6cb; }
    .warning { background: #fff3cd; border-color: #ffeaa7; }
    .info { background: #d1ecf1; border-color: #bee5eb; }
    .code { font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; }
</style>";

// 检查文件是否存在
$files_to_check = [
    'js/echarts.js' => 'ECharts库文件',
    'js/main.js' => '主JavaScript文件',
    'index.html' => '主页面文件',
    'php/get_data.php' => '数据获取API',
    'chart_fix_test.html' => '图表修复测试页面'
];

echo "<h2>1. 文件完整性检查</h2>";
foreach ($files_to_check as $file => $description) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<div class='test success'>✓ {$description}: 存在 ({$size} bytes)</div>";
    } else {
        echo "<div class='test error'>✗ {$description}: 不存在</div>";
    }
}

// 检查ECharts文件内容
echo "<h2>2. ECharts库检查</h2>";
if (file_exists('js/echarts.js')) {
    $echarts_content = file_get_contents('js/echarts.js');
    $echarts_size = strlen($echarts_content);
    
    if ($echarts_size > 100000) { // ECharts库应该比较大
        echo "<div class='test success'>✓ ECharts库文件大小正常: " . number_format($echarts_size) . " bytes</div>";
        
        // 检查是否包含关键标识
        if (strpos($echarts_content, 'echarts') !== false) {
            echo "<div class='test success'>✓ ECharts库包含核心标识</div>";
        } else {
            echo "<div class='test warning'>⚠ ECharts库可能不完整</div>";
        }
    } else {
        echo "<div class='test error'>✗ ECharts库文件过小，可能损坏</div>";
    }
} else {
    echo "<div class='test error'>✗ ECharts库文件不存在</div>";
}

// 检查主JavaScript文件的关键修复
echo "<h2>3. JavaScript修复检查</h2>";
if (file_exists('js/main.js')) {
    $js_content = file_get_contents('js/main.js');
    
    $checks = [
        'chart.dispose()' => '图表销毁逻辑',
        'echarts.init' => '图表初始化',
        'typeof echarts' => 'ECharts库检查',
        'debugCurrentState' => '调试函数',
        'checkChartStatus' => '图表状态检查',
        'initializeApp' => '应用初始化函数'
    ];
    
    foreach ($checks as $pattern => $description) {
        if (strpos($js_content, $pattern) !== false) {
            echo "<div class='test success'>✓ {$description}: 已实现</div>";
        } else {
            echo "<div class='test error'>✗ {$description}: 缺失</div>";
        }
    }
} else {
    echo "<div class='test error'>✗ 主JavaScript文件不存在</div>";
}

// 检查HTML文件的ECharts引用
echo "<h2>4. HTML文件检查</h2>";
if (file_exists('index.html')) {
    $html_content = file_get_contents('index.html');
    
    if (strpos($html_content, 'js/echarts.js') !== false) {
        echo "<div class='test success'>✓ ECharts库引用存在</div>";
    } else {
        echo "<div class='test error'>✗ ECharts库引用缺失</div>";
    }
    
    if (strpos($html_content, 'loadEChartsFromCDN') !== false) {
        echo "<div class='test success'>✓ ECharts备用加载方案存在</div>";
    } else {
        echo "<div class='test warning'>⚠ ECharts备用加载方案缺失</div>";
    }
    
    if (strpos($html_content, 'aggregationSlider') !== false) {
        echo "<div class='test success'>✓ 聚合级别控制器存在</div>";
    } else {
        echo "<div class='test error'>✗ 聚合级别控制器缺失</div>";
    }
} else {
    echo "<div class='test error'>✗ 主页面文件不存在</div>";
}

// 数据库连接测试
echo "<h2>5. 数据库连接测试</h2>";
try {
    require_once 'php/config.php';
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    echo "<div class='test success'>✓ 数据库连接成功</div>";
    
    // 检查测试数据
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM eqp_data");
    $result = $stmt->fetch();
    $count = $result['count'];
    
    if ($count > 0) {
        echo "<div class='test success'>✓ 数据库包含 {$count} 条测试数据</div>";
    } else {
        echo "<div class='test warning'>⚠ 数据库中没有测试数据</div>";
    }
    
    // 测试API调用
    $stmt = $pdo->query("SELECT DISTINCT param_name FROM eqp_data LIMIT 1");
    $result = $stmt->fetch();
    if ($result) {
        $test_param = $result['param_name'];
        echo "<div class='test success'>✓ 找到测试参数: {$test_param}</div>";
        
        // 模拟API调用
        $_GET = [
            'param_name' => $test_param,
            'time_range' => 24,
            'aggregation_level' => 'hour'
        ];
        
        ob_start();
        include 'php/get_data.php';
        $api_output = ob_get_clean();
        
        $api_result = json_decode($api_output, true);
        if ($api_result && $api_result['success']) {
            echo "<div class='test success'>✓ API调用测试成功</div>";
        } else {
            echo "<div class='test error'>✗ API调用测试失败</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='test error'>✗ 数据库连接失败: " . $e->getMessage() . "</div>";
}

// 修复建议
echo "<h2>6. 修复验证和建议</h2>";

echo "<div class='test info'>";
echo "<h3>修复验证步骤：</h3>";
echo "<ol>";
echo "<li>访问 <a href='chart_fix_test.html' target='_blank'>图表修复测试页面</a> 进行详细测试</li>";
echo "<li>在主系统中测试筛选条件变更功能</li>";
echo "<li>使用浏览器开发者工具检查控制台日志</li>";
echo "<li>验证图表在多次变更后仍能正常显示</li>";
echo "</ol>";
echo "</div>";

echo "<div class='test info'>";
echo "<h3>浏览器测试命令：</h3>";
echo "<p>在浏览器控制台中执行以下命令进行调试：</p>";
echo "<div class='code'>";
echo "// 检查ECharts库<br>";
echo "console.log('ECharts loaded:', typeof echarts !== 'undefined');<br><br>";
echo "// 检查应用状态<br>";
echo "if (window.equipmentMonitor) {<br>";
echo "&nbsp;&nbsp;window.equipmentMonitor.debugCurrentState();<br>";
echo "&nbsp;&nbsp;window.equipmentMonitor.checkChartStatus();<br>";
echo "}<br><br>";
echo "// 手动触发图表重新加载<br>";
echo "if (window.equipmentMonitor) {<br>";
echo "&nbsp;&nbsp;window.equipmentMonitor.loadChartData();<br>";
echo "}";
echo "</div>";
echo "</div>";

echo "<div class='test info'>";
echo "<h3>常见问题解决方案：</h3>";
echo "<ul>";
echo "<li><strong>ECharts库加载失败</strong>: 检查 js/echarts.js 文件是否完整，或使用CDN备用方案</li>";
echo "<li><strong>图表重复渲染失败</strong>: 确保每次渲染前正确销毁旧实例</li>";
echo "<li><strong>事件绑定问题</strong>: 检查DOM元素是否在事件绑定前完全加载</li>";
echo "<li><strong>数据格式错误</strong>: 验证API返回的数据结构是否正确</li>";
echo "</ul>";
echo "</div>";

echo "<br><div style='text-align: center;'>";
echo "<a href='index.html' style='margin: 10px; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 4px;'>返回主系统</a>";
echo "<a href='chart_fix_test.html' style='margin: 10px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 4px;'>图表修复测试</a>";
echo "<a href='debug_chart.html' style='margin: 10px; padding: 10px 20px; background: #ffc107; color: black; text-decoration: none; border-radius: 4px;'>调试工具</a>";
echo "</div>";
?>
