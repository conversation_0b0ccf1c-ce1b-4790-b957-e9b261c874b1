<?php
header('Content-Type: application/json'); //设置响应头为 JSON 格式
require_once '../php/db_config.php'; //引入数据库配置文件

try {
    // 获取查询参数，使用空合并运算符设置默认值
    $showtype = $_GET['showtype'] ?? '';
    $params = [
        'isbigalarm' => $_GET['isbigalarm'] ?? '',
        'recorder' => $_GET['recorder'] ?? '',

        'section' => $_GET['section'] ?? '',
        'classes' => $_GET['classes'] ?? '', 
        'line' => $_GET['line'] ?? '',//5
        'project' => $_GET['project'] ?? '',
        'unit' => $_GET['unit'] ?? '',
        'problemtype' => $_GET['problemtype'] ?? '',
        'needfollow' => $_GET['needfollow'] ?? '', 
        'towho' => $_GET['towho'] ?? '',//10
        'status' => $_GET['status'] ?? '', //11 
        'keyword' => $_GET['keyword'] ?? '', 
        'startDate' => $_GET['startDate'] ?? '',
        'endDate' => $_GET['endDate'] ?? '',
        'page' => isset($_GET['page']) ? (int)$_GET['page'] : 1, 
        'pageSize' => isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10, 
    ];

    // 构建WHERE子句和参数
    $where = [];
    $bindParams = [];
    $bindTypes = '';
    $singleValueFields = ['isbigalarm', 'recorder', 'section', 'classes',
        'line', 'project', 'unit', 'problemtype', 'needfollow', 
        'towho', 'status']; // 11EA
    foreach($singleValueFields as $field){
        if(!empty($params[$field])){
            $where[] = "$field = ?";
            $bindParams[] = $params[$field];
            $bindTypes .= 's';
        }
    }

    if (!empty($params['keyword'])) {
        $where[] = "(phenomenon LIKE ? OR analysis LIKE ? OR measure LIKE ?)";
        $bindParams[] = "%{$params['keyword']}%";
        $bindParams[] = "%{$params['keyword']}%";
        $bindParams[] = "%{$params['keyword']}%";
        $bindTypes .= 'sss';
    }

    if (!empty($params['startDate'])) { 
		$where[] = "DATE(created_at) >= ?"; 
		$bindParams[] = $params['startDate']; 
		$bindTypes .= 's'; 
	} 

	if (!empty($params['endDate'])) { 
		$where[] = "DATE(created_at) <= ?"; 
		$bindParams[] = $params['endDate']; 
		$bindTypes .= 's'; 
	} 

    // 公共子查询
    $subQuery = " SELECT * FROM associatelist t1 
        Join ( 
            SELECT 
                MAX(step) AS step1, 
                section as section1, 
                classes as classes1, 
                line as line1, 
                project as project1, 
                unit as unit1, 
                phenomenon as phenomenon1, 
                created_at as  created_at1
            FROM
                associatelist
            GROUP BY
                section, 
                classes, 
                line, 
                project, 
                unit, 
                problemtype, 
                phenomenon, 
                problempart, 
                problemcode, 
                created_at
            ) t2
        ON
            t1.step = t2.step1 
			AND t1.section = t2.section1 
			AND t1.classes = t2.classes1 
			AND t1.line = t2.line1 
			AND t1.project = t2.project1 
			AND t1.unit = t2.unit1 
			AND t1.phenomenon = t2.phenomenon1 
			AND t1.created_at = t2.created_at1
    ";

    // 计算总记录数
    $countSql = "SELECT COUNT(*) as total FROM ($subQuery) t3";
    if (!empty($where)) {
        $countSql .= " WHERE " . implode(" AND ", $where);
    }

    $countStmt = $conn->prepare($countSql);
    if (!empty($bindParams)) {
        $countStmt->bind_param($bindTypes, ...$bindParams);
    }
    $countStmt->execute();
    $totalResult = $countStmt->get_result()->fetch_assoc();
    $total = $totalResult['total'];

    // 计算总页数
    $totalPages = $params['pageSize'] > 0 ? ceil($total / $params['pageSize']) : $total;
    $offset = ($params['page'] - 1) * $params['pageSize'];

    // 获取数据
    $dataSql = "SELECT * FROM ($subQuery) t3";
    if (!empty($where)) {
        $dataSql .= " WHERE " . implode(" AND ", $where);
    }

    if($params['pageSize'] > 0){
        $dataSql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        // 添加分页参数
        $bindParams[] = $params['pageSize'];
        $bindParams[] = $offset;
        $bindTypes .= 'ii';
    }
    else{
        $dataSql .= " ORDER BY created_at DESC";
    }

    $dataStmt = $conn->prepare($dataSql);
    if (!$dataStmt) { 
        die("dataStmt Prepare failed: " . $conn->error); 
    } 
    $dataStmt->bind_param($bindTypes, ...$bindParams);
    $dataStmt->execute();
    $result = $dataStmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        // 检查有无附件 - 20250602
        // 根据 section, classes, line, project, unit, problemtype, phenomenon, created_at 选择到所有id，
        // 再遍历查询是否有附件
        $number_files = 0;

        $idSql = "SELECT id FROM associatelist 
                WHERE 
                    section = ? 
                    AND classes = ? 
                    AND line = ? 
                    AND project = ? 
                    AND unit = ? 
                    AND problemtype = ? 
                    AND phenomenon = ? 
                    AND created_at = ?
                ";
        $idStmt = $conn->prepare($idSql);
        $idStmt->bind_param("ssssssss", $row['section'],$row['classes'],$row['line'],$row['project'],$row['unit'],$row['problemtype'],$row['phenomenon'],$row['created_at']);
        $idStmt->execute();
        $idResult = $idStmt->get_result();
        $ids = [];
        while($idRow = $idResult->fetch_assoc()){
            $ids[] = $idRow['id']; 
        }
        if(!empty($ids)){
            $placeholders = implode(',',array_fill(0, count($ids), '?'));
            $fileSql = "SELECT * FROM associate_files where associate_id IN ($placeholders)";
            $fileStmt = $conn->prepare($fileSql);
            $fileBindTypes = str_repeat('i', count($ids));
            $fileStmt->bind_param($fileBindTypes, ...$ids);
            $fileStmt->execute(); 
            $fileResult = $fileStmt->get_result(); 
            $number_files = $fileResult->num_rows;  
        }
        $row['number_files'] = $number_files;// 添加一列：有多少了附件
        $data[] = $row;
    }

    // 返回 JSON 数据
    echo json_encode([
        'success' => true,
        'data' => $data,
        'total' => $total,
        'totalPages' => $totalPages,
        'currentPage' => $params['page']
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 