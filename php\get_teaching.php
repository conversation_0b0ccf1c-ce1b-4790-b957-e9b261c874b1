<?php
ini_set('display_errors', 0);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 数据库连接配置
require_once 'db_config.php';

try {
    // 获取查询参数
    $title = $_GET['title'] ?? '';
    $category = $_GET['category'] ?? '';
    $section = $_GET['section'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $upload_user = $_GET['upload_user'] ?? '';
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, intval($_GET['limit'])) : 10;
    $offset = ($page - 1) * $limit;

    // 构建查询条件
    $conditions = [];
    $params = [];
    $types = '';

    if (!empty($title)) {
        $conditions[] = "tm.title LIKE ?";
        $params[] = "%$title%";
        $types .= 's';
    }

    if (!empty($category)) {
        $conditions[] = "tm.category = ?";
        $params[] = $category;
        $types .= 's';
    }

    if (!empty($section)) {
        $conditions[] = "tm.section = ?";
        $params[] = $section;
        $types .= 's';
    }

    if (!empty($unit)) {
        $conditions[] = "tm.unit = ?";
        $params[] = $unit;
        $types .= 's';
    }

    if (!empty($upload_user)) {
        $conditions[] = "tm.upload_user LIKE ?";
        $params[] = "%$upload_user%";
        $types .= 's';
    }

    // 构建 WHERE 子句
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';

    // 获取总记录数
    $countSql = "SELECT COUNT(DISTINCT tm.id) as total 
                 FROM teaching_materials tm 
                 LEFT JOIN teaching_files tf ON tm.id = tf.material_id 
                 $whereClause";
    $stmt = $conn->prepare($countSql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $totalResult = $stmt->get_result()->fetch_assoc();
    $total = $totalResult['total'];

    // 获取分页数据
    $sql = "SELECT 
                tm.*, 
                GROUP_CONCAT(tf.file_name) as file_names,
                GROUP_CONCAT(tf.file_path) as file_paths,
                GROUP_CONCAT(tf.file_size) as file_sizes,
                GROUP_CONCAT(tf.id) as file_ids
              FROM teaching_materials tm 
              LEFT JOIN teaching_files tf ON tm.id = tf.material_id 
              $whereClause 
              GROUP BY tm.id 
              ORDER BY tm.upload_time DESC 
              LIMIT ? OFFSET ?";

    $stmt = $conn->prepare($sql);
    
    // 添加分页参数
    $params[] = $limit;
    $params[] = $offset;
    $types .= 'ii';
    
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        // 处理文件数组
        if ($row['file_names']) {
            $fileNames = explode(',', $row['file_names']);
            $filePaths = explode(',', $row['file_paths']);
            $fileSizes = explode(',', $row['file_sizes']);
            $fileIds = explode(',', $row['file_ids']);
            
            $row['files'] = array_map(function($id, $name, $path, $size) {
                return [
                    'id' => $id,
                    'name' => $name,
                    'path' => $path,
                    'size' => $size
                ];
            }, $fileIds, $fileNames, $filePaths, $fileSizes);
        } else {
            $row['files'] = [];
        }

        // 删除原始的连接字段
        unset($row['file_names']);
        unset($row['file_paths']);
        unset($row['file_sizes']);
        unset($row['file_ids']);

        $data[] = $row;
    }

    // 计算总页数
    $totalPages = ceil($total / $limit);

    echo json_encode([
        'success' => true,
        'data' => $data,
        'pagination' => [
            'total' => $total,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'limit' => $limit
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 