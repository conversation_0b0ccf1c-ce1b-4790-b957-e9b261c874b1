<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取搜索参数
    $agvInfo = isset($_GET['agvInfo']) ? trim($_GET['agvInfo']) : '';
    $key = isset($_GET['key']) ? trim($_GET['key']) : '';
    $start_date = isset($_GET['start_date']) ? trim($_GET['start_date']) : '';
    $end_date = isset($_GET['end_date']) ? trim($_GET['end_date']) : '';
    
    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : 10;
    
    // 构建查询条件
    $whereConditions = [];
    $params = [];
    $types = '';
    
    // 添加车辆信息条件
    if (!empty($agvInfo)) {
        $whereConditions[] = "ab.agv_info = ?";
        $params[] = $agvInfo;
        $types .= 's';
    }
    
    // 添加电池信息模糊匹配
    if (!empty($key)) {
        $whereConditions[] = "(ab.old_battery LIKE ? OR ab.new_battery LIKE ?)";
        $params[] = "%$key%";
        $params[] = "%$key%";
        $types .= 'ss';
    }
    
    // 添加日期区间
    if (!empty($start_date) && !empty($end_date)) {
        $whereConditions[] = "((ab.move_out_date BETWEEN ? AND ?) OR (ab.return_date BETWEEN ? AND ?))";
        $params[] = $start_date;
        $params[] = $end_date;
        $params[] = $start_date;
        $params[] = $end_date;
        $types .= 'ssss';
    } elseif (!empty($start_date)) {
        $whereConditions[] = "(ab.move_out_date >= ? OR ab.return_date >= ?)";
        $params[] = $start_date;
        $params[] = $start_date;
        $types .= 'ss';
    } elseif (!empty($end_date)) {
        $whereConditions[] = "(ab.move_out_date <= ? OR ab.return_date <= ?)";
        $params[] = $end_date;
        $params[] = $end_date;
        $types .= 'ss';
    }
    
    // 组合WHERE子句
    $whereClause = '';
    if (!empty($whereConditions)) {
        $whereClause = "WHERE " . implode(" AND ", $whereConditions);
    }
    
    // 查询总记录数
    $countSql = "SELECT COUNT(*) as total FROM agv_battery ab $whereClause";
    
    if (!empty($params)) {
        $countStmt = $conn->prepare($countSql);
        $countStmt->bind_param($types, ...$params);
        $countStmt->execute();
        $totalResult = $countStmt->get_result();
    } else {
        $totalResult = $conn->query($countSql);
    }
    
    $totalRow = $totalResult->fetch_assoc();
    $total = $totalRow['total'];

    // 构建最终查询参数
    $limitClause = '';
    if ($pageSize > 0) {  
        $offset = ($page - 1) * $pageSize;
        $limitClause = "LIMIT ?, ?";
        $finalTypes = $types . 'ii';
        $finalParams = array_merge($params, [$offset, $pageSize]);
    } else {
        $finalTypes = $types;
        $finalParams = $params;
    }
    
    // 主查询
    $sql = "SELECT ab.*, 
                   GROUP_CONCAT(abf.file_name SEPARATOR '|') as file_names,
                   GROUP_CONCAT(abf.file_path SEPARATOR '|') as file_paths
            FROM agv_battery ab 
            LEFT JOIN agv_battery_files abf ON ab.id = abf.battery_id 
            $whereClause 
            GROUP BY ab.id 
            ORDER BY ab.change_date DESC";
    
    if ($pageSize > 0) $sql .= " LIMIT ?, ?";

    $stmt = $conn->prepare($sql);
    if ($pageSize > 0) {
        $stmt->bind_param($finalTypes, ...$finalParams);
    } elseif (!empty($finalTypes)) {
        $stmt->bind_param($finalTypes, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $batteryChanges = [];
    while ($row = $result->fetch_assoc()) {
        // 处理文件信息
        $files = [];
        if (!empty($row['file_names'])) {
            $fileNames = explode('|', $row['file_names']);
            $filePaths = explode('|', $row['file_paths']);
            
            for ($i = 0; $i < count($fileNames); $i++) {
                $files[] = [
                    'file_name' => $fileNames[$i],
                    'file_path' => $filePaths[$i] ?? ''
                ];
            }
        }
        
        $row['files'] = $files;
        // 移除临时字段
        unset($row['file_names']);
        unset($row['file_paths']);
        
        $batteryChanges[] = $row;
    }

    // 计算总页数
    $totalPages = ($pageSize > 0) ? ceil($total / $pageSize) : 1;

    echo json_encode([
        'success' => true,
        'data' => $batteryChanges,
        'total' => $total,
        'currentPage' => $page,
        'totalPages' => $totalPages,
        'pageSize' => $pageSize
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>
