<?php
header('Content-Type: application/json');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

// 数据库配置信息
$serverName = "109.120.2.35";
$connectionOptions = array(
    "Database" => "EQP_management",
    "Uid" => "eqplink",
    "PWD" => "eqplink",
    "CharacterSet" => "UTF-8"
);

try {
    $conn = sqlsrv_connect($serverName, $connectionOptions);

    if ($conn === false) {
        throw new Exception("Connection failed: " . print_r(sqlsrv_errors(), true));
    }

    // 获取查询参数
    $section = isset($_GET['section']) ? trim($_GET['section']) : '';
    $project = isset($_GET['project']) ? trim($_GET['project']) : '';
    $line = isset($_GET['line']) ? trim($_GET['line']) : '';
    $unit = isset($_GET['unit']) ? trim($_GET['unit']) : '';
    $partname = isset($_GET['partname']) ? trim($_GET['partname']) : '';
    $parttype = isset($_GET['parttype']) ? trim($_GET['parttype']) : '';
    $partbrand = isset($_GET['partbrand']) ? trim($_GET['partbrand']) : '';
    $partmodel = isset($_GET['partmodel']) ? trim($_GET['partmodel']) : '';
    $useposition = isset($_GET['useposition']) ? trim($_GET['useposition']) : '';
    $partlevel = isset($_GET['partlevel']) ? trim($_GET['partlevel']) : '';
    
    //$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    //$pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10;
    //确保 `$page` 和 `$pageSize` 为正整数。
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1; 
    $pageSize = isset($_GET['pageSize']) ? max(1, (int)$_GET['pageSize']) : 10; 

    // 构建WHERE子句
    $where = [];
    $params = [];

    // 构建参数数组
    if (!empty($section)) {
        $where[] = "section = ?";
        $params[] = array($section, SQLSRV_PARAM_IN);
    }

    if (!empty($project)) {
        $where[] = "project = ?";
        $params[] = array($project, SQLSRV_PARAM_IN);
    }

    if (!empty($line)) {
        $where[] = "line = ?";
        $params[] = array($line, SQLSRV_PARAM_IN);
    }

    if (!empty($unit)) {
        $where[] = "unit = ?";
        $params[] = array($unit, SQLSRV_PARAM_IN);
    }

    if (!empty($partname)) {
        $where[] = "partname = ?";
        $params[] = array($partname, SQLSRV_PARAM_IN);
    }

    if (!empty($parttype)) {
        $where[] = "parttype = ?";
        $params[] = array($parttype, SQLSRV_PARAM_IN);
    }

    if (!empty($partbrand)) {
        $where[] = "partbrand = ?";
        $params[] = array($partbrand, SQLSRV_PARAM_IN);
    }

    if (!empty($partmodel)) {
        $where[] = "partmodel = ?";
        $params[] = array($partmodel, SQLSRV_PARAM_IN);
    }

    if (!empty($useposition)) {
        $where[] = "useposition = ?";
        $params[] = array($useposition, SQLSRV_PARAM_IN);
    }

    if (!empty($partlevel)) {
        $where[] = "partlevel = ?";
        $params[] = array($partlevel, SQLSRV_PARAM_IN);
    }

    // 组合Where子句
    $whereClause = !empty($where) ? "WHERE ".implode(" AND ", $where) : "";

    // 计算总记录数
    //$countSql = "SELECT COUNT(*) as total FROM associatelist";
    $countSql = "SELECT COUNT(*) as total FROM parts_bom $whereClause";
    
    $countStmt = sqlsrv_query($conn,$countSql,$params);
    if($countStmt === false){
        $errors = sqlsrv_errors();
        $errorMessages = [];
        foreach ($errors as $error) {
            $errorMessages[] = "Code: {$error['code']}, Message: {$error['message']}";
        }
        throw new Exception("Query1 failed:\n" . implode("\n", $errorMessages));
    }

    // 处理结果集
    $totalResult = sqlsrv_fetch_array($countStmt, SQLSRV_FETCH_ASSOC);
    $total = $totalResult['total'];

    // 计算总页数
    if($pageSize > 0)
        $totalPages = ceil($total / $pageSize);
    else
        $totalPages = $total;
    $offset = ($page - 1) * $pageSize;

    // 获取数据
    if($pageSize > 0){
        // 添加分页参数
        $sql = "select * from parts_bom 
                $whereClause 
                ORDER BY created_at DESC OFFSET $offset ROWS FETCH NEXT $pageSize ROWS ONLY";
                //ORDER BY created_at DESC LIMIT ? OFFSET ?";
                //ORDER BY created_at DESC OFFSET $offset ROWS FETCH NEXT $pageSize ROWS ONLY
        //$params[] = array($pageSize, SQLSRV_PARAM_IN);
        //$params[] = array($offset, SQLSRV_PARAM_IN);
    }
    else{
        $sql = "select * from parts_bom $whereClause ORDER BY created_at DESC";
    }

    $stmt = sqlsrv_query($conn, $sql, $params);
    if ($stmt === false){
        $errors = sqlsrv_errors();
        $errorMessages = [];
        foreach ($errors as $error) {
            $errorMessages[] = "Code: {$error['code']}, Message: {$error['message']}";
        }
        throw new Exception("Query2 failed:\n" . implode("\n", $errorMessages));
    }

    // 处理结果集
    $data = array();
    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        $data[] = $row;
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
        'total' => $total,
        'totalPages' => $totalPages,
        'currentPage' => $page
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

if (isset($countStmt) && is_resource($countStmt)) {
    sqlsrv_free_stmt($countStmt);
}
if (isset($stmt) && is_resource($stmt)) {
    sqlsrv_free_stmt($stmt);
}
if ($conn !== null && $conn !== false) {
    sqlsrv_close($conn);
}
?> 