<?php
ini_set('display_errors', 0);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';

try {
    $materialId = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if ($materialId <= 0) {
        throw new Exception('无效的资料ID');
    }

    // 获取资料的所有文件
    $sql = "SELECT 
        tf.id,
        tf.file_name,
        tf.file_path,
        tf.file_size,
        tm.title,
        tm.category
    FROM teaching_files tf
    JOIN teaching_materials tm ON tf.material_id = tm.id
    WHERE tf.material_id = ?";

    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $materialId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $files = [];
    $materialInfo = null;
    
    while ($row = $result->fetch_assoc()) {
        if (!$materialInfo) {
            $materialInfo = [
                'title' => $row['title'],
                'category' => $row['category']
            ];
        }
        
        $files[] = [
            'id' => $row['id'],
            'name' => $row['file_name'],
            'path' => $row['file_path'],
            'size' => $row['file_size']
        ];
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'material' => $materialInfo,
            'files' => $files
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 