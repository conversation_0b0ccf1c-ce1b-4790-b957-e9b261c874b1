// 切换主要内容
function switchContent(contentId) {
    // 移除所有主内容的active类
    document.querySelectorAll('.page-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(contentId).classList.add('active');
    
    // 根据不同的内容ID切换到对应的默认选项卡
    switch(contentId) {
        case 'faultContent':
            switchTab('search');
            break;
        case 'associateContent':
            switchTab('asssearch');
            break;
        case 'machineContent':
            switchTab('machinePlan');
            break;
        case 'siteContent':
            switchTab('spotfire');
            break;
        case 'partsContent':
            switchTab('bomList');
            break;
        case 'improvementContent':
            switchTab('proposal');
            break;
        case 'manualContent':
            switchTab('manual');
            break;
        case 'noticeContent':
            switchTab('editor');
            break;
        case 'teachingContent':
            switchTab('teaching');
            break;
    }
}

// 切换普通选项卡
function switchTab(tabName) {
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active');
    });
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    document.querySelector(`button[onclick*="'${tabName}'"]`).classList.add('active');
    document.getElementById(`${tabName}Tab`).classList.add('active');
} 