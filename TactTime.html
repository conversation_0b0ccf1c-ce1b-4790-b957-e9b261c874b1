﻿<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"> 
    <meta http-equiv="Pragma" content="no-cache"> 
    <meta http-equiv="Expires" content="0"> 
    <title>TactTime - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/echarts.js"></script>
    <script src="js/tabs.js" defer></script>
    <script src="js/tact_time.js" defer></script>
</head>
<body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <button id="refreshButton" class="sparepartssearch-btn" onclick="refreshData()" style="margin-bottom:10px;">刷新数据</button>
        <br>
    <div id="dynamicTableContainer" style="min-height: 200px; position: relative;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); ">
            <h2>数据加载中...</h2>
        </div>
        <!-- 动态表格将在这里显示 -->
    </div>
    <br>
    <div class="filter-section">
        <select id="lineSelect">
            <option value="">选择LINE</option>
        </select>

        <select id="unitSelect">
            <option value="">选择UNIT</option>
        </select>

        <label for="dateSelect">日期</label>
        <input type="date" id="dateSelect" name="dateSelect">

        <button onclick="loadData()">加载数据</button>
        <button id="resetbtn">重置</button>
    </div>
    <div id="chartContainer" style="width: 100%; height: 400px;">
        <!-- 折线图将在这里显示 -->
    </div>
    <br>
    <table id="tt-table">
        <thead>
            <tr>
                <th>LINE</th>
                <th>UNITS</th>
                <th>UNIT</th>
                <th>PRODUCT</th>
                <th>DATE</th>
                <th>HH</th>
                <th>TT</th>
                <th>PERIOD</th>
                <th>MODULE</th>
            </tr>
        </thead>
        <tbody id="tableBody">
            <!-- 数据将在这里显示 -->
        </tbody>
    </table>
    </div>
</body>
</html> 