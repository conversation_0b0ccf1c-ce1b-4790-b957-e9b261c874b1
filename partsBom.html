<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate"> 
    <meta http-equiv="Pragma" content="no-cache"> <meta http-equiv="Expires" content="0"> 
    <title>备品BOM List - 设备综合管理</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="bomlist/partsBomStyle.css">
    <link rel="stylesheet" href="bomlist/importModalStyle.css">
    <script src="js/tabs.js" defer></script>
    <script src="bomlist/partsBom.js" defer></script>
    <script src="js/echarts.js" defer></script>
    <script src="js/xlsx.js" defer></script>
    <script src="js/html2canvas.js" defer></script>
    <script src="js/jquery-1.11.3.min.js" defer></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'bomSearch':
                        switchTab('bomSearch');
                        break;
                    case 'bomRegister':
                        switchTab('bomRegister');
                        break;
                    default:
                        switchTab('bomSearch');
                }
            }
        });
    </script>
</head>
<body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0;">
      <div class="tabs" style="display: none;">
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('bomSearch')">BOM查询</button>
                <button class="tab-button" onclick="switchTab('bomRegister')">BOM登录</button>
            </div>
            <button class="btn-home" onclick="window.location.href='index.html'">返回首页</button>
        </div>
            
        <!-- BOM查询选项卡 -->
        <div id="bomSearchTab" class="tab-content active">
            <div class="bomSearch">
                <form id="bomSearchForm" action="#" method="get">
                    <div class="bomSearch-row">
                        
                        <!-- <div id="projectSelect">
                            <label for="bomSearch-project">工程</label>
                            <select id="bomSearch-project" name="project" style="width: 90px;">
                            </select>
                        </div> -->

                        <label for="bomSearch-line">LINE</label>
                        <select id="bomSearch-line" name="line" style="width: 90px;">
                        </select>
                        
                        <label for="bomSearch-unit">UNIT</label>
                        <select id="bomSearch-unit" name="unit" style="width: 90px;">
                        </select>

                        <label for="bomSearch-partname">部品名</label>
                        <input type="text" id="bomSearch-partname" name="partname" style="width: 90px;">

                        <!-- <label for="bomSearch-parttype">部品类型</label>
                        <select id="bomSearch-parttype" name="parttype" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="气缸">气缸</option>
                            <option value="伺服">伺服</option>
                            <option value="马达">马达</option>
                            <option value="Sensor">Sensor</option>
                            <option value="主板">主板</option>
                            <option value="其他">其他</option>
                        </select> -->

                        <label for="bomSearch-partbrand">部品品牌</label>
                        <input type="text" id="bomSearch-partbrand" name="partbrand" style="width: 90px;">

                        <label for="bomSearch-partmodel">部品型号</label>
                        <input type="text" id="bomSearch-partmodel" name="partmodel" style="width: 90px;">

                        <!-- <label for="bomSearch-partstockcount">部品库存</label>
                        <input type="number" name="partstockcount" min="1" > -->

                        <label for="bomSearch-useposition">使用位置</label>
                        <input type="text" id="bomSearch-useposition" name="useposition" style="width: 90px;">

                        <label for="bomSearch-partlevel">部品级别</label>
                        <select id="bomSearch-partlevel" name="partlevel" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="常规备件">常规备件</option>
                            <option value="消耗备件">消耗备件</option>
                            <option value="高危备件">高危备件</option>
                        </select>
                        
                        <!-- <label for="bomSearch-recorder">登陆人</label>
                        <input type="text" id="bomSearch-recorder" name="recorder" style="width: 90px;"> -->

                        <div class="button-group">
                            <button type="submit" class="bomSearch-btn">查询</button>
                            <button type="reset" class="bomReset-btn">重置</button>
                            <!-- <button type="button" class="bomDownload-btn">导出</button> -->
                        </div>
                    </div>
                </form>
            </div>

            <div class="bomList">
                <table class="data-table" id="bomSearchTable">
                    <thead>
                        <tr>
                            <th>LINE</th>
                            <th>UNIT</th>
                            <th>部品名</th>
                            <th>品牌</th>
                            <th>型号</th>
                            <th>库存</th>
                            <th>使用位置</th>
                            <th>备品级别</th>
                            <th>安全库存</th>
                            <th>损坏履历</th>
                            <!-- <th>故障图片</th> -->
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过 JavaScript 动态加载 -->
                    </tbody>
                </table>
                <!-- 右键菜单 -->
                <!-- <div id="contextMenu" style="display:none; position:absolute; background:#fff; border:1px solid #ccc; padding:5px;"> 
                    <button onclick="associateManager.exportToExcel()">导出Excel</button> 
                </div>                  -->
            </div>

            <!-- 在表格后添加分页控件 -->
            <div class="pagination">
                <div class="pagination-info">
                    共 <span class="total-count">0</span> 条记录，
                    每页 <select class="page-size">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select> 条
                </div>
                <div class="pagination-controls">
                    <button class="btn-first-page">首页</button>
                    <button class="btn-prev-page">上一页</button>
                    <span class="page-info">
                        第 <input type="number" class="current-page" min="1"> 页，
                        共 <span class="total-pages">0</span> 页
                    </span>
                    <button class="btn-next-page">下一页</button>
                    <button class="btn-last-page">末页</button>
                </div>
            </div>
        </div>

        <!-- BOM登录选项卡 -->
        <div id="bomRegisterTab" class="tab-content">
            <div class="bomRegister">
                <form action="#" method="post" class="bomRegister-form">
                    <!-- 表头行 -->
                    <table class="register-table">
                        <tr>
                            <th>LINE</th>
                            <th>UNIT</th>
                            <th>部品名</th>
                            <th>品牌</th>
                            <th>型号</th>
                            <th>存库</th>
                            <th>使用位置</th>
                            <th>备品级别</th>
                            <th>安全库存</th>
                            <th>损坏履历</th>
                            <th>操作</th>
                        </tr>
                        <!-- 初始10行空表格 -->
                        <tbody id="registerTableBody">
                            <!-- 这里将通过JavaScript动态生成10行空表格 -->
                        </tbody>
                    </table>

                    <div class="form-actions">
                        <!-- <button type="button" class="btn-import-file">导入</button> -->
                        <button type="button" class="btn-add-rows">添加更多行</button>
                        <button type="submit" class="btn-submit">提交</button>
                        <button type="reset" class="btn-reset">重置</button>
                        <p style="color: red;">*请删除多余空行后提交！</p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 在交接列表表格后添加 处理 弹窗结构 -->
    <div id="bomDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>标题,line unit 故障现象</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <table class="detail-table">
                    <tr>
                        <th>LINE</th>
                        <th>UNIT</th>
                        <th>部品名</th>
                        <th>品牌</th>
                        <th>型号</th>
                        <!-- <th>存库</th>
                        <th>使用位置</th> -->
                        <!-- <th>备品级别</th>
                        <th>安全库存</th>
                        <th>损坏履历</th> -->
                        <th>登陆人</th>
                    </tr>
                    <tr>
                        <!-- 这里将通过JavaScript动态生成 -->
                    </tr>
                </table>

                <!-- 可以考虑添加一些资料、维修方法等 -->
                <div class="detail-sections" id="detail-sections">
                    <div class="detail-section">
                        <h3>库存信息</h3>
                        <div class="section-content">
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>使用信息</h3>
                        <div class="section-content">
                        </div>
                    </div>


                    <!-- <div class="detail-section">
                        <h3>备品级别</h3>
                        <div class="section-content">
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>安全库存</h3>
                        <div class="section-content">
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>损坏履历</h3>
                        <div class="section-content">
                            
                        </div>
                    </div> -->

                    <!-- <div class="detail-section">
                        <h3>相关附件</h3>
                        
                        <div class="section-content">
                        </div>
                    </div> -->
                </div>

                <div class="modal-footer">
                    <button class="btn-return">返回</button>
                    <!-- <button class="btn-modify" id="modal-btn-modify">修改</button> -->
                     <div class="modal-btn-modify">

                     </div>
                </div>
                <!-- 历史履历显示在下方 -->
                <!-- <div class="history" id="history" style="overflow: scroll; max-height: 250px;">
                    <div class="history-title">
                        <h3 onclick="associateManager.historyTitleClick(associateManager.bShowHistory)">点击历史履历信息展开</h3>
                    </div>
                    <div class="history-body" id="history-body" style="display: none;">
                        <table class="history-body-table" id="history-body-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>故障原因</th>
                                    <th>处理内容</th>
                                    <th>发生时间</th>
                                    <th>交接担当</th>
                                    <th>相关附件</th>
                                </tr>
                            </thead>
                            
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div> -->
          </div>
      </div>
    </div>

    <!-- 批量上传模态框 -->
    <div id="importModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>待上传数据</h2>
            <table id="dataTable"></table>
            <button id="backBtn">返回</button>
            <button id="uploadBtn">上传</button>
        </div>
    </div>
</body>
</html> 