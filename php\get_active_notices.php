<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取section参数
    $section = isset($_GET['section']) ? $_GET['section'] : '';
    // 获取project参数
    $project = isset($_GET['project']) ? $_GET['project'] : '';
    // 获取当前生效的通知
    $current_time = date('Y-m-d H:i:s');
    
        $sql = "SELECT n.*, COUNT(nf.id) as file_count 
                FROM notice n 
                LEFT JOIN notice_files nf ON n.id = nf.notice_id 
            WHERE (n.end_time IS NULL OR n.end_time > ?)";
    
    $params = [$current_time];
    $types = 's';
    
    // 添加section条件
    if (!empty($section)) {
        $sql .= " AND n.section IN (?, 'ALL') ";
        $params[] = $section;
        $types .= 's';
    }
    
    // 添加project条件
    if (!empty($project)) {
        $sql .= " AND n.project = ?";
        $params[] = $project;
        $types .= 's';
    }
    
    // 添加分组和排序
    $sql .= " GROUP BY n.id ORDER BY n.upload_time DESC";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("准备语句失败: " . $conn->error);
        }
    
    // 绑定参数
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
        $stmt->execute();
        $result = $stmt->get_result();

    if (!$result) {
        throw new Exception("查询失败: " . $conn->error);
    }
    
    $notices = [];
    while ($row = $result->fetch_assoc()) {
        // 确保 file_count 是数字类型
        $row['file_count'] = intval($row['file_count']);
        $notices[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $notices
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 