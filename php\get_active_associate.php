<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取section参数
    $section = isset($_GET['section']) ? $_GET['section'] : '';
    // 获取project参数
    $project = isset($_GET['project']) ? $_GET['project'] : '';
    
    // 准备SQL语句
    $sql = "    SELECT id, line, unit, phenomenon, analysis, measure, 
            problemtype, problempart, problemcode, status, updated_at
    FROM (select * from associatelist t1 
JOIN ( SELECT MAX(step) AS step1, section as section1, project as project1, line as line1, unit as unit1, phenomenon as phenomenon1, created_at as  created_at1
FROM associatelist 
GROUP BY section, project, line, unit, problemtype, phenomenon, problempart, problemcode, created_at ) t2 
ON t1.step = t2.step1 
AND t1.section = t2.section1
AND t1.project = t2.project1
AND t1.line = t2.line1 
AND t1.unit = t2.unit1 
AND t1.phenomenon = t2.phenomenon1 
AND t1.created_at = t2.created_at1) t3
    WHERE status = 'open'";
    
    // 如果section不为空，添加section条件
    if (!empty($section)) {
        $sql .= " AND section = ?";
    }
    
    // 如果project不为空，添加section条件
    if (!empty($project)) {
        $sql .= " AND project = ?";
    }
    
    // 添加排序和限制
    $sql .= " ORDER BY updated_at DESC LIMIT 10";
    
    // 准备和执行语句
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("准备语句失败: " . $conn->error);
    }
    
    // 根据参数数量构建bind_param
    if (!empty($section) && !empty($project)) {
        $stmt->bind_param('ss', $section, $project);
    } else if (!empty($section)) {
        $stmt->bind_param('s', $section);
    } else if (!empty($project)) {
        $stmt->bind_param('s', $project);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $issues = [];
    while ($row = $result->fetch_assoc()) {
        $issues[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $issues
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 