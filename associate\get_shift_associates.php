<?php
header('Content-Type: application/json'); //设置响应头为 JSON 格式
require_once '../php/db_config.php'; //引入数据库配置文件

try {
    // 获取查询参数，使用空合并运算符设置默认值
    $showtype = $_GET['showtype'] ?? '';

    $isbigalarm = $_GET['isbigalarm'] ?? '';//1
    $recorder = $_GET['recorder'] ?? '';

    $section = $_GET['section'] ?? '';
    $area = $_GET['area'] ?? '';
    $shift = $_GET['shift'] ?? '';//5
    $classes = $_GET['classes'] ?? '';
    $line = $_GET['line'] ?? '';
    $project = $_GET['project'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $problemtype = $_GET['problemtype'] ?? '';//10
    $needfollow = $_GET['needfollow'] ?? '';
    $status = $_GET['status'] ?? '';
    $keyword = $_GET['problemKeyword'] ?? '';
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';//15

    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10;
    
    // 构建WHERE子句
    $where = [];
    $params = [];
    $types = '';

    // 如果搜索的是日期
    if($showtype === '日期'){
        // 定义变量
        $dataList = [];  // 日期数据
        $Manpower = []; // 人力交接数据
        $Associate = []; // 交接数据
        $total= 0;  // 总条数
        $totalPages = 0;    //总页数

        if (!empty($section)) {
            $where[] = "section = ?";
            $params[] = $section;
            $types .= 's';
        }
        if (!empty($area)) {
            $where[] = "area = ?";
            $params[] = $area;
            $types .= 's';
        }
        if (!empty($shift)) {
            $where[] = "shift = ?";
            $params[] = $shift;
            $types .= 's';
        }
        // 分类 日期 
        if (!empty($classes)) {
            $where[] = "classes = ?";
            $params[] = $classes;
            $types .= 's';
        }
        

        // 获取人力数据
        $manpowerSql = "select id, date, section, area, shiftname, classes, mancount, mancountactual, 
                mancountextra, recorder, created_at, updated_at 
            from (select * from shift_manpower t1
            join( SELECT id as id2,date as date2,section as section2,area as area2,shiftname as shiftname2,
                classes as classes2,max(created_at) as created_at2
                from shift_manpower group by date, section, area, shiftname, classes) t2
            on t1.created_at = t2.created_at2) t3"; 
        if (!empty($where)) {
            $manpowerSql .= " WHERE " . implode(" AND ", $where);
        }
        $manpowerSql .= " ORDER BY date desc";
        // 添加分页参数 取给定日期加上给定了班次的数据，人力应该只有一条数据
        // $offset = ($page - 1) * $pageSize;
        // $params[] = $pageSize;
        // $params[] = $offset;
        // $types .= 'ii';

        $manpowerStmt = $conn->prepare($manpowerSql);
        if (!empty($params)) {
            $manpowerStmt->bind_param($types, ...$params);
        }
        $manpowerStmt->execute();
        // get_result：返回数据结果 fetch_assoc：返回行数
        $manpowerResult = $manpowerStmt->get_result(); 
        while ($row = $manpowerResult->fetch_assoc()) {
            $Manpower[] = $row;  
        }

        if (!empty($isbigalarm)) {
            $where[] = "isbigalarm = ?";
            $params[] = $isbigalarm;
            $types .= 's';
        }
        if (!empty($recorder)) {
            $where[] = "recorder = ?";
            $params[] = $recorder;
            $types .= 's';
        }

        
        if (!empty($line)) {
            $where[] = "line = ?";
            $params[] = $line;
            $types .= 's';
        }
        if (!empty($project)) {
            $where[] = "project = ?";
            $params[] = $project;
            $types .= 's';
        }
        if (!empty($unit)) {
            $where[] = "unit = ?";
            $params[] = $unit;
            $types .= 's';
        }
        if (!empty($problemtype)) {
            $where[] = "problemtype = ?";
            $params[] = $problemtype;
            $types .= 's';
        }
        if (!empty($needfollow)) {
            $where[] = "needfollow = ?";
            $params[] = $needfollow;
            $types .= 's';
        }
        if (!empty($status)) {
            $where[] = "status = ?";
            $params[] = $status;
            $types .= 's';
        }
        if (!empty($keyword)) {
            $where[] = "(phenomenon LIKE ? OR analysis LIKE ? OR measure LIKE ?)";
            $params[] = "%$keyword%";
            $params[] = "%$keyword%";
            $params[] = "%$keyword%";
            $types .= 'sss';
        }
        if (!empty($startDate)) {
            $where[] = "DATE(created_at) >= ?";
            $params[] = $startDate;
            $types .= 's';
        }
        if (!empty($endDate)) {
            $where[] = "DATE(created_at) <= ?";
            $params[] = $endDate;
            $types .= 's';
        }
        // 获取LD交接数据
        $associateSql ="select * from associatelist";
        if (!empty($where)) {
            $associateSql .= " WHERE " . implode(" AND ", $where);
        }
        $associateSql .= " ORDER BY problemtype";
        $associateStmt = $conn->prepare($associateSql);
        if (!empty($params)) {
            $associateStmt->bind_param($types, ...$params);
        }
        $associateStmt->execute();

        $associateResult = $associateStmt->get_result();

        while ($row = $associateResult->fetch_assoc()) {
            $Associate[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'Manpower' => $Manpower,
            'Associate' => $Associate
            
        ]);
       
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 