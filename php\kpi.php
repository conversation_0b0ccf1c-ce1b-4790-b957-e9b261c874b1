<?php
// 数据库连接配置
$serverName = "109.120.2.35";
$uid = "eqplink";
$pwd = "eqplink";


$connectionInfo = array(
    "Database" => "EQP_management",
    "Uid" => $uid,
    "PWD" => $pwd,
    "CharacterSet" => "UTF-8"
);
$conn = sqlsrv_connect($serverName, $connectionInfo);
if ($conn === false) {
    die(print_r(sqlsrv_errors(), true));
}

// 获取最近三个月的班次信息
$shiftQuery = "
    SELECT DISTINCT 
        CONVERT(varchar(10), date, 120) as date,
        shift as shift1,
        classes as classes1
    FROM bigdata.dbo.shift
    WHERE CONVERT(VARCHAR(7),date) >= CONVERT(VARCHAR(7),DATEADD(MM,-2,GETDATE()),21) AND classes != '休'
";

$stmt = sqlsrv_query($conn, $shiftQuery);
if ($stmt === false) {
    die(print_r(sqlsrv_errors(), true));
}

$leaders = [
    'A' => ['1F' => '沈阳',   '2F' => '张宝龙'],
    'B' => ['1F' => '王宪坤', '2F' => '张帅'],
    'C' => ['1F' => '赵亚洲', '2F' => '钱鹏']
];

// 存储计算结果
$resultData = [];

// 处理每个班次
while ($shiftRow = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
    $date = $shiftRow['date'];
    $shift = $shiftRow['shift1'];
    $classes = $shiftRow['classes1'];
    

    $kpiQuery = "
        SELECT
            floor,
            SUM([Plan]) as plans,
            SUM([Out]) as outs
        FROM (
            SELECT 
            *,
            CASE WHEN RIGHT([Line],2) > 10 THEN '2F' ELSE '1F' END as floor,
            CASE WHEN [HH] > 8 
                THEN CONVERT(VARCHAR(10), DATEADD(DD, -1, [Date]), 120)
                ELSE CONVERT(VARCHAR(10), [Date], 120)
            END AS DateOffset
        FROM [EQP_management].[dbo].[kpi]
            ) t
        WHERE DateOffset = ?
        AND Classes1 = ?
        AND [Plan] > 0
        GROUP BY floor
    ";
    
    $kpiParams = array($date, $classes);
    $kpiStmt = sqlsrv_query($conn, $kpiQuery, $kpiParams);
    
    while ($kpiRow = sqlsrv_fetch_array($kpiStmt, SQLSRV_FETCH_ASSOC)) {
        $floor = $kpiRow['floor'];
        $plans = (float)$kpiRow['plans'];
        $outs = (float)$kpiRow['outs'];
        
        // 计算KPI
        $kpi = ($outs >= $plans) ? 100 : round(($outs / $plans) * 100, 2);
        
        // 匹配负责人
        $leader = $leaders[$shift][$floor] ?? '未知';
        
        // 存储结果
        $resultData[] = [
            'date'       => $date,
            'shift'      => $shift,
            'classes'    => $classes,
            'floor'      => $floor,
            'plans'      => $plans,
            'outs'       => $outs,
            'kpi'        => $kpi,
            'leader'     => $leader
        ];
    }
    sqlsrv_free_stmt($kpiStmt);
}

// 关闭连接
sqlsrv_free_stmt($stmt);
sqlsrv_close($conn);

// 输出
// echo "<table border='1'>
//     <tr>
//         <th>日期</th>
//         <th>班次</th>
//         <th>班别</th>
//         <th>楼层</th>
//         <th>计划量</th>
//         <th>完成量</th>
//         <th>KPI</th>
//         <th>负责人</th>
//     </tr>";

// foreach ($resultData as $row) {
//     echo "<tr>
//         <td>{$row['date']}</td>
//         <td>{$row['shift']}</td>
//         <td>{$row['classes']}</td>
//         <td>{$row['floor']}</td>
//         <td>{$row['plans']}</td>
//         <td>{$row['outs']}</td>
//         <td>{$row['kpi']}</td>
//         <td>{$row['leader']}</td>
//     </tr>";
// }

// echo "</table>";

?>