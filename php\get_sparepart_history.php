<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取备品ID
    $sparepartId = isset($_GET['sparepart_id']) ? intval($_GET['sparepart_id']) : 0;
    
    $whereConditions = [];
    $params = [];
    $types = '';
    
    // 当传入有效ID时添加查询条件
    if ($sparepartId > 0) {
        $whereConditions[] = "sparepart_id = ?";
        $params[] = $sparepartId;
        $types .= 'i';
    }
    
    // 构建WHERE子句
    $whereClause = !empty($whereConditions) 
        ? "WHERE " . implode(" AND ", $whereConditions) 
        : "";
    
    // 查询备品履历
    $sql = "SELECT * FROM sparepart_history $whereClause ORDER BY change_date DESC, id DESC";
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $history = [];
    while ($row = $result->fetch_assoc()) {
        // 格式化日期为YYYY-MM-DD格式
        $date = new DateTime($row['change_date']);
        $row['formatted_date'] = $date->format('Y-m-d');
        $history[] = $row;
    }
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => $history
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 