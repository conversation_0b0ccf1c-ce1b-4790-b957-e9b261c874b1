<?php
header('Content-Type: application/json');

try {
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    if (!isset($data['message'])) {
        throw new Exception('Missing message parameter');
    }

    // API配置
    $apiKey = 'd1da5c9f-c48a-48b3-a8af-5f9d378cbf2c';
    $apiUrl = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

    // 准备请求数据
    $requestData = [
        // 'model' => 'ep-20250114221038-z4psg',//Doubao Pro 256k
        //'model' => 'ep-20250216181150-nb6ds',//DeepSeek-R1
        'model' => 'ep-20250311073147-4tjhv',//Doubao 1.5 Pro
        'messages' => [
            [
                'role' => 'system',
                'content' => ''
            ],
            [
                'role' => 'user',
                'content' => $data['message']
            ]
        ]
    ];

    // 发起API请求
    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode !== 200) {
        throw new Exception('API request failed with status code: ' . $httpCode);
    }

    $result = json_decode($response, true);
    if (isset($result['choices'][0]['message']['content'])) {
        echo json_encode([
            'success' => true,
            'response' => $result['choices'][0]['message']['content']
        ]);
    } else {
        throw new Exception('Invalid response format from AI API');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 