<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'db_config.php';

try {
    // 检查是否有数据
    $isFormData = !empty($_FILES) || !empty($_POST);

    if ($isFormData) {
        // 处理FormData
        if (!isset($_POST['fault_id']) || !isset($_POST['status'])) {
            throw new Exception('缺少必要参数');
        }

        $fault_id = intval($_POST['fault_id']);
        $status = $_POST['status'];

        // 获取其他更新字段（可选）
        $phenomenon = isset($_POST['phenomenon']) ? $_POST['phenomenon'] : null;
        $analysis = isset($_POST['analysis']) ? $_POST['analysis'] : null;
        $measures = isset($_POST['measures']) ? $_POST['measures'] : null;
    } else {
        // 处理JSON数据（保持向后兼容）
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['fault_id']) || !isset($data['status'])) {
        throw new Exception('缺少必要参数');
    }

    $fault_id = intval($data['fault_id']);
    $status = $data['status'];

        // 获取其他更新字段（可选）
        $phenomenon = isset($data['phenomenon']) ? $data['phenomenon'] : null;
        $analysis = isset($data['analysis']) ? $data['analysis'] : null;
        $measures = isset($data['measures']) ? $data['measures'] : null;
    }

    // 验证状态值
    if (!in_array($status, ['OPEN', 'CLOSE'])) {
        throw new Exception('无效的状态值');
    }
    
    // 准备SQL和参数
    $set_fields = ["status = ?"]; 
    $params = [];
    $types = "s";
    $params[] = &$status;

    // 动态添加
    if ($phenomenon !== null) {
        $set_fields[] = "phenomenon = ?";
        $params[] = &$phenomenon;
        $types .= "s";
    }
    
    if ($analysis !== null) {
        $set_fields[] = "analysis = ?";
        $params[] = &$analysis;
        $types .= "s";
    }
    
    if ($measures !== null) {
        $set_fields[] = "measures = ?";
        $params[] = &$measures;
        $types .= "s";
    }
    
    $params[] = &$fault_id;
    $types .= "i";
    
    // 构建SQL语句
    $sql = "UPDATE alarmlist SET " . implode(", ", $set_fields) . " WHERE id = ?";
    $stmt = $conn->prepare($sql);
    
    // 绑定参数类型
    array_unshift($params, $types);
    
    call_user_func_array([$stmt, 'bind_param'], $params);
    
    if (!$stmt->execute()) {
        throw new Exception('更新失败: ' . $stmt->error);
    }

    // 检查是否有文件上传
    $hasFileUpload = $isFormData && !empty($_FILES['files']);

    // 如果没有文件上传且没有记录被更新，则报错
    if (!$hasFileUpload && $stmt->affected_rows === 0) {
        throw new Exception('未找到要更新的记录');
    }

    // 如果只有文件上传而没有其他字段更新，需要验证故障记录是否存在
    if ($hasFileUpload && $stmt->affected_rows === 0) {
        // 验证故障记录是否存在
        $checkSql = "SELECT id FROM alarmlist WHERE id = ?";
        $checkStmt = $conn->prepare($checkSql);
        if (!$checkStmt) {
            throw new Exception("准备验证语句失败: " . $conn->error);
        }
        $checkStmt->bind_param('i', $fault_id);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();

        if ($checkResult->num_rows === 0) {
            throw new Exception('未找到要更新的故障记录');
        }
    }

    // 处理文件上传（如果有）
    if ($hasFileUpload) {
        $uploadDir = '../uploads/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        foreach ($_FILES['files']['tmp_name'] as $key => $tmp_name) {
            if ($_FILES['files']['error'][$key] === UPLOAD_ERR_OK) {
                $fileName = $_FILES['files']['name'][$key];
                $fileSize = $_FILES['files']['size'][$key];

                // 生成唯一文件名
                $safeFileName = preg_replace('/[#<>:"\/\\|?*]/', '_', $fileName);
                $uniqueName = uniqid() . '_' . $safeFileName;
                $filePath = $uploadDir . $uniqueName;

                if (move_uploaded_file($tmp_name, $filePath)) {
                    // 保存文件信息到数据库
                    $sql = "INSERT INTO fault_files (fault_id, file_name, file_path, file_size, upload_time)
                           VALUES (?, ?, ?, ?, NOW())";
                    $stmt = $conn->prepare($sql);
                    if (!$stmt) {
                        throw new Exception("准备文件插入语句失败: " . $conn->error);
                    }
                    $stmt->bind_param('issi', $fault_id, $fileName, $uniqueName, $fileSize);
                    if (!$stmt->execute()) {
                        throw new Exception("保存文件信息失败: " . $stmt->error);
                    }
                } else {
                    throw new Exception("文件上传失败: " . $fileName);
                }
            }
        }
    }

    echo json_encode([
        'success' => true,
        'message' => '更新成功'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 