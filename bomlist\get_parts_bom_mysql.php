<?php
header('Content-Type: application/json');
require_once '../php/db_config.php';

try {
    // 获取查询参数
    $section = $_GET['section'] ?? '';
    $project = $_GET['project'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $partname = $_GET['partname'] ?? '';
    $parttype = $_GET['parttype'] ?? '';
    $partbrand = $_GET['partbrand'] ?? '';
    $partmodel = $_GET['partmodel'] ?? '';
    $useposition = $_GET['useposition'] ?? '';
    $partlevel = $_GET['partlevel'] ?? '';
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10;

    // 构建WHERE子句
    $where = [];
    $params = [];
    $types = '';

    if (!empty($section)) {
        $where[] = "section = ?";
        $params[] = $section;
        $types .= 's';
    }

    if (!empty($project)) {
        $where[] = "project = ?";
        $params[] = $project;
        $types .= 's';
    }

    if (!empty($unit)) {
        $where[] = "unit = ?";
        $params[] = $unit;
        $types .= 's';
    }

    if (!empty($partname)) {
        $where[] = "partname = ?";
        $params[] = $partname;
        $types .= 's';
    }

    if (!empty($parttype)) {
        $where[] = "parttype = ?";
        $params[] = $parttype;
        $types .= 's';
    }

    if (!empty($partbrand)) {
        $where[] = "partbrand = ?";
        $params[] = $partbrand;
        $types .= 's';
    }

    if (!empty($partmodel)) {
        $where[] = "partmodel = ?";
        $params[] = $partmodel;
        $types .= 's';
    }

    if (!empty($useposition)) {
        $where[] = "useposition = ?";
        $params[] = $useposition;
        $types .= 's';
    }

    if (!empty($partlevel)) {
        $where[] = "partlevel = ?";
        $params[] = $partlevel;
        $types .= 's';
    }

    // if (!empty($keyword)) {
    //     $where[] = "(phenomenon LIKE ? OR analysis LIKE ? OR measure LIKE ?)";
    //     $params[] = "%$keyword%";
    //     $params[] = "%$keyword%";
    //     $params[] = "%$keyword%";
    //     $types .= 'sss';
    // }

    // if (!empty($startDate)) {
    //     $where[] = "DATE(created_at) >= ?";
    //     $params[] = $startDate;
    //     $types .= 's';
    // }

    // if (!empty($endDate)) {
    //     $where[] = "DATE(created_at) <= ?";
    //     $params[] = $endDate;
    //     $types .= 's';
    // }

    // 计算总记录数
    //$countSql = "SELECT COUNT(*) as total FROM associatelist";
    $countSql = "SELECT COUNT(*) as total FROM parts_bom";
    if (!empty($where)) {
        $countSql .= " WHERE " . implode(" AND ", $where);
    }

    $countStmt = $conn->prepare($countSql);
    if (!empty($params)) {
        $countStmt->bind_param($types, ...$params);
    }
    $countStmt->execute();
    $totalResult = $countStmt->get_result()->fetch_assoc();
    $total = $totalResult['total'];

    // 计算总页数
    if($pageSize > 0)
        $totalPages = ceil($total / $pageSize);
    else
        $totalPages = $total;
    $offset = ($page - 1) * $pageSize;

    // 获取数据
    // $sql = "SELECT a.*, 
    //         (SELECT COUNT(*) FROM associate_files WHERE associate_id = a.id) as image_count 
    //         FROM associatelist a";
    //$sql = "select id,section,classes,line,project,unit,problemtype,phenomenon,analysis,measure,problempart,
    //problempart,problemcode,needfollow,towho,status,step,recorder,created_at,updated_at 
    $sql = "select * from parts_bom";
    if (!empty($where)) {
        $sql .= " WHERE " . implode(" AND ", $where);
    }

    //
    if($pageSize > 0){
        $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
        // 添加分页参数
        $params[] = $pageSize;
        $params[] = $offset;
        $types .= 'ii';
    }
    else{
        $sql .= " ORDER BY created_at DESC";
    }


    $stmt = $conn->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode([
        'success' => true,
        'data' => $data,
        'total' => $total,
        'totalPages' => $totalPages,
        'currentPage' => $page
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 