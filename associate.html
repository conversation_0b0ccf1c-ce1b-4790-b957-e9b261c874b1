<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache"> <meta http-equiv="Expires" content="0">
    <title>现场交接 - 设备综合管理</title>
    <link rel="stylesheet" href="associate/style.css">
    <script src="js/tabs.js" defer></script>
    <script src="associate/associate2.js" defer></script>
    <script src="js/echarts.js" defer></script>
    <script src="js/xlsx.js" defer></script>
    <script src="js/html2canvas.js" defer></script>
    <script src="js/jquery-1.11.3.min.js" defer></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'search':
                        switchTab('asssearch');
                        break;
                    case 'register':
                        switchTab('assregister');
                        break;
                    case 'analysis':
                        switchTab('assAnalysis');
                        break;
                    default:
                        switchTab('asssearch');
                }
            }
        });
    </script>
</head>
<body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <div class="tabs" style="display: none;">
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('asssearch')">交接查询</button>
                <button class="tab-button" onclick="switchTab('assregister')">交接登录</button>
                <button class="tab-button" onclick="switchTab('assAnalysis')">交接分析</button>
            </div>
        </div>
            
        <!-- 交接查询选项卡 -->
        <div id="asssearchTab" class="tab-content active">
            <div class="asssearch">
                <form id="asssearchForm" action="#" method="get">
                    <div class="asssearch-row">
                        
                        <div id="projectSelect">
                            <label for="asssearch-project">工程</label>
                            <select id="asssearch-project" name="project" style="width: 90px;">
                            </select>
                        </div>

                        <label for="asssearch-line">LINE</label>
                        <select id="asssearch-line" name="line" style="width: 90px;">
                        </select>
                        
                        <label for="asssearch-unit">UNIT</label>
                        <select id="asssearch-unit" name="unit" style="width: 90px;">
                        </select>

                        <label for="asssearch-problemtype">分类</label>
                        <select id="asssearch-problemtype" name="problemtype" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="故障-硬件">故障-硬件</option>
                            <option value="故障-程序">故障-程序</option>
                            <option value="品质调试">品质调试</option>
                            <option value="切机">切机</option>
                            <option value="Tact Time">Tact Time</option>
                            <option value="原材不良">原材不良</option>
                            <option value="HD">HD</option>
                            <option value="其他">其他</option>  
                        </select>

                        <label for="asssearch-needfollow">需要跟进</label>
                        <select id="asssearch-needfollow" name="needfollow" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="是">是</option>
                            <option value="否">否</option>
                        </select>

                        <label for="asssearch-isbigalarm">是否1H以上大故障</label>
                        <select id="asssearch-isbigalarm" name="isbigalarm" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="是">是</option>
                            <option value="否">否</option>
                        </select>

                        <div id="towhoContainer" style="display: none";>
                            <label for="asssearch-towho">谁跟进</label>
                            <select id="asssearch-towho" name="towho" style="width: 90px;">
                                <option value="">请选择</option>
                                <option value="严先维">严先维</option>
                                <option value="项腾飞">项腾飞</option>
                                <option value="朱庆凯">朱庆凯</option>
                                <option value="胡文平">胡文平</option>
                                <option value="邢丹">邢丹</option>
                                <option value="袁阳光">袁阳光</option>
                                <option value="王振">王振</option>
                                <option value="赵伦">赵伦</option>
                                <option value="沈凯雷">沈凯雷</option>
                                <option value="陈振振">陈振振</option>
                                <option value="庞海龙">庞海龙</option>
                                <option value="左伟">左伟</option>
                                <option value="卞向宇">卞向宇</option>
                                <option value="杜志伟">杜志伟</option>
                                <option value="杨鑫">杨鑫</option>
                                <option value="苟振福">苟振福</option>
                                <option value="马庆森">马庆森</option>
                                <option value="徐德松">徐德松</option>
                                <option value="郁程阳">郁程阳</option>
                                <option value="朱从海">朱从海</option>
                                <option value="李杰">李杰</option>
                                <option value="冯殿超">冯殿超</option>
                                <option value="薛欢">薛欢</option>
                                <option value="Line Leader">Line Leader</option>
                                <option value="PM">PM</option>
                                <option value="O">O</option>
                                <option value="A">A</option>
                                <option value="B">B</option>
                                <option value="C">C</option>
                            </select>
                        </div>
                        

                        <label for="asssearch-status">是否结案</label>
                        <select id="asssearch-status" name="status" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="open">open</option>
                            <option value="close">close</option>
                        </select>

                        <label for="asssearch-recorder">交接人</label>
                        <input type="text" id="asssearch-recorder" name="recorder" style="width: 60px;">
                        

                        <label for="asssearch-problemKeyword">关键词</label>
                        <input type="text" id="asssearch-problemKeyword" name="problemKeyword" placeholder="请输入关键词查询" style="width: 160px;">
                        
                        <label for="asssearch-start-date">日期</label>
                        <input type="date" id="asssearch-start-date" name="start_date">
                        
                        <label for="asssearch-end-date">~</label>
                        <input type="date" id="asssearch-end-date" name="end_date">
                        
                        <div class="radio-group">
                            <label>区分：</label>
                            <label>
                                <input class="asssearch-showtype" type="radio" name="showtype" value="故障" checked id="showtype-alarm" >
                                <span>故障</span>
                            </label>
                            <label>
                                <input class="asssearch-showtype" type="radio" name="showtype" value="日期" id="showtype-daily" >
                                <span>日期</span>
                            </label>
                            <label>
                                <input class="asssearch-showtype" type="radio" name="showtype" value="人力" id="showtype-man" >
                                <span>人力</span>
                            </label>
                        </div>

                        <div id="shiftContainer" style="display: none";>
                            <label for="asssearch-shift">班次</label>
                            <select id="asssearch-shift" name="shift" style="width: 90px;">
                                <option value="" selected>请选择</option>
                                <option value="LD">LD</option>
                                <option value="LG">LG</option>
                            </select>
                            <label for="asssearch-area">领班</label>
                            <select id="asssearch-area" name="area" style="width: 90px;">
                                <!-- <option value="">请选择</option>
                                <option value="1F">1F</option>
                                <option value="2F">2F</option>
                                <option value="CP">CP</option>
                                <option value="OLB">OLB</option> -->
                            </select>
                        </div>

                        <div class="button-group">
                            <button type="submit" class="asssearch-btn">查询</button>
                            <button type="reset" class="assreset-btn">重置</button>
                            <div class="btn-user-defined">
                                <button type="button" class="assDownload-btn">导出</button>
                                <button type="button" class="assTReport-btn">T信通报</button>
                                <button type="button" class="assEmailReport-btn">邮件发送</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="asslist" id="asslist-content">
                <!-- <table class="data-table" id="assSearchTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>班次</th>
                            <th>LINE</th>
                            <th>UNIT</th>
                            <th>现象</th>
                            <th>原因</th>
                            <th>处理内容</th>
                            <th>故障分类</th>
                            <th>故障部件</th>
                            <th>故障代码</th>
                            <th>需要跟进</th>
                            <th>谁跟进</th>
                            <th>是否结案</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table> -->
            </div>

            <!-- 在表格后添加分页控件 -->
            <div class="pagination">
                <div class="pagination-info">
                    共 <span class="total-count">0</span> 条记录，
                    每页 <select class="page-size">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select> 条
                </div>
                <div class="pagination-controls">
                    <button class="btn-first-page">首页</button>
                    <button class="btn-prev-page">上一页</button>
                    <span class="page-info">
                        第 <input type="number" class="current-page" min="1"> 页，
                        共 <span class="total-pages">0</span> 页
                    </span>
                    <button class="btn-next-page">下一页</button>
                    <button class="btn-last-page">末页</button>
                </div>
            </div>
        </div>

        <!-- 交接登录选项卡 -->
        <div id="assregisterTab" class="tab-content">
            <div class="assregister">
                <form action="#" method="post" class="assregister-form">
                    <div class="shift-manpower">
                        <div class="shift-manpower-row">
                            <!-- <label for="assShift-date">日期</label>
                            <input type="date" id="assShift-date" name="shift_manpower_date" required> -->
    
                            <label for="assShift-area">领班</label>
                            <select id="assShift-area" name="area" style="width: 90px;" required>
                                <!-- <option value="">请选择</option>
                                <option value="1F">1F</option>
                                <option value="2F">2F</option>
                                <option value="CP">CP</option>
                                <option value="OLB">OLB</option> -->
                            </select>

                            <label for="assShift-shift">班组</label>
                            <select id="assShift-shift" name="shift" style="width: 90px;" required>
                                <option value="">请选择</option>
                                <option value="A">A</option>
                                <option value="B">B</option>
                                <option value="C">C</option>
                            </select>
    
                            <label for="assShift-classes">班次</label>
                            <select id="assShift-classes" name="classes" style="width: 90px;" required>
                                <option value="">请选择</option>
                                <option value="LD">LD</option>
                                <option value="LG">LG</option>
                            </select>
    
                            <label for="assShift-manCount">本班应到人数</label>
                            <input type="number" id="assShift-manCount" name="assShift-manCount" style="width: 80px;">
    
                            <label for="assShift-manCountActual">本班实到人数</label>
                            <input type="number" id="assShift-manCountActual" name="assShift-manCountActual" style="width: 80px;">
    
                            <label for="assShift-manCountExtra">加班人数</label>
                            <input type="number" id="assShift-manCountExtra" name="assShift-manCountExtra" style="width: 80px;">
                        </div>
                    </div>
                    <!-- 表头行 -->
                    <table class="register-table">
                        <tr>
                            <!-- <th>班次</th> -->
                            <th>UNIT</th>
                            <th>LINE</th>
                            <th>故障分类</th>
                            <th>现象</th>
                            <th>原因</th>
                            <th>处理内容</th>
                            <!-- <th>故障部件</th> -->
                            <!-- <th>故障代码</th> -->
                            <th>需要跟进</th>
                            <th>谁跟进</th>
                            <th>大故障</th>
                            <th>故障图片</th>
                            <th>操作</th>
                        </tr>
                        <!-- 初始10行空表格 -->
                        <tbody id="registerTableBody">
                            <!-- 这里将通过JavaScript动态生成10行空表格 -->
                        </tbody>
                    </table>

                    <div class="form-actions">
                        <button type="button" class="btn-add-rows" id="btn-add-rows">添加更多行</button>
                        <button type="submit" class="btn-submit" id="btn-submit">提交</button>
                        <button type="reset" class="btn-reset" id="btn-reset">重置</button>
                        <p style="color: red;">*请删除多余空行后提交！</p>
                    </div>
                </form>
            </div>
        </div>

        <!-- 交接分析选项卡 -->
        <div id="assAnalysisTab" class="tab-content">
            <div class="assAnalysis">
                <form id="assAnalysisForm" action="#" method="get">
                    <div class="assAnalysis-row">
                        
                        <div id="projectSelect">
                            <label for="assAnalysis-project">工程</label>
                            <select id="assAnalysis-project" name="project" style="width: 90px;">
                            </select>
                        </div>

                        <label for="assAnalysis-line">LINE</label>
                        <select id="assAnalysis-line" name="line" style="width: 90px;">
                        </select>
                        
                        <label for="assAnalysis-unit">UNIT</label>
                        <select id="assAnalysis-unit" name="unit" style="width: 90px;">
                        </select>

                        <label for="assAnalysis-problemtype">故障分类</label>
                        <select id="assAnalysis-problemtype" name="problemtype" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="一般">一般</option>
                            <option value="硬件干涉">硬件干涉</option>
                            <option value="程序bug">程序bug</option>
                            <option value="部件损坏">部件损坏</option>
                        </select>

                        <label for="assAnalysis-needfollow">需要跟进</label>
                        <select id="assAnalysis-needfollow" name="needfollow" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="是">是</option>
                            <option value="否">否</option>
                        </select>

                        <label for="assAnalysis-status">是否结案</label>
                        <select id="assAnalysis-status" name="status" style="width: 90px;">
                            <option value="">请选择</option>
                            <option value="open">open</option>
                            <option value="close">close</option>
                        </select>

                        <label for="assAnalysis-start-date">日期</label>
                        <input type="date" id="assAnalysis-start-date" name="start_date">
                        
                        <label for="assAnalysis-end-date">~</label>
                        <input type="date" id="assAnalysis-end-date" name="end_date">

                        <label for="assAnalysis-tableShow">图表呈现</label>
                        <select id="assAnalysis-tableShow" name="tableShow">
                            <option value="bar">柱状图</option>
                            <option value="line">折线图</option>
                            <option value="pie">饼图</option>
                        </select>

                        <label for="assAnalysis-tableFor">呈现对象</label>
                        <select id="assAnalysis-tableFor" name="tableFor">
                            <option value="forLine">LINE</option>
                            <option value="forUnit">UNIT</option>
                        </select>

                        <div class="button-group">
                            <button type="submit" class="assAnalysis-btn">查询</button>
                            <button type="reset" class="assAnalysisReset-btn">重置</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="assAnalysisECharts">
                <!-- 为 ECharts 准备一个定义了宽高的 DOM -->
                <div id="EChartsDOM" style="width: 800px;height:600px;"></div>
            </div>
        </div>
    </div>

    <!-- 在交接列表表格后添加 处理 弹窗结构 -->
    <div id="associateDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>标题,line unit 故障现象</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <table class="detail-table">
                    <tr>
                        <th>科室</th>
                        <th>LINE</th>
                        <th>UNIT</th>
                        <th>故障现象</th>
                        <!-- <th>故障原因</th> -->
                        <!-- <th>处理内容</th> -->
                        <th>故障部件</th>
                        <th>故障代码</th>
                        <th>需要跟进</th>
                        <th>发生时间</th>
                        <th>交接担当</th>
                    </tr>
                    <tr>
                        <!-- 这里将通过JavaScript动态生成 -->
                    </tr>
                </table>

                <div class="detail-sections" id="detail-sections">
                    
                    <div class="detail-section">
                        <h3>故障原因</h3>
                        <div class="section-content">
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>处理内容</h3>
                        <div class="section-content">
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>是否结案</h3>
                        <div class="section-content">
                            
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>相关附件</h3>
                        
                        <div class="section-content">
                            <!-- 附件将在这里动态显示 -->
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn-return">返回</button>
                    <!-- <button class="btn-modify" id="modal-btn-modify">修改</button> -->
                     <div class="modal-btn-modify">

                     </div>
                </div>
                <!-- 历史履历显示在下方 -->
                <div class="history" id="history" style="overflow: scroll; max-height: 250px;">
                    <div class="history-title">
                        <h3 onclick="associateManager.historyTitleClick(associateManager.bShowHistory)">点击历史履历信息展开</h3>
                    </div>
                    <div class="history-body" id="history-body" style="display: none;">
                        <table class="history-body-table" id="history-body-table">
                            <thead>
                                <tr>
                                    <th>序号</th>
                                    <th>故障原因</th>
                                    <th>处理内容</th>
                                    <th>发生时间</th>
                                    <th>交接担当</th>
                                    <th>相关附件</th>
                                </tr>
                            </thead>
                            
                            <tbody>
                                <!-- 数据将通过 JavaScript 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
          </div>
      </div>
    </div>
</body>
</html> 