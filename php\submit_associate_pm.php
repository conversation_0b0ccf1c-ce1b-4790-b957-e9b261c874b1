<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
require_once 'db_config.php';

// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // 开始事务
    $conn->begin_transaction();

    // 获取表单数据数组
    $sections = $_POST['section'] ?? [];
    $classess = $_POST['classes'] ?? [];
    $lines = $_POST['line'] ?? [];
    $projects = $_POST['project'] ?? [];
    $units = $_POST['unit'] ?? [];
    $problemtypes = $_POST['problemtype'] ?? [];
    $phenomena = $_POST['phenomenon'] ?? [];
    $analyses = $_POST['analysis'] ?? [];
    $measures = $_POST['measure'] ?? [];
    $problemparts = $_POST['problempart'] ?? [];
    $problemcodes = $_POST['problemcode'] ?? [];
    $needfollows = $_POST['needfollow'] ?? [];
    $towhos = $_POST['towho'] ?? [];
    $statuses = $_POST['status'] ?? [];
    $recorder = $_POST['recorder'] ?? '';

    // 处理每一行数据
    $successCount = 0;
    $errors = [];

    for ($i = 0; $i < count($lines); $i++) {
        // 跳过空行
        if (empty($lines[$i]) || empty($units[$i])) continue;

        $sql = "INSERT INTO associatelist (
            section, classes, line, project, unit, problemtype, phenomenon, analysis, measure,
            problempart, problemcode, needfollow, towho, status, recorder
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("准备语句失败: " . $conn->error);
        }

        $bindResult = $stmt->bind_param('sssssssssssssss',
            $sections[$i],
            $classess[$i],
            $lines[$i],
            $projects[$i],
            $units[$i],
            $problemtypes[$i],
            $phenomena[$i],
            $analyses[$i],
            $measures[$i],
            $problemparts[$i],
            $problemcodes[$i],
            $needfollows[$i],
            $towhos[$i],
            $statuses[$i],
            $recorder
        );

        if (!$bindResult) {
            throw new Exception("参数绑定失败: " . $stmt->error);
        }

        if (!$stmt->execute()) {
            throw new Exception("执行失败: " . $stmt->error);
        }

        $associate_id = $stmt->insert_id;
        
        // 处理图片上传（如果有的话）
        if (isset($_FILES['files']) && 
            isset($_FILES['files']['name'][$i]) && 
            !empty($_FILES['files']['name'][$i][0])) { // 检查是否有实际文件上传
            
            $files = $_FILES['files'];
            foreach ($files['name'][$i] as $j => $fileName) {
                if ($files['error'][$i][$j] === UPLOAD_ERR_OK) {
                    $fileType = $files['type'][$i][$j];
                    $fileTmpName = $files['tmp_name'][$i][$j];
                    $fileSize = $files['size'][$i][$j];
                    
                    // 验证文件类型
                    if (!in_array($fileType, ['image/jpeg', 'image/png', 'image/gif'])) {
                        continue; // 跳过不支持的文件类型
                    }
                    
                    // 生成唯一文件名
                    $uniqueName = uniqid() . '_' . $fileName;
                    $uploadDir = '../uploads/associates/';
                    
                    // 创建上传目录
                    if (!file_exists($uploadDir)) {
                        if (!mkdir($uploadDir, 0777, true)) {
                            throw new Exception("创建上传目录失败");
                        }
                    }
                    
                    $filePath = $uploadDir . $uniqueName;
                    
                    // 移动上传的文件
                    if (move_uploaded_file($fileTmpName, $filePath)) {
                        // 保存文件信息到数据库
                        $fileSql = "INSERT INTO associate_files (
                            associate_id, file_name, file_path, file_type, file_size
                        ) VALUES (?, ?, ?, ?, ?)";
                        
                        $fileStmt = $conn->prepare($fileSql);
                        if ($fileStmt) {
                            $fileBindResult = $fileStmt->bind_param('isssi',
                                $associate_id,
                                $fileName,
                                $uniqueName,
                                $fileType,
                                $fileSize
                            );
                            
                            if ($fileBindResult) {
                                $fileStmt->execute();
                            }
                            $fileStmt->close();
                        }
                    }
                }
            }
        }
        
        $successCount++;
        $stmt->close();
    }

    // 提交事务
    $conn->commit();

    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => true,
        'message' => "成功提交 {$successCount} 条记录",
        'count' => $successCount
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 回滚事务
    if (isset($conn) && $conn->ping()) {
        $conn->rollback();
    }
    
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error' => $e->getTraceAsString()
    ], JSON_UNESCAPED_UNICODE);
}

// 关闭连接
if (isset($conn)) {
    $conn->close();
}
?> 