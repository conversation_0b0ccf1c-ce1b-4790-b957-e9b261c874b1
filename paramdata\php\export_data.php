<?php
/**
 * 数据导出API
 * 支持CSV格式导出监控数据
 */

require_once 'config.php';

try {
    // 获取请求参数
    $device_mark = sanitizeInput($_GET['device_mark'] ?? '');
    $model_mark = sanitizeInput($_GET['model_mark'] ?? '');
    $param_name = sanitizeInput($_GET['param_name'] ?? '');
    $time_range = sanitizeInput($_GET['time_range'] ?? '24', 'int');
    $start_time = sanitizeInput($_GET['start_time'] ?? '');
    $end_time = sanitizeInput($_GET['end_time'] ?? '');
    $export_format = sanitizeInput($_GET['export'] ?? 'csv');
    
    // 验证必要参数
    if (empty($param_name)) {
        sendResponse(false, null, '请选择要导出的项目名', 400);
    }
    
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // 构建查询条件
    $whereConditions = ['param_name = :param_name'];
    $params = [':param_name' => $param_name];
    
    if (!empty($device_mark)) {
        $whereConditions[] = 'device_mark = :device_mark';
        $params[':device_mark'] = $device_mark;
    }
    
    if (!empty($model_mark)) {
        $whereConditions[] = 'model_mark = :model_mark';
        $params[':model_mark'] = $model_mark;
    }
    
    // 时间范围处理
    if (!empty($start_time) && !empty($end_time)) {
        $whereConditions[] = 'trigger_time_new BETWEEN :start_time AND :end_time';
        $params[':start_time'] = $start_time;
        $params[':end_time'] = $end_time;
    } else {
        $whereConditions[] = 'trigger_time_new >= DATE_SUB(NOW(), INTERVAL :time_range HOUR)';
        $params[':time_range'] = $time_range;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 查询数据
    $query = "
        SELECT 
            device_mark as '设备名',
            model_mark as '数据分类',
            param_name as '项目名',
            param_value as '项目值',
            param_spec as '项目基准',
            trigger_time_new as '上传时间',
            CASE WHEN param_value > param_spec THEN '超标' ELSE '正常' END as '状态'
        FROM param_data 
        WHERE {$whereClause}
        ORDER BY trigger_time_new DESC
        LIMIT 50000
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $data = $stmt->fetchAll();
    
    if (empty($data)) {
        sendResponse(false, null, '没有找到符合条件的数据');
    }
    
    // 根据导出格式处理
    if ($export_format === 'csv') {
        exportCSV($data, $param_name);
    } else {
        sendResponse(false, null, '不支持的导出格式');
    }
    
} catch (Exception $e) {
    logMessage("数据导出失败: " . $e->getMessage(), 'ERROR');
    sendResponse(false, null, '数据导出失败: ' . $e->getMessage(), 500);
}

/**
 * 导出CSV格式数据
 */
function exportCSV($data, $param_name) {
    $filename = "equipment_data_{$param_name}_" . date('Y-m-d_H-i-s') . '.csv';
    
    // 设置HTTP头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Expires: 0');
    
    // 输出UTF-8 BOM，确保Excel正确显示中文
    echo "\xEF\xBB\xBF";
    
    // 创建文件句柄
    $output = fopen('php://output', 'w');
    
    if (!empty($data)) {
        // 输出表头
        $headers = array_keys($data[0]);
        fputcsv($output, $headers);
        
        // 输出数据行
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
    }
    
    fclose($output);
    
    // 记录导出日志
    logMessage("数据导出成功: {$filename}, 记录数: " . count($data));
    
    exit;
}
?>
