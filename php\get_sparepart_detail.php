<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取备品ID
    $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    if ($id <= 0) {
        throw new Exception("无效的备品ID");
    }
    
    // 查询备品信息
    $sql = "SELECT * FROM sparepartlist WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        throw new Exception("未找到指定备品");
    }
    
    $sparepart = $result->fetch_assoc();
    
    // 查询附件信息
    $sql = "SELECT * FROM sparepart_files WHERE sparepart_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    $stmt->execute();
    $filesResult = $stmt->get_result();
    
    $files = [];
    while ($file = $filesResult->fetch_assoc()) {
        // 检查文件是否存在
        $filePath = '../uploads/spareparts/' . $file['file_path'];
        $file['file_exists'] = file_exists($filePath);
        
        // 如果是图片，检查文件是否可读
        if (strpos($file['file_type'], 'image/') === 0) {
            $file['readable'] = is_readable($filePath);
        }
        
        $files[] = $file;
    }
    
    // 添加文件信息到备品数据
    $sparepart['files'] = $files;
    
    // 返回结果
    echo json_encode([
        'success' => true,
        'data' => $sparepart
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 