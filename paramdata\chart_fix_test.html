<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表重复加载修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
        }
        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .control-group {
            display: flex;
            flex-direction: column;
        }
        .control-group label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        .control-group select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            background-color: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }
        .test-button:hover {
            background-color: #5a6fd8;
        }
        .status-display {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 6px;
            margin-top: 1rem;
            position: relative;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 0.5rem;
            background-color: #e9ecef;
            margin: 0 2px;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        .step.active {
            background-color: #667eea;
            color: white;
        }
        .step.completed {
            background-color: #28a745;
            color: white;
        }
        .step.error {
            background-color: #dc3545;
            color: white;
        }
    </style>
    <script src="js/echarts.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>图表重复加载修复测试</h1>
            <p>专门测试图表在筛选条件变更时的重复加载问题</p>
        </div>
        
        <div class="test-section">
            <h3>测试步骤指示器</h3>
            <div class="step-indicator">
                <div class="step" id="step1">1. ECharts加载</div>
                <div class="step" id="step2">2. 初次渲染</div>
                <div class="step" id="step3">3. 筛选变更</div>
                <div class="step" id="step4">4. 重新渲染</div>
                <div class="step" id="step5">5. 验证成功</div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试控制面板</h3>
            <div class="test-controls">
                <div class="control-group">
                    <label for="testParam">测试项目:</label>
                    <select id="testParam">
                        <option value="">请选择项目</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="testTimeRange">时间范围:</label>
                    <select id="testTimeRange">
                        <option value="24">24小时</option>
                        <option value="168" selected>7天</option>
                        <option value="360">15天</option>
                    </select>
                </div>
                <div class="control-group">
                    <label for="testAggregation">聚合级别:</label>
                    <select id="testAggregation">
                        <option value="minute">分钟级</option>
                        <option value="hour" selected>小时级</option>
                        <option value="day">天级</option>
                    </select>
                </div>
            </div>
            
            <button class="test-button" onclick="runInitialTest()">1. 初次加载测试</button>
            <button class="test-button" onclick="runReloadTest()">2. 重复加载测试</button>
            <button class="test-button" onclick="runStressTest()">3. 压力测试</button>
            <button class="test-button" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="test-button" onclick="clearChart()">清空图表</button>
        </div>
        
        <div class="test-section">
            <h3>测试图表区域</h3>
            <div id="testChartContainer" class="chart-container">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #666;">
                    等待测试开始...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>测试状态和日志</h3>
            <div id="statusDisplay" class="status-display">等待测试开始...</div>
        </div>
    </div>

    <script>
        let testChart = null;
        let testCount = 0;
        let logMessages = [];
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', async () => {
            updateStep('step1', 'active');
            logMessage('页面加载完成');
            
            // 检查ECharts库
            if (typeof echarts !== 'undefined') {
                updateStep('step1', 'completed');
                logMessage('✓ ECharts库加载成功，版本: ' + echarts.version);
            } else {
                updateStep('step1', 'error');
                logMessage('✗ ECharts库加载失败');
                return;
            }
            
            // 加载测试选项
            await loadTestOptions();
        });
        
        // 更新步骤状态
        function updateStep(stepId, status) {
            const step = document.getElementById(stepId);
            step.className = 'step ' + status;
        }
        
        // 记录日志消息
        function logMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            logMessages.push(logEntry);
            
            // 只保留最近50条日志
            if (logMessages.length > 50) {
                logMessages = logMessages.slice(-50);
            }
            
            document.getElementById('statusDisplay').textContent = logMessages.join('\n');
            console.log(logEntry);
        }
        
        // 加载测试选项
        async function loadTestOptions() {
            try {
                const response = await fetch('php/get_options.php');
                const result = await response.json();
                
                if (result.success && result.data.params) {
                    const select = document.getElementById('testParam');
                    result.data.params.forEach(param => {
                        const option = document.createElement('option');
                        option.value = param;
                        option.textContent = param;
                        select.appendChild(option);
                    });
                    
                    if (result.data.params.length > 0) {
                        select.value = result.data.params[0];
                    }
                    
                    logMessage('✓ 测试选项加载成功');
                } else {
                    logMessage('✗ 测试选项加载失败: ' + result.message);
                }
            } catch (error) {
                logMessage('✗ 测试选项加载异常: ' + error.message);
            }
        }
        
        // 初次加载测试
        async function runInitialTest() {
            logMessage('=== 开始初次加载测试 ===');
            updateStep('step2', 'active');
            
            const paramName = document.getElementById('testParam').value;
            if (!paramName) {
                logMessage('✗ 请选择测试项目');
                updateStep('step2', 'error');
                return;
            }
            
            try {
                const data = await fetchTestData();
                if (data) {
                    renderTestChart(data);
                    updateStep('step2', 'completed');
                    logMessage('✓ 初次加载测试成功');
                } else {
                    updateStep('step2', 'error');
                    logMessage('✗ 初次加载测试失败');
                }
            } catch (error) {
                updateStep('step2', 'error');
                logMessage('✗ 初次加载测试异常: ' + error.message);
            }
        }
        
        // 重复加载测试
        async function runReloadTest() {
            logMessage('=== 开始重复加载测试 ===');
            updateStep('step3', 'active');
            
            // 更改筛选条件
            const timeRangeSelect = document.getElementById('testTimeRange');
            const aggregationSelect = document.getElementById('testAggregation');
            
            // 循环更改筛选条件
            const timeRanges = ['24', '168', '360'];
            const aggregations = ['hour', 'day', 'minute'];
            
            for (let i = 0; i < 3; i++) {
                logMessage(`--- 第${i + 1}次重新加载 ---`);
                
                // 更改时间范围
                timeRangeSelect.value = timeRanges[i % timeRanges.length];
                aggregationSelect.value = aggregations[i % aggregations.length];
                
                logMessage(`更改筛选条件: 时间范围=${timeRangeSelect.value}, 聚合级别=${aggregationSelect.value}`);
                
                updateStep('step4', 'active');
                
                try {
                    const data = await fetchTestData();
                    if (data) {
                        renderTestChart(data);
                        logMessage(`✓ 第${i + 1}次重新加载成功`);
                        
                        // 等待一秒再进行下一次测试
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    } else {
                        logMessage(`✗ 第${i + 1}次重新加载失败`);
                        updateStep('step4', 'error');
                        return;
                    }
                } catch (error) {
                    logMessage(`✗ 第${i + 1}次重新加载异常: ` + error.message);
                    updateStep('step4', 'error');
                    return;
                }
            }
            
            updateStep('step3', 'completed');
            updateStep('step4', 'completed');
            updateStep('step5', 'completed');
            logMessage('✓ 重复加载测试全部成功');
        }
        
        // 压力测试
        async function runStressTest() {
            logMessage('=== 开始压力测试 ===');
            
            const iterations = 10;
            let successCount = 0;
            
            for (let i = 0; i < iterations; i++) {
                logMessage(`压力测试 ${i + 1}/${iterations}`);
                
                try {
                    const data = await fetchTestData();
                    if (data) {
                        renderTestChart(data);
                        successCount++;
                        logMessage(`✓ 压力测试 ${i + 1} 成功`);
                    } else {
                        logMessage(`✗ 压力测试 ${i + 1} 失败`);
                    }
                } catch (error) {
                    logMessage(`✗ 压力测试 ${i + 1} 异常: ` + error.message);
                }
                
                // 短暂延迟
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            logMessage(`压力测试完成: ${successCount}/${iterations} 成功`);
        }
        
        // 获取测试数据
        async function fetchTestData() {
            const paramName = document.getElementById('testParam').value;
            const timeRange = document.getElementById('testTimeRange').value;
            const aggregationLevel = document.getElementById('testAggregation').value;
            
            const params = new URLSearchParams({
                param_name: paramName,
                time_range: timeRange,
                aggregation_level: aggregationLevel
            });
            
            const response = await fetch(`php/get_data.php?${params}`);
            const result = await response.json();
            
            if (result.success) {
                logMessage(`API调用成功，数据点数: ${result.data.chart_data.times.length}`);
                return result.data;
            } else {
                logMessage(`API调用失败: ${result.message}`);
                return null;
            }
        }
        
        // 渲染测试图表
        function renderTestChart(data) {
            const container = document.getElementById('testChartContainer');
            
            // 销毁现有图表
            if (testChart) {
                testChart.dispose();
                testChart = null;
                logMessage('销毁现有图表实例');
            }
            
            // 清空容器
            container.innerHTML = '';
            
            // 创建新图表
            testChart = echarts.init(container);
            logMessage('创建新图表实例');
            
            const option = {
                title: {
                    text: '测试图表',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: data.chart_data.times
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '正常值',
                        type: 'line',
                        data: data.chart_data.normal_values,
                        itemStyle: { color: '#5470c6' }
                    },
                    {
                        name: '超标值',
                        type: 'line',
                        data: data.chart_data.abnormal_values,
                        itemStyle: { color: '#ee6666' }
                    }
                ]
            };
            
            testChart.setOption(option);
            logMessage('图表配置设置完成');
            
            // 验证渲染结果
            setTimeout(() => {
                const chartDom = container.querySelector('canvas') || container.querySelector('svg');
                if (chartDom) {
                    logMessage('✓ 图表渲染成功，DOM元素已创建');
                } else {
                    logMessage('✗ 图表渲染可能失败，未找到canvas或svg元素');
                }
            }, 100);
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            logMessage('=== 系统状态检查 ===');
            
            const status = {
                echartsLoaded: typeof echarts !== 'undefined',
                echartsVersion: typeof echarts !== 'undefined' ? echarts.version : null,
                testChartExists: !!testChart,
                testChartDisposed: testChart ? testChart.isDisposed() : null,
                containerExists: !!document.getElementById('testChartContainer'),
                containerContent: document.getElementById('testChartContainer').innerHTML.length
            };
            
            Object.entries(status).forEach(([key, value]) => {
                logMessage(`${key}: ${value}`);
            });
        }
        
        // 清空图表
        function clearChart() {
            if (testChart) {
                testChart.dispose();
                testChart = null;
                logMessage('图表已清空');
            }
            
            document.getElementById('testChartContainer').innerHTML = 
                '<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #666;">图表已清空</div>';
            
            // 重置步骤指示器
            ['step1', 'step2', 'step3', 'step4', 'step5'].forEach(stepId => {
                document.getElementById(stepId).className = 'step';
            });
        }
    </script>
</body>
</html>
