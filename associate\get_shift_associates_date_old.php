<?php
header('Content-Type: application/json');
require_once 'db_config.php';

try {
    // 获取查询参数
    $showtype = $_GET['showtype'] ?? '';

    $isbigalarm = $_GET['isbigalarm'] ?? '';//1
    $recorder = $_GET['recorder'] ?? '';

    // $tableShow = $_GET['tableShow'] ?? '';
    $section = $_GET['section'] ?? '';
    $area = $_GET['area'] ?? '';
    $shift = $_GET['shift'] ?? '';
    $classes = $_GET['classes'] ?? '';
    $line = $_GET['line'] ?? '';
    $project = $_GET['project'] ?? '';
    $unit = $_GET['unit'] ?? '';
    $problemtype = $_GET['problemtype'] ?? '';
    $needfollow = $_GET['needfollow'] ?? '';
    $status = $_GET['status'] ?? '';
    $keyword = $_GET['problemKeyword'] ?? '';
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';

    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['pageSize']) ? (int)$_GET['pageSize'] : 10;
    
    // 构建WHERE子句
    $where = [];
    $params = [];
    $types = '';

    // 如果搜索的是日期
    if($showtype === '日期'){
        // 定义变量
        $date = [];  // 日期数据
        $manpower = []; // 人力交接数据
        $total= 0;  // 总天数
        $totalPages = 0;    //总页数

        if (!empty($section)) {
            $where[] = "section = ?";
            $params[] = $section;
            $types .= 's';
        }
    
        if (!empty($shift)) {
            $where[] = "shift = ?";
            $params[] = $shift;
            $types .= 's';
        }

        if (!empty($classes)) {
            $where[] = "classes = ?";
            $params[] = $classes;
            $types .= 's';
        }

        // 交接总条数 获取不重复的date值
        // SELECT DISTINCT date FROM shift_manpower ORDER BY date desc
        $countSql = "SELECT DISTINCT date FROM shift_manpower";
        if (!empty($where)) {
            $countSql .= " WHERE " . implode(" AND ", $where);
        }
        $countSql .= " ORDER BY date desc";
        
        $countStmt = $conn->prepare($countSql);
        if (!empty($params)) {
            $countStmt->bind_param($types, ...$params);
        }
        $countStmt->execute();
        // get_result：返回数据结果 fetch_assoc：返回行数
        $dateResult = $countStmt->get_result(); 
        while ($row = $dateResult->fetch_assoc()) {//不能只能通过数字索引返回
        //while ($row = $dateResult->fetch_array(MYSQLI_NUM)) {
            $date[] = $row['date'];  //得到date的数组
            //$date[] = $row[0];  //得到date的数组
        }

        $total = count($date);   //得到数组的长度

        // LIMIT ? OFFSET ?      $pageSize  $offset
        // 计算总页数
        $totalPages = ceil($total / $pageSize); // 总共多少页
        $offset = ($page - 1) * $pageSize;  // 当前页的数据从哪开始，例：pageSize =10，offset 0 \ 10 \ 20   从0开始取10条，从10开始，取10条


        echo json_encode([
            'success' => true,
            'data' => $date,
            'total' => $total,
            'totalPages' => $totalPages,
            'currentPage' => $page
        ]);

    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 