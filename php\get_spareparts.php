<?php
require_once 'db_config.php';

header('Content-Type: application/json');

try {
    // 获取搜索参数
    $state = isset($_GET['state']) ? trim($_GET['state']) : '';
    $section = isset($_GET['section']) ? trim($_GET['section']) : '';
    $key = isset($_GET['key']) ? trim($_GET['key']) : '';
    $location = isset($_GET['location']) ? trim($_GET['location']) : '';
    $code = isset($_GET['code']) ? trim($_GET['code']) : '';
    
    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $pageSize = isset($_GET['page_size']) ? intval($_GET['page_size']) : 10;
    
    // // 验证页码和每页条数
    // if ($page < 1) $page = 1;
    // if ($pageSize < 1) $pageSize = 10;
    // if ($pageSize > 100) $pageSize = 100;  // 限制最大页面大小
    
    // 构建查询条件
    $whereConditions = [];
    $params = [];
    $types = '';
    
    // 添加状态条件
    if (!empty($state)) {
        $states = explode(',', $state);
        $stateConditions = [];
        foreach ($states as $s) {
            $stateConditions[] = "state = ?";
            $params[] = trim($s);
            $types .= 's';
        }
        if (!empty($stateConditions)) {
            $whereConditions[] = "(" . implode(" OR ", $stateConditions) . ")";
        }
    }
    
    // 添加科室条件
    // if (!empty($section)) {
    //     $whereConditions[] = "section = ?";
    //     $params[] = $section;
    //     $types .= 's';
    // }
    if (!empty($section)) {
        $whereConditions[] = "(section = ? OR section = ?)"; 
        $params[] = $section;
        $params[] = 'ALL';
        $types .= 'ss';
    }
    
    // 添加关键词搜索条件（备品名称和型号字段）
    if (!empty($key)) {
        $whereConditions[] = "(name LIKE ? OR model LIKE ? OR `use` LIKE ? OR reason LIKE ? OR  `recorder` LIKE ?)";
        $params[] = "%$key%";
        $params[] = "%$key%";
        $params[] = "%$key%";
        $params[] = "%$key%";
        $params[] = "%$key%";
        $types .= 'sssss';
    }

    // 添加库位搜索条件
    if (!empty($location)) {
        $whereConditions[] = "location LIKE ?";
        $params[] = "%$location%";
        $types .= 's';
    }
    
    // 添加料号搜索条件
    if (!empty($code)) {
        $whereConditions[] = "code LIKE ?";
        $params[] = "%$code%";
        $types .= 's';
    }
    
    // 组合WHERE子句
    $whereClause = '';
    if (!empty($whereConditions)) {
        $whereClause = "WHERE " . implode(" AND ", $whereConditions);
    }
    
    // 计算起始索引
    $offset = ($page - 1) * $pageSize;
    
    // 查询各状态的总数
    $stateCountSql = "SELECT 
    SUM(CASE WHEN state = 'open' THEN 1 ELSE 0 END) as open_count,
    SUM(CASE WHEN state = 'run' THEN 1 ELSE 0 END) as run_count,
    SUM(CASE WHEN state = 'arrive' THEN 1 ELSE 0 END) as arrive_count
    FROM sparepartlist $whereClause";

    if (!empty($params)) {
        $stateCountStmt = $conn->prepare($stateCountSql);
        $stateCountStmt->bind_param($types, ...$params);
        $stateCountStmt->execute();
        $stateCountResult = $stateCountStmt->get_result();
    } else {
        $stateCountResult = $conn->query($stateCountSql);
    }

    $stateCounts = $stateCountResult->fetch_assoc();

    if (!$stateCounts) {
        $stateCounts = [
            'open_count' => 0,
            'run_count' => 0,
            'arrive_count' => 0
        ];
    }

    // 查询总记录数
    $countSql = "SELECT COUNT(*) as total FROM sparepartlist $whereClause";
    
    if (!empty($params)) {
        $countStmt = $conn->prepare($countSql);
        $countStmt->bind_param($types, ...$params);
        $countStmt->execute();
        $totalResult = $countStmt->get_result();
    } else {
        $totalResult = $conn->query($countSql);
    }
    
    $totalRow = $totalResult->fetch_assoc();
    $total = $totalRow['total'];


    $limitClause = '';
    if ($pageSize > 0) {  
        $offset = ($page - 1) * $pageSize;
        $limitClause = "LIMIT ?, ?";
        $finalTypes = $types . 'ii';
        $finalParams = array_merge($params, [$offset, $pageSize]);
    } else {
        $finalTypes = $types;
        $finalParams = $params;
    }
    

    $sql = "SELECT * FROM sparepartlist $whereClause ORDER BY created_at DESC";
    if ($pageSize > 0) $sql .= " LIMIT ?, ?";

    $stmt = $conn->prepare($sql);
    if ($pageSize > 0) {
        $stmt->bind_param($finalTypes, ...$finalParams);
    } elseif (!empty($finalTypes)) {
        $stmt->bind_param($finalTypes, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $spareparts = [];
    while ($row = $result->fetch_assoc()) {
        $spareparts[] = $row;
    }

        // 修改返回的总页数计算
        $totalPages = ($pageSize > 0) ? ceil($total / $pageSize) : 1;

        echo json_encode([
            'success' => true,
            'data' => $spareparts,
            'total' => $total,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'pageSize' => $pageSize,
            'state_counts' => [
            'open' => (int)$stateCounts['open_count'],
            'run' => (int)$stateCounts['run_count'],
            'arrive' => (int)$stateCounts['arrive_count']
        ]
        ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 