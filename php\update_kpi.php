<?php

header('Content-Type: application/json');

require_once 'kpi.php';

$servername = "109.120.2.35";
$username = "eqpmanage";
$password = "eqpmanage";
$dbname = "eqp_management";

try {
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("MySQL连接失败: " . $conn->connect_error);
    }

    $conn->begin_transaction();

    foreach ($resultData as $record) {
        $date = $record['date'];
        $leader = $record['leader'];
        $FP = $record['kpi'];

        // 检查记录是否存在
        $checkStmt = $conn->prepare("SELECT COUNT(*) FROM kpi WHERE date=? AND leader=?");
        $checkStmt->bind_param("ss", $date, $leader);
        $checkStmt->execute();
        $checkStmt->bind_result($count);
        $checkStmt->fetch();
        $exists = $count > 0;
        $checkStmt->close();

        // 动态更新
        if ($exists) {
            $stmt = $conn->prepare("UPDATE kpi SET FP=? WHERE date=? AND leader=?");
            $stmt->bind_param("dss", $FP, $date, $leader);
        } else {
            $stmt = $conn->prepare("INSERT INTO kpi (date, leader, FP) VALUES (?,?,?)");
            $stmt->bind_param("ssd", $date, $leader, $FP);
        }

        if (!$stmt->execute()) {
            throw new Exception("数据库操作失败：".$stmt->error);
        }
        $stmt->close();
    }

    $conn->commit();
    echo json_encode(['success' => true]);

} catch (Exception $e) {
    if (isset($conn)) $conn->rollback();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conn)) $conn->close();
}
?>