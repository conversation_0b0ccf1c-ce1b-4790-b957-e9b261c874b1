<?php
require_once 'sqlsrv_config.php';

header('Content-Type: application/json;charset=utf-8');
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");

session_start();

try {
    // 检查必填字段
    $required_fields = ['id', 'partlevel', 'saftystock', 'damagedhistory', 'recorder'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("缺少必填字段: $field");
        }
    }

    // 获取并验证数据
    $id = intval($_POST['id']);
    $partlevel = trim($_POST['partlevel']);
    $saftystock = intval($_POST['saftystock']);
    $damagedhistory = trim($_POST['damagedhistory']);
    $recorder = trim($_POST['recorder']);

    // 准备SQL语句
    $sql = "UPDATE parts_bom 
            SET partlevel = ?, 
                saftystock = ?, 
                damagedhistory = ?, 
                recorder = ?
            WHERE id = ?";
    $stmt = sqlsrv_prepare($conn, $sql, array(&$partlevel, &$saftystock, &$damagedhistory, &$recorder, &$id)); 
    if (!$stmt) {
        $errors = sqlsrv_errors(); 
		$errorMessage = ""; 
		foreach ($errors as $error) { 
			$errorMessage .= $error['message'] . " "; 
		} 
		throw new Exception("准备语句失败: " . $errorMessage); 
    }

    // 执行语句 
	if (!sqlsrv_execute($stmt)) { 
		$errors = sqlsrv_errors(); 
		$errorMessage = ""; 
		foreach ($errors as $error) { 
			$errorMessage .= $error['message'] . " "; 
		} 
		throw new Exception("执行失败: " . $errorMessage); 
	} 

    // 检查影响行数 
	$rowsAffected = sqlsrv_rows_affected($stmt); 
	if ($rowsAffected === 0) { 
		throw new Exception("没有修改任何数据或记录不存在"); 
	} 

    echo json_encode([
        'success' => true,
        'message' => '修改成功'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}finally{
    // 关闭连接
    if (isset($stmt) && is_resource($stmt)) {
        sqlsrv_free_stmt($stmt);
    }
    if ($conn !== null && $conn !== false) {
        sqlsrv_close($conn);
    }
}
?>