document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录
    const loggedInUser = localStorage.getItem('loggedInUser');
    if (!loggedInUser) {
        window.location.href = 'login.html';
        return;
    }

    // 解析存储的用户信息
    const userData = JSON.parse(loggedInUser);
    
    // 设置头部用户名
    const headerUsername = document.getElementById('headerUsername');
    if (headerUsername) {
        headerUsername.textContent = userData.name;
    }
    
    // 设置录入人
    const recorderInput = document.getElementById('register-recorder');
    if (recorderInput) {
        recorderInput.value = userData.name;
    }
    // 设置发布人
    const publisherInput = document.getElementById('notice-publisher');
    if (publisherInput) {
        publisherInput.value = userData.name;
    }

    // 添加登出功能
    const userProfile = document.querySelector('.user-profile');
    userProfile.addEventListener('click', function() {
        if (confirm('确定要退出登录吗？')) {
            localStorage.removeItem('loggedInUser');
            window.location.href = 'login.html';
        }
    });
}); 

function updateAvatar() {
    const userName = document.querySelector('.user-name').textContent;
    const initials = userName.split(' ')
        .map(name => name.charAt(0))
        .join('');
    document.querySelector('.user-avatar').textContent = initials;
}

// 页面加载时执行
document.addEventListener('DOMContentLoaded', updateAvatar);

document.addEventListener('DOMContentLoaded', function() {
    const headerUsername = document.getElementById('headerUsername');
    const avatarText = document.getElementById('avatarText');
    
    // 如果用户名存在，则显示第一个字符
    if (headerUsername.textContent) {
        avatarText.textContent = headerUsername.textContent.charAt(0);
    }
}); 



