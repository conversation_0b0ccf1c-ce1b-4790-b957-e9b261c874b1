﻿body {
    font-family: <PERSON><PERSON>, "Microsoft YaHei", sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

.sidebar {
    width: 160px;
    height: 100vh;
    background-color: #f8f9fa;
    position: fixed;
    left: 0;
    top: 60px;
    padding-top: 20px;
    border-right: 1px solid #eee;
    overflow-y: auto;
    z-index: 100;
}

.user-info {
    display: flex;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 20px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background-color: #e9ecef;
    border-radius: 50%;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #666;
}

.user-details {
    color: #333;
}

.user-name {
    font-weight: bold;
    margin: 0;
}

.user-role {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: #333;
    text-decoration: none;
    margin: 5px 0;
}

.menu-item:hover {
    background-color: #e9ecef;
}

.menu-icon {
    width: 20px;
    margin-right: 10px;
    text-align: center;
    color: #666;
}

.main-content {
    margin-left: 160px;
    margin-top: 60px;
    padding: 20px;
    min-height: calc(100vh - 60px);
    box-sizing: border-box;
    overflow-x: hidden;
    overflow-y: auto;
}



h1 {
    font-size: 24px;
    color: #333;
}

h4 {
    color: #666;
    margin-bottom: 10px;
}

hr {
    border: none;
    border-top: 1px solid #eee;
    margin: 20px 0;
}

.poetry {
    font-family: "KaiTi", "楷体", serif;
    line-height: 2;
    margin: 20px 0;
}

.footer {
    margin-top: 20px;
    padding: 20px;
    text-align: center;
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

em {
    font-style: normal;
    color: #666;
}

strong {
    font-weight: bold;
    color: #333;
}

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    background: linear-gradient(45deg, #4c6fff, #3a5ae8);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.header-title:hover {
    transform: scale(1.02);
    color: #0056b3;
}

.menu-toggle {
    font-size: 20px;
    color: #666;
    cursor: pointer;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 200px;
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 30px;
}

.header-icon {
    font-size: 20px;
    color: #666;
    cursor: pointer;
    transition: color 0.3s ease;
}

.header-icon:hover {
    color: #4c6fff;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.header-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}







.button-group {
    display: flex;
    gap: 10px;
    margin-left: auto; 
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.data-table th {
    background-color: #f5f5f5;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

.data-table td {
    padding: 8px;
    border: 1px solid #ddd;
    vertical-align: middle;
}

.btn-view {
    padding: 4px 8px;
    background-color: #4c6fff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-view:hover {
    background-color: #3a5ae8;
}


.menu-item.active {
    background-color: #e0e0e0;
}

.tabs {
    margin: 20px 0;
    border-bottom: 2px solid #ddd;
}

.line {
    margin: 20px 0;
    border-bottom: 2px solid #ddd;
}

.tab-button {
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    background: none;
    cursor: pointer;
    margin-right: 10px;
    position: relative;
}

.tab-button.active {
    color: #4c6fff;
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #4c6fff;
}

.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
}

.register-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.form-group textarea {
    resize: vertical;
}

.save-btn,
.submit-btn {
    background-color: #4c6fff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.save-btn:hover,
.submit-btn:hover {
    background-color: #3a5ae8;
}

.page-content {
    display: none;
    background: white;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.page-content.active {
    display: block;
}

/* 确保其他选项卡内容也正确隐藏和显示 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.notice-tab {
    display: none;
}

.notice-tab.active {
    display: block;
}

/* 选项卡容器样式调整 */
.tabs, .notice-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    border-bottom: 2px solid #ddd;
    padding-bottom: 10px;
}

.tabs-left {
    display: flex;
    gap: 10px;
}

/* 返回首页按钮样式 */
.btn-home {
    padding: 8px 16px;
    background-color: #ffffff;
    color: rgb(0, 0, 0);
    border: 1px solid #1082da;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-home:hover {
    background-color: #1082da;
    color: rgb(255, 255, 255);
}

.btn-home::before {
    content: "🏠";
    font-size: 14px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    max-height: 3000px;
    overflow:scroll;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 50px auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    color: #333;
}

.close {
    font-size: 24px;
    color: #666;
    cursor: pointer;
}

.close:hover {
    color: #333;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 14px;
}

.detail-table th,
.detail-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
}

.detail-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    white-space: nowrap;
}

.detail-sections {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin: 20px 0;
}

.detail-section,
.change-status {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
}

.detail-section h3,
.change-status h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #333;
}

.section-content {
    color: #666;
    line-height: 1.6;
}

.image-section {
    display: flex;
    gap: 10px;
    overflow-x: auto;
}

.image-section img {
    max-width: 200px;
    height: auto;
    border-radius: 4px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.history-title h3{
    text-align:center;
}

.history-body{
    width: 100%;
    height:100%;
    overflow:scroll;
}

.history-body-table {
    width: 100%;
    height: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    font-size: 14px;
}

.history-body-table th,
.history-body-table td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: center;
}

.history-body-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    white-space: nowrap;
}

.btn-return,
.btn-modify {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-return {
    background-color: #6c757d;
    color: white;
}

.btn-modify {
    background-color: #4c6fff;
    color: white;
}

.btn-return:hover {
    background-color: #5a6268;
}

.btn-modify:hover {
    background-color: #3a5ae8;
}

/* 分页控件样式 */
.pagination {
    display: flex;
    justify-content: flex-end; /* 整体右对齐 */
    align-items: center;
    margin-top: 20px;
    padding: 10px;
    background: #f5f5f5;
    border-radius: 4px;
}

.pagination-info,
.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 20px; /* 添加左边距，分隔两个部分 */
}

.page-size {
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 60px;
}

.current-page {
    width: 50px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.pagination button {
    padding: 4px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    cursor: pointer;
}

.pagination button:hover {
    background: #e9e9e9;
}

.pagination button:disabled {
    background: #f5f5f5;
    cursor: not-allowed;
    color: #999;
}

.page-info {
    display: flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap; /* 防止文字换行 */
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;  /* 使标签可以居中 */
    min-width: 60px;       /* 设置最小宽度保持一致性 */
    text-align: center;    /* 标签文字居中 */
}

.status-active {
    background-color: #28a745;
    color: white;
}

.status-expired {
    background-color: #dc3545;
    color: white;
}

.status-running {
    background-color: #3591dc;
    color: white;
}



/* 文件上传区域样式 */
.upload-area {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.btn-upload {
    padding: 8px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-upload:hover {
    background-color: #45a049;
}

.file-list {
    margin-top: 10px;
}

.file-item {
    display: flex;
    align-items: center;
    margin: 5px 0;
    padding: 5px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.file-name {
    flex: 1;
    margin-right: 10px;
}

.needPreview{
    color: #bbb; 
	text-decoration: none; 
	cursor: pointer; 
}

.needPreview:hover{
    color: #281cce; 
}


.file-size {
    color: #666;
    margin-right: 10px;
}

.btn-delete-file {
    background: none;
    border: none;
    color: #ff4444;
    cursor: pointer;
    font-size: 18px;
    padding: 0 5px;
}

/* 表单操作按钮样式 */
.form-actions {
    text-align: center;
    margin-top: 20px;
}

.form-actions button {
    padding: 10px 20px;
    margin: 0 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-submit {
    background-color: #4c6fff;
    color: white;
}

.btn-reset {
    background-color: #6c757d;
    color: white;
}

.btn-submit:hover {
    background-color: #3a5ae8;
}

.btn-reset:hover {
    background-color: #5a6268;
}


/* 添加行按钮样式 */
.btn-add-rows {
    background-color: #4c6fff;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
}

.btn-add-rows:hover {
    background-color: #3a5ae8;
}

/* 单选按钮组样式 */
.radio-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    padding: 8px;
    height: 100%;
    box-sizing: border-box;
}

.radio-group label {
    display: inline-flex;  /* 改为inline-flex */
    align-items: center;
    gap: 5px;
    cursor: pointer;
    margin: 0 5px;  /* 添加水平间距 */
}

.radio-group input[type="radio"] {
    margin: 0;
    cursor: pointer;
    width: auto;  /* 防止继承100%宽度 */
}

.radio-group span {
    font-size: 14px;
    white-space: nowrap;  /* 防止文字换行 */
}

/* 确保单选按钮单元格与其他单元格对齐 */
.splist-table td.radio-group,
.register-table td.radio-group {
    vertical-align: middle;
    padding: 8px;
    height: 100%;
    min-height: 40px;  /* 与其他单元格保持一致的最小高度 */
}

/* 修改删除按钮样式 */
.action-column {
    width: 60px;
    padding: 8px !important;
}

.btn-delete-row {
    background-color: #ce3746;
    color: #ffffff;
    border: 1px solid #ce3746;
    border-radius: 4px;
    padding: 4px 12px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-delete-row:hover {
    background-color: #c82333;
    color: rgb(255, 255, 255);
}

/* 修改表格头部样式以适应新增列 */
.splist-table th:last-child,
.splist-table td:last-child,
.register-table th:last-child,
.register-table td:last-child {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

/* 用户信息页面样式 */
.user-profile-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-profile-container h2 {
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4c6fff;
}

.user-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 20px;
}

.info-group {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.info-group label {
    font-weight: bold;
    color: #555;
    width: 80px;
}

.info-group span {
    color: #333;
    flex: 1;
}

/* 用户菜单样式 */
.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 120px;
    margin-top: 5px;
}

.user-menu .menu-item {
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.user-menu .menu-item:hover {
    background-color: #f5f5f5;
}

/* 确保user-profile有相对定位 */
.user-profile {
    position: relative;
    cursor: pointer;
}

/* 文件上传相关样式 */
.file-item {
    display: flex;
    align-items: center;
    margin: 5px 0;
    padding: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
    font-size: 12px;
}

.file-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 5px;
}

.file-size {
    color: #666;
    margin-right: 5px;
}

.btn-delete-file {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0 5px;
    font-size: 16px;
}

.btn-delete-file:hover {
    color: #c82333;
}

input[type="file"] {
    max-width: 200px;
}

/* 查询搜索栏样式 */
.bomSearch {
        background-color: #f5f5f5;
        padding: 20px;
        border-radius: 4px;
        margin-bottom: 20px;
}

.bomSearch-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.bomSearch-row label {
    font-size: 14px;
    color: #333;
    margin-right: 0;
}

.bomSearch-row select {
    width: 120px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 5px;
    font-size: 14px;
    color: #333;
    background-color: #fff;
}

.bomSearch-row input[type="text"] {
    width: 200px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 10px;
    font-size: 14px;
}

.bomSearch-row input[type="date"] {
    width: 130px;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0 5px;
    font-size: 14px;
}

.button-group {
    display: flex;
    gap: 10px;
}

.bomSearch-btn,
.bomDownload-btn,
.bomReset-btn {
    height: 28px;
    padding: 0 15px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.bomDownload-btn,
.bomSearch-btn {
    background-color: #4c6fff;
    color: white;
}

.bomReset-btn {
    background-color: #6c757d;
    color: white;
}

.bomDownload-btn,
.bomSearch-btn:hover {
    background-color: #3a5ae8;
}

.bomReset-btn:hover {
    background-color: #5a6268;
}

/* 响应式布局调整 */
@media screen and (max-width: 1200px) {
    .bomSearch-row {
        flex-wrap: wrap;
    }

    .button-group {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
        justify-content: flex-end;
    }
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-group textarea {
    resize: vertical;
}

.file-info {
    margin-top: 5px;
    font-size: 0.9em;
    color: #666;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-start;
    margin-top: 20px;
}

.btn-submit,
.btn-reset {
    padding: 8px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}


.btn-reset {
    background-color: #f0f0f0;
    color: #333;
}


.btn-reset:hover {
    background-color: #e4e4e4;
}

/* 添加跟进状态样式 */
.follow-status,
.closed-status {
    padding: 4px 8px;
    border-radius: 4px;  /* 这里定义圆角 */
    font-size: 14px;
    display: inline-block;
    min-width: 40px;
    text-align: center;
}

/* "是"的样式 */
.follow-yes,
.closed-yes {
    background-color: hwb(140 90% 0%);
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

/* "否"的样式 */
.follow-no,
.closed-no {
    background-color: hsl(0, 0%, 90%);
    color: hsl(0, 0%, 50%);
    border: 1px solid hsl(0, 0%, 80%);
}

/* 居中对齐的列 */
/* 表内文字的对齐方式 */
.bomRegister .register-table td:nth-child(1),  /* LINE */
.bomRegister .register-table td:nth-child(2),  /* UNIT */
.bomRegister .register-table td:nth-child(3),  /* 故障分类 */
.bomRegister .register-table td:nth-child(4),  /* 故障部件 */
.bomRegister .register-table td:nth-child(5),  /* 故障部件 */
.bomRegister .register-table td:nth-child(6),  /* 故障部件 */
.bomRegister .register-table td:nth-child(7),  /* 故障代码 */
.bomRegister .register-table td:nth-child(8),  /* 需要跟进 */
.bomRegister .register-table td:nth-child(9),  /* 故障图片 */ 
.bomRegister .register-table td:nth-child(10),  /* 操作 */ 
.bomList .data-table td:nth-child(1),  /* Unit */
.bomList .data-table td:nth-child(2),  /* 部品名 */
.bomList .data-table td:nth-child(3),  /* 品牌 */
.bomList .data-table td:nth-child(4),  /* 型号 */
.bomList .data-table td:nth-child(5),  /* 库存 */
.bomList .data-table td:nth-child(6),  /* 使用位置 */
.bomList .data-table td:nth-child(7),  /* 备品级别 */
.bomList .data-table td:nth-child(8),  /* 安全库存 */
.bomList .data-table td:nth-child(9),  /* 损坏履历 */
.bomList .data-table td:nth-child(10)  /* 操作 */  {
    text-align: center;
}

/* 表头始终居中对齐 */
.bomRegister .register-table th,
.bomList .data-table th
{
    background-color: #f5f5f5;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid #ddd;
    white-space: nowrap;
}

/* 交接列表表格列宽控制 */
.bomRegister .register-table ,
.bomList .data-table {
    /*table-layout: fixed;*/  /* 使用固定列宽布局 */
    width: 100%;
}

/* 设置各列宽度 */
.bomList .data-table th:nth-child(1), /* 日期 */
.bomList .data-table td:nth-child(1) {
    width: 10%;
}

.bomList .data-table th:nth-child(2), /* 班次 */
.bomList .data-table td:nth-child(2) {
    width: 10%;
}

.bomList .data-table th:nth-child(3), /* LINE */
.bomList .data-table td:nth-child(3) {
    width: 10%;
}

.bomList .data-table th:nth-child(4), /* UNIT */
.bomList .data-table td:nth-child(4) {
    width: 10%;
}

.bomList .data-table th:nth-child(5), /* 现象 */
.bomList .data-table td:nth-child(5) {
    width: 10%;
}

.bomList .data-table th:nth-child(6), /* 分析 */
.bomList .data-table td:nth-child(6) {
    width: 10%;
}

.bomList .data-table th:nth-child(7), /* 对策 */
.bomList .data-table td:nth-child(7) {
    width: 10%;
}

.bomList .data-table th:nth-child(8), /* 故障分类 */
.bomList .data-table td:nth-child(8) {
    width: 10%;
}

.bomList .data-table th:nth-child(9), /* 故障部位 */
.bomList .data-table td:nth-child(9) {
    width: 10%;
}

.bomList .data-table th:nth-child(10), /* 故障代码 */
.bomList .data-table td:nth-child(10) {
    width: 10%;
}

/* 处理长文本溢出 */
.bomList .data-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;  /* 允许文本换行 */
    word-wrap: break-word;  /* 长单词换行 */
}

/* 现象、分析、对策列允许更多行显示 */
.bomList .data-table td:nth-child(5),
.bomList .data-table td:nth-child(6),
.bomList .data-table td:nth-child(4) {
    max-height: 100px;
    overflow-y: auto;
}

.main-content h3 {
    margin-left: 10px;  /* 添加左边距 */
}
/* 登录 */
.register-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.register-table th,
.register-table td {
    padding: 10px;
    border: 1px solid #ddd;
}

.register-table th {
    background-color: #f5f5f5;
    text-align: right;
    width: 100px;
    white-space: nowrap;
}

.register-table td {
    background-color: #fff;
}

.register-table select,
.register-table input[type="number"],
.register-table input[type="text"],
.register-table textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.register-table textarea {
    resize: vertical;
    min-height: 40px;
}

.bomRegister .register-table  {
    /*table-layout: fixed;*/  /* 使用固定列宽布局 */
    width: 100%;
}

/* 设置各列宽度 */
.bomRegister .register-table th:nth-child(1), /* 班次 */
.bomRegister .register-table td:nth-child(1) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(2), /* LINE */
.bomRegister .register-table td:nth-child(2) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(3), /* UNIT */
.bomRegister .register-table td:nth-child(3) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(4), /* 故障分类 */
.bomRegister .register-table td:nth-child(4) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(5), /* 现象 */
.bomRegister .register-table td:nth-child(5) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(6), /* 原因 */
.bomRegister .register-table td:nth-child(6) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(7), /* 处理内容 */
.bomRegister .register-table td:nth-child(7) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(8), /* 故障部件 */
.bomRegister .register-table td:nth-child(8) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(9), /* 故障代码 */
.bomRegister .register-table td:nth-child(9) {
    width: 10%;
}

.bomRegister .register-table th:nth-child(10), /* 需要跟进 */
.bomRegister .register-table td:nth-child(10) {
    width: 10%;
}

/* 处理长文本溢出 */
.bomRegister .register-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;  /* 允许文本换行 */
    word-wrap: break-word;  /* 长单词换行 */
}

/* 现象、分析、对策列允许更多行显示 */
.bomRegister .register-table td:nth-child(9) {
    max-height: 100px;
    overflow-y: auto;
}

.site-content {
    display: inline-block;
    text-align: center;
    padding: 20px;
    margin: 10px;
    min-width: 200px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.site-content:hover {
    background-color: whitesmoke;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

a.site-content  {
    text-decoration: none;
    color: #333;
    font-size: 18px;
    transition: color 0.3s ease;
}

a.site-content a:hover {
    color: #4c6fff;
}

.line-table {
    display: inline-block;
    vertical-align: top;
    margin-right: 10px;
    margin-bottom: 20px;    
    font-size: 14px;
}

