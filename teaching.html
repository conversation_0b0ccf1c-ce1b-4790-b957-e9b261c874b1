<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资料手册 - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/tabs.js" defer></script>
    <script src="js/teaching.js" defer></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'teaching':
                        switchTab('teaching');
                        break;
                    case 'teachingupload':
                        switchTab('teachingupload');
                        break;
                    default:
                        switchTab('teaching');
                }
            }
        });
    </script>
</head>
<body style="margin: 0; padding: 10px;">

    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <div class="tabs" style="display: none;"> 
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('teaching')">资料手册</button>
                <button class="tab-button" onclick="switchTab('teachingupload')">资料上传</button>
            </div>
        </div>
            
        <!-- 资料手册选项卡 -->
            <div id="teachingTab" class="tab-content active">
                    <div class="teachingsearch">
                        <form id="teachingsearchForm" action="#" method="get">
                            <div class="teachingsearch-row">
                                
                                <label for="teachingsearch-section">科室</label>
                                <select id="teachingsearch-section" name="section">
                                    <option value="">全部</option>
                                </select>

                                <label for="teachingsearch-unit">Unit</label>
                                <select id="teachingsearch-unit" name="unit">
                                    <option value="">全部</option>
                                </select>

                                <label for="teachingsearch-category">分类</label>
                                <select id="teachingsearch-category" name="category">
                                    <option value="">全部</option>
                                    <option value="operation">操作手册</option>
                                    <option value="courseware">教育资料</option>
                                    <option value="other">其他</option>
                                </select>

                                <label for="teachingsearch-upload_user">上传人</label>
                                <input type="text" id="teachingsearch-upload_user" name="upload_user" placeholder="请输入姓名查询">

                                <label for="teachingsearch-title">关键词</label>
                                <input type="text" id="teachingsearch-title" name="title" placeholder="请输入关键词查询">
                                <p style="color: red;">*部分文件加密无法预览，下载后可正常查看！</p>
                                <div class="button-group">
                                    <button type="submit" class="teachingsearch-btn">查询</button>
                                    <button type="reset" class="teachingreset-btn">重置</button>
                                </div>
                            </div>
                        </form>
                    </div>
    
                <div class="teachinglist">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>文件名</th>
                                <th>分类</th>
                                <th>科室</th>
                                <th>Unit</th>
                                <th>上传人</th>
                                <th>上传时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过 JavaScript 动态加载 -->
                        </tbody>
                    </table>
                </div>
    
                <!-- 在表格后添加分页控件 -->
                <div class="pagination">
                    <div class="pagination-info">
                        共 <span class="total-count">0</span> 条记录，
                        每页 <select class="page-size">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select> 条
                    </div>
                    <div class="pagination-controls">
                        <button class="btn-first-page">首页</button>
                        <button class="btn-prev-page">上一页</button>
                        <span class="page-info">
                            第 <input type="number" class="current-page" min="1"> 页，
                            共 <span class="total-pages">0</span> 页
                        </span>
                        <button class="btn-next-page">下一页</button>
                        <button class="btn-last-page">末页</button>
                    </div>
                </div>
            </div>

            <!-- 资料上传选项卡 --> 
            <div id="teachinguploadTab" class="tab-content">
                <div class="teachingupload-content">
                    <form id="uploadForm" class="upload-form">
                        <div class="form-group">
                            <label for="fileTitle">资料标题</label>
                            <input type="text" id="fileTitle" name="fileTitle" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="fileCategory">资料分类</label>
                            <select id="fileCategory" name="fileCategory" required>
                                <option value="">请选择分类</option>
                                <option value="operation">操作手册</option>
                                <option value="courseware">教育资料</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="fileUnit">资料Unit</label>
                            <select id="fileUnit" name="fileUnit" required>
                                <option value="">请选择Unit</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="fileUpload">选择文件</label>
                            <input type="file" id="fileUpload" name="fileUpload">
                            <div id="fileList" class="file-list"></div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-submit">上传资料</button>
                            <button type="reset" class="btn-reset">重置</button>
                        </div>
                    </form>
                </div>
            </div>
    </div>
</body>
</html> 