document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已登录（仅在非登录页面）
    const userInfo = localStorage.getItem('userInfo');
    if (!userInfo && !window.location.href.includes('login.html')) {
        window.location.href = 'login.html';
        return;
    }

    // 加载组件并初始化用户界面
    if (!window.location.href.includes('login.html')) {
        fetch('components.html')
            .then(response => response.text())
            .then(data => {
                document.getElementById('components-container').innerHTML = data;
                initializeUserInterface(JSON.parse(userInfo));
            });
    }
}); 

// 初始化用户界面
function initializeUserInterface(userInfo) {
    // 设置头部用户名和下拉菜单
    const headerUsername = document.getElementById('headerUsername');
    if (headerUsername) {
        headerUsername.textContent = userInfo.name;
        
        // 添加用户名点击事件（显示下拉菜单）
        headerUsername.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            
            const menu = document.createElement('div');
            menu.className = 'user-menu';
            menu.innerHTML = `
                <div class="menu-item" onclick="showUserInfo()">个人信息</div>
                <div class="menu-item" onclick="logout()">退出登录</div>
            `;
            
            // 移除任何现有的菜单
            const existingMenu = document.querySelector('.user-menu');
            if (existingMenu) {
                existingMenu.remove();
            }
            
            // 将菜单添加到用户名元素后
            headerUsername.appendChild(menu);
            
            // 点击其他地方关闭菜单
            document.addEventListener('click', function closeMenu(e) {
                if (!headerUsername.contains(e.target)) {
                    menu.remove();
                    document.removeEventListener('click', closeMenu);
                }
            });
        });
    }

    // 设置录入人
    const recorderInput = document.getElementById('register-recorder');
    if (recorderInput) {
        recorderInput.value = userInfo.name;
    }

    // 设置发布人
    const publisherInput = document.getElementById('notice-publisher');
    if (publisherInput) {
        publisherInput.value = userInfo.name;
    }

    // 设置头像
    const avatarText = document.getElementById('avatarText');
    if (avatarText && userInfo.name) {
        avatarText.textContent = userInfo.name.charAt(0);
    }
}

// 显示用户信息弹窗
function showUserInfo() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');

    // 创建弹窗
    const modal = document.createElement('div');
    modal.className = 'user-info-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>个人信息</h2>
                <span class="close" onclick="closeUserInfo()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="user-info">
                    <div class="info-group">
                        <label>姓名：</label>
                        <span>${userInfo.name || userInfo.username || '-'}</span>
                    </div>
                    <div class="info-group">
                        <label>工号：</label>
                        <span>${userInfo.account || '-'}</span>
                    </div>
                    <div class="info-group">
                        <label>部门：</label>
                        <span>${userInfo.department || '-'}</span>
                    </div>
                    <div class="info-group">
                        <label>科室：</label>
                        <span>${userInfo.section || '-'}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭弹窗
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeUserInfo();
        }
    });
}

// 关闭用户信息弹窗
function closeUserInfo() {
    const modal = document.querySelector('.user-info-modal');
    if (modal) {
        modal.remove();
    }
}

// 登出函数
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('userInfo');
        window.location.href = 'login.html';
    }
}

// 用户信息管理类
class UserManager {
    constructor() {
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.loadUserInfo();
        });
    }

    loadUserInfo() {
        const userInfo = JSON.parse(localStorage.getItem('userInfo'));
        if (userInfo) {
            const elements = {
                'userName': userInfo.name,
                'userAccount': userInfo.account,
                'userDepartment': userInfo.department,
                'userSection': userInfo.section
            };

            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value || '-';
                }
            });
        }
    }
}

// 创建UserManager实例
const userManager = new UserManager();