<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
require_once 'db_config.php';

try {
    // 获取分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = ($page - 1) * $limit;

    // 构建基础查询
    $where_conditions = [];
    $params = [];
    $types = '';

    // 添加所有可能的搜索条件
    $search_fields = [
        'line',
        'unit',
        'category',
        'keyword',
        'section'
    ];

    // 在处理搜索条件之前添加日期区间处理
    if (!empty($_GET['start_date'])) {
        $where_conditions[] = "DATE(datetime) >= ?";
        $params[] = $_GET['start_date'];
        $types .= 's';
    }

    if (!empty($_GET['end_date'])) {
        $where_conditions[] = "DATE(datetime) <= ?";
        $params[] = $_GET['end_date'];
        $types .= 's';
    }

    // 处理每个搜索字段
    foreach ($search_fields as $field) {
        if (!empty($_GET[$field])) {
            // 对关键词进行多字段模糊搜索
            if ($field === 'keyword') {
                $where_conditions[] = "(keyword LIKE ? OR phenomenon LIKE ? OR issue LIKE ? OR measures LIKE ?)";
                $search_value = "%{$_GET[$field]}%";
                array_push($params, $search_value, $search_value, $search_value, $search_value);
                $types .= 'ssss';
            } else {
                $where_conditions[] = "$field = ?";
                $params[] = $_GET[$field];
                $types .= 's';
            }
        }
    }

    // 计算总记录数
    $count_sql = "SELECT COUNT(*) as total FROM alarmlist";
    if (!empty($where_conditions)) {
        $count_sql .= " WHERE " . implode(" AND ", $where_conditions);
    }
    
    $count_stmt = $conn->prepare($count_sql);
    if (!empty($params)) {
        $count_stmt->bind_param($types, ...$params);
    }
    $count_stmt->execute();
    $total_result = $count_stmt->get_result();
    $total_row = $total_result->fetch_assoc();
    $total = $total_row['total'];
    $total_pages = ceil($total / $limit);

    // 获取数据
    $sql = "SELECT * FROM alarmlist";
    if (!empty($where_conditions)) {
        $sql .= " WHERE " . implode(" AND ", $where_conditions);
    }
    $sql .= " ORDER BY datetime DESC LIMIT ? OFFSET ?";
    
    $stmt = $conn->prepare($sql);
    $types .= 'ii';
    $params[] = $limit;
    $params[] = $offset;
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $faults = [];
    while ($row = $result->fetch_assoc()) {
        $faults[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $faults,
        'pagination' => [
            'total' => $total,
            'current_page' => $page,
            'total_pages' => $total_pages,
            'limit' => $limit
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 