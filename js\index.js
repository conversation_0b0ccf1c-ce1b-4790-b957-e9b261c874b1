﻿class IndexManager {
    constructor() {
        this.initializeEventListeners();
        this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
    }

    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.loadActiveNotices();
            this.loadActiveIssues();
            this.loadClosedIssues();
        });
    }

    async loadActiveNotices() {
        try {
            // 根据用户权限设置 project
            let project = '';
            switch(this.userInfo?.level) {
                case 21:
                    project = 'OC';
                    break;
                case 22:
                    project = 'LCM';
                    break;
                case 23:
                    project = 'LOG';
                    break;
            }            
            const section = this.userInfo?.section || '';
            const response = await fetch(`php/get_active_notices.php?section=${encodeURIComponent(section)}&project=${project}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateNoticeTable(result.data);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载通知失败:', error);
        }
    }

    updateNoticeTable(notices) {
        const tbody = document.getElementById('activeNotices');
        if (!tbody) return;

        if (!notices || notices.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无生效通知</td></tr>';
            return;
        }

        tbody.innerHTML = notices.map(notice => this.generateNoticeRow(notice)).join('');
    }

    generateNoticeRow(notice) {
        const status = this.getNoticeStatus(notice.end_time);
        return `
            <tr>
                <td>${notice.related}</td>
                <td>${notice.content}</td>
                                <td>
                    ${notice.file_count > 0 ? 
                        `<button class="btn-view-files" onclick="indexManager.viewNoticeFiles(${notice.id})">
                            附件(${notice.file_count})
                        </button>` : 
                        '<span style="color: #666;">无附件</span>'
                    }
                </td>
                <td>${notice.publisher}</td>
                <td><span class="status-badge ${status.class}">${status.text}</span></td>
                <td>${this.formatDate(notice.upload_time)}</td>
                <td>${this.formatDate(notice.end_time) || '永久有效'}</td>
            </tr>
        `;
    }

    async viewNoticeFiles(noticeId) {
        try {
            const response = await fetch(`php/get_notice_files.php?notice_id=${noticeId}`);
            const result = await response.json();
            
            if (result.success && result.data && result.data.length > 0) {
                // 创建文件预览弹窗
                const viewer = document.createElement('div');
                viewer.className = 'file-viewer';
                viewer.style.position = 'fixed';
                viewer.style.top = '0';
                viewer.style.left = '0';
                viewer.style.width = '100%';
                viewer.style.height = '100%';
                viewer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                viewer.style.zIndex = '1000';
                viewer.style.display = 'flex';
                viewer.style.justifyContent = 'center';
                viewer.style.alignItems = 'center';

                const fileListHtml = result.data.map(file => {
                    const isImage = file.file_type && file.file_type.startsWith('image/');
                    if (isImage) {
                        return `
                            <div class="file-item" style="margin: 10px 0;">
                                <img src="uploads/notice/${file.file_path}" 
                                     alt="${file.file_name}"
                                     title="${file.file_name}"
                                     style="
                                        max-width: 90vw;
                                        max-height: 80vh;
                                        object-fit: contain;
                                        cursor: pointer;
                                     "
                                     onclick="window.open(this.src, '_blank')"
                                >
                                <div style="color: white; text-align: center; margin-top: 5px;">
                                    ${file.file_name}
                                </div>
                            </div>`;
                    } else {
                        return `
                            <div class="file-item" style="
                                background: white;
                                padding: 15px;
                                margin: 10px 0;
                                border-radius: 5px;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                cursor: pointer;
                            " onclick="window.open('uploads/notice/${file.file_path}', '_blank')">
                                <span>${file.file_name}</span>
                                <span style="color: #666; margin-left: 20px;">
                                    (${(file.file_size / 1024).toFixed(2)} KB)
                                </span>
                            </div>`;
                    }
                }).join('');

                viewer.innerHTML = `
                    <div class="file-viewer-content" style="
                        width: 90%;
                        max-height: 90vh;
                        overflow-y: auto;
                        padding: 20px;
                    ">
                        <span class="close-btn" style="
                            position: fixed;
                            right: 20px;
                            top: 20px;
                            color: white;
                            font-size: 30px;
                            cursor: pointer;
                            z-index: 1001;
                        ">&times;</span>
                        <div class="file-list" style="
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 10px;
                        ">
                            ${fileListHtml}
                        </div>
                    </div>
                `;

                document.body.appendChild(viewer);

                // 关闭按钮事件
                viewer.querySelector('.close-btn').onclick = () => {
                    viewer.remove();
                };

                // 点击背景关闭
                viewer.addEventListener('click', (e) => {
                    if (e.target === viewer) {
                        viewer.remove();
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', function closeOnEsc(e) {
                    if (e.key === 'Escape') {
                        viewer.remove();
                        document.removeEventListener('keydown', closeOnEsc);
                    }
                });
            } else {
                alert('该通知没有附件');
            }
        } catch (error) {
            console.error('获取附件失败:', error);
            alert('获取附件失败：' + error.message);
        }
    }

    getNoticeStatus(endTime) {
        if (!endTime) {
            return { text: '永久有效', class: 'status-active' };
        }
        return { text: '生效中', class: 'status-active' };
    }

    async loadActiveIssues() {
        try {
            // 根据用户权限设置 project
            let project = '';
            switch(this.userInfo?.level) {
                case 21:
                    project = 'OC';
                    break;
                case 22:
                    project = 'LCM';
                    break;
                case 23:
                    project = 'LOG';
                    break;
            }
            const section = this.userInfo?.section || '';
            const response = await fetch(`php/get_active_associate.php?section=${section}&project=${project}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateIssuesTable(result.data);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载待解决问题失败:', error);
        }
    }

    updateIssuesTable(issues) {
        const tbody = document.getElementById('activeAssociate');
        if (!tbody) return;

        if (!issues || issues.length === 0) {
            tbody.innerHTML = '<tr><td colspan="10" style="text-align: center;">暂无待解决问题</td></tr>';
            return;
        }

        tbody.innerHTML = issues.map(issue => this.generateIssueRow(issue)).join('');
    }

    getAssociateStatus(status) {
        if (!status) {
            return { text: '否', class: 'status-active' };
        }
        return { text: '跟踪中', class: 'status-active' };
    }

    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: '2-digit',
            month: '2-digit',
            day: '2-digit'
        });
    }

    generateIssueRow(issue) {
        const status = this.getAssociateStatus(issue.status);
        return `
            <tr>
                <td>${issue.line || ''}</td>
                <td>${issue.unit || ''}</td>
                <td>${issue.phenomenon || ''}</td>
                <td>${issue.analysis || ''}</td>
                <td>${issue.measure || ''}</td>
                <td>${issue.problemtype || ''}</td>
                <td>${issue.problempart || ''}</td>
                <td>${issue.problemcode || ''}</td>
                <td><span class="status-badge ${status.class}">${status.text}</span></td>
                <td>${this.formatDate(issue.updated_at)}</td>
            </tr>
        `;
    }

    async loadClosedIssues() {
        try {
            // 根据用户权限设置 project
            let project = '';
            switch(this.userInfo?.level) {
                case 21:
                    project = 'OC';
                    break;
                case 22:
                    project = 'LCM';
                    break;
                case 23:
                    project = 'LOG';
                    break;
            }
            const section = this.userInfo?.section || '';
            const response = await fetch(`php/get_closed_associate.php?section=${section}&project=${project}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateClosedIssuesTable(result.data);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载问题处理结果反馈失败:', error);
        }
    }

    updateClosedIssuesTable(issues) {
        const tbody = document.getElementById('closedAssociate');
        if (!tbody) return;

        if (!issues || issues.length === 0) {
            tbody.innerHTML = '<tr><td colspan="10" style="text-align: center;">暂无处理结果反馈</td></tr>';
            return;
        }

        tbody.innerHTML = issues.map(issue => this.generateClosedIssueRow(issue)).join('');
    }

    generateClosedIssueRow(issue) {
        const status = this.getAssociateClosedStatus(issue.status);
        return `
            <tr>
                <td>${issue.line || ''}</td>
                <td>${issue.unit || ''}</td>
                <td>${issue.phenomenon || ''}</td>
                <td>${issue.analysis || ''}</td>
                <td>${issue.measure || ''}</td>
                <td>${issue.problemtype || ''}</td>
                <td>${issue.problempart || ''}</td>
                <td>${issue.problemcode || ''}</td>
                <td><span class="status-badge ${status.class}">${status.text}</span></td>
                <td>${this.formatDate(issue.updated_at)}</td>
            </tr>
        `;
    }

    getAssociateClosedStatus(status) {
        if (!status) {
            return { text: '否', class: 'status-active' };
        }
        return { text: '已结案', class: 'status-active' };
    }
}



// 创建首页管理器实例
const indexManager = new IndexManager(); 