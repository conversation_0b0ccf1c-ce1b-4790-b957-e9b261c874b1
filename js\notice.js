class NoticeManager {
    constructor() {
        this.initializeEventListeners();
        this.userInfo = JSON.parse(localStorage.getItem('userInfo'));
        this.selectedFiles = new Map(); // 存储已选择的文件
        this.loadProjectOptions();
    }

    initializeEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            // 处理通知表单提交
            const noticeForm = document.getElementById('noticeForm');
            if (noticeForm) {
                noticeForm.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    await this.submitNotice(e.target);
                });
            }

            // 添加选项卡切换监听
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', () => {
                    if (button.textContent === '历史记录') {
                        this.loadNoticeList();
                    }
                });
            });

            // 初始化文件上传相关事件
            this.initializeFileUpload();
        });
        
    }

    initializeFileUpload() {
        const fileUpload = document.getElementById('fileUpload');
        const fileList = document.getElementById('fileList');
        
        if (!fileUpload || !fileList) return;

        fileUpload.addEventListener('change', (e) => {
            const newFiles = Array.from(e.target.files);
            
            newFiles.forEach(file => {
                const fileId = Date.now() + '-' + file.name;
                this.selectedFiles.set(fileId, file);
                this.addFileToList(fileId, file);
            });

            fileUpload.value = '';
        });
    }

    addFileToList(fileId, file) {
        const fileList = document.getElementById('fileList');
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
            <span class="file-name" data-file-id="${fileId}">${file.name}</span>
            <span class="file-size">(${(file.size / 1024).toFixed(2)} KB)</span>
            <button type="button" class="btn-delete-file" data-file-id="${fileId}">×</button>
        `;

        // 添加文件名点击事件（预览）
        const fileName = fileItem.querySelector('.file-name');
        fileName.addEventListener('click', () => {
            this.previewFile(file);
        });

        // 添加删除按钮事件
        const deleteBtn = fileItem.querySelector('.btn-delete-file');
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectedFiles.delete(fileId);
            fileItem.remove();
        });

        fileList.appendChild(fileItem);
    }

    previewFile(file) {
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewWindow = window.open('', '_blank');
                previewWindow.document.write(`
                    <img src="${e.target.result}" style="max-width: 100%; height: auto;">
                `);
            };
            reader.readAsDataURL(file);
        } else {
            const fileUrl = URL.createObjectURL(file);
            window.open(fileUrl, '_blank');
        }
    }

    // 加载通知列表
    async loadNoticeList() {
        try {
            // 根据用户权限设置 project
            let project = '';
            switch(this.userInfo?.level) {
                case 21:
                    project = 'OC';
                    break;
                case 22:
                    project = 'LCM';
                    break;
                case 23:
                    project = 'LOG';
                    break;
            }      
            const section = this.userInfo?.section || '';
            const response = await fetch(`php/get_notices.php?section=${encodeURIComponent(section)}&project=${project}`);
            const result = await response.json();
            
            if (result.success) {
                this.updateNoticeTable(result.data);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载通知列表失败:', error);
            alert('加载失败：' + error.message);
        }
    }

    // 更新通知表格
    updateNoticeTable(notices) {
        const tbody = document.querySelector('#historyTab .data-table tbody');
        if (!tbody) return;

        if (!notices || notices.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无通知记录</td></tr>';
            return;
        }

        tbody.innerHTML = notices.map(notice => this.generateNoticeRow(notice)).join('');
    }

    // 生成通知行HTML
    generateNoticeRow(notice) {
        const status = this.getNoticeStatus(notice.end_time);
        const userData = JSON.parse(localStorage.getItem('userInfo'));
        const currentUser = userData ? userData.name : '';
        const canDelete = notice.publisher === currentUser;
        
        return `
            <tr>
                <td>${notice.related}</td>
                <td>${notice.content}</td>
                <td>
                    ${notice.file_count > 0 ? 
                        `<button class="btn-view-files" onclick="noticeManager.viewNoticeFiles(${notice.id})">
                            附件(${notice.file_count})
                        </button>` : 
                        '<span style="color: #666;">无附件</span>'
                    }
                </td>
                <td>${notice.publisher}</td>
                <td><span class="status-badge ${status.class}">${status.text}</span></td>
                <td>${this.formatDate(notice.upload_time)}</td>
                <td>${this.formatDate(notice.end_time) || '永久有效'}</td>
                <td>${canDelete ? 
                    '<button class="btn-delete" onclick="noticeManager.deleteNotice(' + notice.id + ')">删除</button>' : 
                    ''}
                </td>
            </tr>
        `;
    }

    // 获取通知状态
    getNoticeStatus(endTime) {
        if (!endTime) {
            return { text: '永久有效', class: 'status-active' };
        }

        const now = new Date();
        const end = new Date(endTime);
        
        if (now > end) {
            return { text: '已过期', class: 'status-expired' };
        } else {
            return { text: '生效中', class: 'status-active' };
        }
    }

    async viewNoticeFiles(noticeId) {
        try {
            const response = await fetch(`php/get_notice_files.php?notice_id=${noticeId}`);
            const result = await response.json();
            
            if (result.success && result.data && result.data.length > 0) {
                // 创建文件预览弹窗
                const viewer = document.createElement('div');
                viewer.className = 'file-viewer';
                viewer.style.position = 'fixed';
                viewer.style.top = '0';
                viewer.style.left = '0';
                viewer.style.width = '100%';
                viewer.style.height = '100%';
                viewer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                viewer.style.zIndex = '1000';
                viewer.style.display = 'flex';
                viewer.style.justifyContent = 'center';
                viewer.style.alignItems = 'center';

                const fileListHtml = result.data.map(file => {
                    const isImage = file.file_type && file.file_type.startsWith('image/');
                    if (isImage) {
                        return `
                            <div class="file-item" style="margin: 10px 0;">
                                <img src="uploads/notice/${file.file_path}" 
                                     alt="${file.file_name}"
                                     title="${file.file_name}"
                                     style="
                                        max-width: 90vw;
                                        max-height: 80vh;
                                        object-fit: contain;
                                        cursor: pointer;
                                     "
                                     onclick="window.open(this.src, '_blank')"
                                >
                                <div style="color: white; text-align: center; margin-top: 5px;">
                                    ${file.file_name}
                                </div>
                            </div>`;
                    } else {
                        return `
                            <div class="file-item" style="
                                background: white;
                                padding: 15px;
                                margin: 10px 0;
                                border-radius: 5px;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                cursor: pointer;
                            " onclick="window.open('uploads/notice/${file.file_path}', '_blank')">
                                <span>${file.file_name}</span>
                                <span style="color: #666; margin-left: 20px;">
                                    (${(file.file_size / 1024).toFixed(2)} KB)
                                </span>
                            </div>`;
                    }
                }).join('');

                viewer.innerHTML = `
                    <div class="file-viewer-content" style="
                        width: 90%;
                        max-height: 90vh;
                        overflow-y: auto;
                        padding: 20px;
                    ">
                        <span class="close-btn" style="
                            position: fixed;
                            right: 20px;
                            top: 20px;
                            color: white;
                            font-size: 30px;
                            cursor: pointer;
                            z-index: 1001;
                        ">&times;</span>
                        <div class="file-list" style="
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            gap: 10px;
                        ">
                            ${fileListHtml}
                        </div>
                    </div>
                `;

                document.body.appendChild(viewer);

                // 关闭按钮事件
                viewer.querySelector('.close-btn').onclick = () => {
                    viewer.remove();
                };

                // 点击背景关闭
                viewer.addEventListener('click', (e) => {
                    if (e.target === viewer) {
                        viewer.remove();
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', function closeOnEsc(e) {
                    if (e.key === 'Escape') {
                        viewer.remove();
                        document.removeEventListener('keydown', closeOnEsc);
                    }
                });
            } else {
                alert('该通知没有附件');
            }
        } catch (error) {
            console.error('获取附件失败:', error);
            alert('获取附件失败：' + error.message);
        }
    }

    async submitNotice(form) {
        try {
            const section = this.userInfo?.section || '';
            const formData = new FormData(form);
            formData.append('section', section);

            // 添加文件到 FormData
            this.selectedFiles.forEach((file, fileId) => {
                formData.append('files[]', file);
            });

            const response = await fetch('php/submit_notice.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (result.success) {
                alert('通知发布成功');
                form.reset();
                // 清空文件列表
                this.selectedFiles.clear();
                document.getElementById('fileList').innerHTML = '';
                // 重新设置发布人
                const publisherInput = document.getElementById('notice-publisher');
                if (publisherInput) {
                    const currentUser = localStorage.getItem('username');
                    publisherInput.value = currentUser || '';
                }
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('提交失败:', error);
            alert('发布失败：' + error.message);
        }
    }

    // 添加删除方法
    async deleteNotice(id) {
        if (!confirm('确定要删除这条通知吗？')) {
            return;
        }

        try {
            const response = await fetch('php/delete_notice.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: id })
            });

            const result = await response.json();
            if (result.success) {
                alert('删除成功');
                this.loadNoticeList(); // 重新加载列表
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('删除失败:', error);
            alert('删除失败：' + error.message);
        }
    }
    async loadProjectOptions() {
        try {
            // 获取用户科室
            const section = this.userInfo?.section || '';
            
            // 根据用户权限设置默认project
            let defaultProject = '';
            switch(this.userInfo?.level) {
                case 21:
                    defaultProject = 'OC';
                    break;
                case 22:
                    defaultProject = 'LCM';
                    break;
                case 23:
                    defaultProject = 'LOG';
                    break;
            }
            
            // 加载Project选项
            const projectResponse = await fetch(`php/get_options.php?type=project&section=${section}`);
            const projectData = await projectResponse.json();
            if (projectData.success) {
                const projectSelect = document.getElementById('notice-project');
                if (projectSelect) {
                    projectSelect.innerHTML = `<option value="">${section || '全部'}</option>` + 
                        projectData.data.map(project => 
                            `<option value="${project}" ${project === defaultProject ? 'selected' : ''}>${project}</option>`
                        ).join('');
                    
                    // 如果有默认project，触发change事件
                    if (defaultProject) {
                        projectSelect.value = defaultProject;
                        projectSelect.dispatchEvent(new Event('change'));
                    }
                }
            }

        } catch (error) {
            console.error('加载选项失败:', error);
        }
    }

    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: '2-digit',
            month: '2-digit',
            day: '2-digit'
        });
    }


}

// 创建通知管理器实例
const noticeManager = new NoticeManager(); 