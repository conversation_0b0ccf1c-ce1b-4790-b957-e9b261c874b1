<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电池管理 - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/agv.css">
    <script src="js/tabs.js" defer></script>
    <script src="js/xlsx.js"></script>
    <script src="js/agv_battery.js" defer></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'changelist':
                        switchTab('changelist');
                        break;
                    case 'changein':
                        switchTab('changein');
                        break;
                    case 'battery':
                        switchTab('battery');
                        break;
                    default:
                        switchTab('changelist');
                }
            }
        });
    </script>
</head>
<body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <div class="tabs" style="display: none;">
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('changelist')">电池更换履历</button>
                <button class="tab-button" onclick="switchTab('changein')">电池更换登录</button>
                <button class="tab-button" onclick="switchTab('battery')">AGV电池信息</button>
                <a href="http://************:8001" target="_blank" rel="nofollow" >
                    <button class="tab-button" onclick="switchTab('smartCenter')">Smart Center</button></a>
            </div>
        </div>
            
            <!-- AGV电池信息选项卡 -->
            <div id="batteryTab" class="tab-content">

                <div class="batterylist" style="width: 100%; height: 700px; overflow: auto;">
                    <table class="data-table">
                        <thead>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>

                <!-- 在HTML中添加模态框结构 -->
                <div id="editModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2>电池详情</h2>
                            <span class="close-button">&times;</span>
                        </div>
                        <div class="modal-body">
                            <h4>基本信息</h4>
                            <table class="detail-table">
                                <tr>
                                    <th>栋别</th>
                                    <th>产线内/外</th>
                                    <th>位置区域</th>
                                    <th>对应充电桩</th>
                                    <th>归类</th>
                                    <th>物品名称</th>
                                    <th>使用状态</th>
                                </tr>
                                <tr></tr>
                            </table>

                            <h4>电池规格</h4>
                            <table class="detail-table">
                                <tr>
                                    <th>唯一编号</th>
                                    <th>电池型号</th>
                                    <th>储能类型</th>
                                    <th>电池厂家</th>
                                    <th>导入日期</th>
                                    <th>充电类型</th>
                                </tr>
                                <tr></tr>
                                <tr>
                                    <th>电池认证</th>
                                    <th>电池容量</th>
                                    <th>寿命(年)</th>
                                    <th>电池规格</th>
                                    <th>电池数量</th>
                                </tr>
                                <tr></tr>
                            </table>

                            <h4>责任管理</h4>
                            <table class="detail-table">
                                <tr>
                                    <th>管理部门</th>
                                    <th>部门长</th>
                                    <th>科室</th>
                                    <th>科长</th>
                                    <th>担当</th>
                                    <th>点检周期</th>
                                </tr>
                                <tr></tr>
                            </table>
                        </div>
                        <div class="modal-footer">
                            <button class="btn-return">返回</button>
                            <button class="btn-modify">修改</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 履历查询选项卡 -->
            <div id="changelistTab" class="tab-content active">
                <div class="sparepartssearch">
                    <form id="batterysearchForm" action="#" method="get">
                        <div class="sparepartssearch-row">

                            <label for="batterysearch-agvInfo">车辆信息</label>
                            <select id="batterysearch-agvInfo" name="agvInfo">
                                <option value="">全部</option>
                            </select>
                            
                            <label for="batterysearch-key">电池信息</label>
                            <input type="text" id="batterysearch-key" name="key" placeholder="请输入关键词查询">
                            
                            <label for="batterysearch-start-date">日期</label>
                            <input type="date" id="batterysearch-start-date" name="start_date">
                            
                            <label for="batterysearch-end-date">~</label>
                            <input type="date" id="batterysearch-end-date" name="end_date">
                            
                            <div class="button-group">
                                <button type="submit" class="sparepartssearch-btn">查询</button>
                                <button type="button" class="sparepartsreset-btn">重置</button>
                                <button type="button" class="sparepartsdownload-btn">导出</button>
                            </div>
                        </div>
                    </form>
                </div>

                <div id="battery-list" class="battery-list">
                    <table id="battery-change-table" class="splist-table">
                        <tr>
                            <th>车辆信息</th>
                            <th>更换前电池信息</th>
                            <th>更换后电池信息</th>
                            <th>电池更换时间</th>
                            <th>车辆搬出时间</th>
                            <th>车辆返回时间</th>
                            <th>更换评估报告</th>
                        </tr>

                        <tbody id="batteryListTableBody">
                            <!-- 这里将通过JavaScript动态生成1行空表格 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 在表格后添加分页控件 -->
                <div class="pagination">
                    <div class="pagination-info">
                        共 <span class="total-count">0</span> 条记录，
                        每页 <select class="page-size">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select> 条
                    </div>
                    <div class="pagination-controls">
                        <button class="btn-first-page">首页</button>
                        <button class="btn-prev-page">上一页</button>
                        <span class="page-info">
                            第 <input type="number" class="current-page" min="1"> 页，
                            共 <span class="total-pages">0</span> 页
                        </span>
                        <button class="btn-next-page">下一页</button>
                        <button class="btn-last-page">末页</button>
                    </div>
                </div>                
            </div>



            <!-- 履历录入选项卡 -->
            <div id="changeinTab" class="tab-content">
                <div class="battery-change-editor">
                    <form action="#" method="post" class="battery-change-form">
                        <!-- 表头行 -->
                        <table class="splist-table">
                            <tr>
                                <th>车辆信息</th>
                                <th>更换前电池信息</th>
                                <th>更换后电池信息</th>
                                <th>电池更换时间</th>
                                <th>车辆搬出时间</th>
                                <th>车辆返回时间</th>
                                <th>更换评估报告</th>
                                <th>操作</th>
                            </tr>

                            <tbody id="batteryChangeTableBody">
                                <!-- 这里将通过JavaScript动态生成1行空表格 -->
                            </tbody>
                        </table>

                        <div class="form-actions">
                            <button type="button" class="btn-add-rows">添加</button>
                            <button type="submit" class="btn-submit">提交</button>
                            <p style="color: red;">*所有表格必填</p>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Smart Center 选项卡 -->
             <div id="smartCenterTab" class="tab-content">
                <iframe src="http://************:8080/team/batteryall.html" 
                width="100%" height="960px" frameborder="0"></iframe>
             </div>
    </div>
</body>
</html> 