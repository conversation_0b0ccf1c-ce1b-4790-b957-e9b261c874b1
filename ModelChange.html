<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>切机管理 - 设备综合管理</title>
    <link rel="icon" href="pic/icon/weblogo1.png">
    <link rel="stylesheet" href="css/style.css">
    <script src="js/tabs.js" defer></script>
    <script src="js/model_change.js" defer></script>
    <script>
        // 根据URL参数显示对应的tab
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            if (tabParam) {
                // 根据参数显示对应的tab
                switch(tabParam) {
                    case 'JIG':
                        switchTab('JIG');
                        break;
                    case 'MC':
                        switchTab('MC');
                        break;
                    default:
                        switchTab('JIG');
                }
            }
        });
    </script>
    <style>
    .JIGlist {overflow: auto;}

    .JIGlist .data-table th:nth-child(1),
    .JIGlist .data-table td:nth-child(1) {
        position: sticky;
        left: 0;
        z-index: 2;
        min-width: 50px;
        background-color: #f5f7fa;
    }

    .JIGlist .data-table th:nth-child(2),
    .JIGlist .data-table td:nth-child(2) {
        position: sticky;
        left: 60px; 
        z-index: 2;
        min-width: 60px;
        background-color: #f5f7fa;
    }

    .JIGlist .data-table th {
        position: sticky;
        top: 0;
        z-index: 2;
        background-color: #f5f7fa;
    }

    /* 处理固定行列交叉处的单元格 */
    .JIGlist .data-table th:nth-child(1),
    .JIGlist .data-table th:nth-child(2) {
        z-index: 3;  /* 确保交叉点的表头单元格在最上层 */
        border-bottom: 1px solid #ddd;
    }



    .JIGlist .data-table td,
    .JIGlist .data-table th
    {
        min-width: 50px;
        padding: 12px;
        border: 1px solid #ddd;
        text-align: center;
    }

    .JIGlist .data-table tr:hover {
        background-color: #f9fafb;
    }

    .inventory-cell {
        background-color: #f0f9eb;
        font-weight: bold;
    }

    .loading-spinner {
        display: inline-block;
        width: 24px;
        height: 24px;
        border: 3px solid #f3f3f3;
        border-radius: 50%;
        border-top-color: #3498db;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .highlight {
    background-color:rgb(255, 214, 220);
    font-weight: bold;
    color: red;
    }
</style>
</head>
<body style="margin: 0; padding: 10px;">
    <div class="main-content" style="margin-left: 0; margin-top: 0;">
        <div class="tabs">
            <div class="tabs-left">
                <button class="tab-button active" onclick="switchTab('JIG')">金型信息</button>
                <button class="tab-button" onclick="switchTab('MC')">切机管理</button>
            </div>
        </div>
            
        <!-- 金型信息选项卡 -->
            <div id="JIGTab" class="tab-content active">
                
                    <!-- <div class="JIGsearch">
                        <form id="JIGsearchForm" action="-" method="get">
                            <div class="JIGsearch-row">
                                
                                <label for="JIGsearch-section">-</label>
                                <select id="JIGsearch-section" name="section">
                                    <option value="">-</option>
                                </select>

                                <label for="JIGsearch-unit">-</label>
                                <select id="JIGsearch-unit" name="unit">
                                    <option value="">-</option>
                                </select>

                                <label for="JIGsearch-title">-</label>
                                <input type="text" id="JIGsearch-title" name="title" placeholder="-">

                                <div class="button-group">
                                    <button type="submit" class="JIGsearch-btn">-</button>
                                    <button type="reset" class="JIGreset-btn">-</button>
                                </div>
                            </div>
                        </form>
                    </div> -->
                    
    
                <div class="JIGlist" style="width: 100%; height: 700px; overflow: auto;">
                    <table class="data-table">
                        <thead>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
    
                <!-- 在表格后添加分页控件 -->
                <!-- <div class="pagination">
                    <div class="pagination-info">
                        共 <span class="total-count">0</span> 条记录，
                        每页 <select class="page-size">
                            <option value="10">10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                        </select> 条
                    </div>
                    <div class="pagination-controls">
                        <button class="btn-first-page">首页</button>
                        <button class="btn-prev-page">上一页</button>
                        <span class="page-info">
                            第 <input type="number" class="current-page" min="1"> 页，
                            共 <span class="total-pages">0</span> 页
                        </span>
                        <button class="btn-next-page">下一页</button>
                        <button class="btn-last-page">末页</button>
                    </div>
                </div> -->
            </div>

            <!-- 切机管理选项卡 --> 
            <div id="MCTab" class="tab-content">
            </div>
    </div>
</body>
</html> 