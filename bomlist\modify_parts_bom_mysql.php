<?php
require_once '../php/db_config.php';
header('Content-Type: application/json');
session_start();

try {
    // 检查必填字段
    $required_fields = ['id', 'partlevel', 'saftystock', 'damagedhistory', 'recorder'];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("缺少必填字段: $field");
        }
    }

    // 获取并验证数据
    $id = intval($_POST['id']);
    $partlevel = trim($_POST['partlevel']);
    $saftystock = intval($_POST['saftystock']);
    $damagedhistory = trim($_POST['damagedhistory']);
    $recorder = trim($_POST['recorder']);

    // 准备SQL语句
    $sql = "UPDATE parts_bom 
            SET partlevel = ?, 
                saftystock = ?, 
                damagedhistory = ?, 
                recorder = ?
            WHERE id = ?";
    
    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("准备语句失败: " . $conn->error);
    }

    // 绑定参数并执行
    $stmt->bind_param('sissi', $partlevel, $saftystock, $damagedhistory, $recorder, $id);
    
    if (!$stmt->execute()) {
        throw new Exception("执行失败: " . $stmt->error);
    }

    // 检查影响行数
    if ($stmt->affected_rows === 0) {
        throw new Exception("没有修改任何数据或记录不存在");
    }

    echo json_encode([
        'success' => true,
        'message' => '修改成功'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?>